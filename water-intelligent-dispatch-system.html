<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水量智能预测调度系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f4c75 0%, #3282b8 50%, #bbe1fa 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .system-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .system-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px 35px;
            margin-bottom: 25px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .system-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(15, 76, 117, 0.1), transparent);
            animation: flow 4s infinite;
        }

        @keyframes flow {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 2.8rem;
            color: #0f4c75;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .system-status {
            display: flex;
            justify-content: center;
            gap: 30px;
            font-size: 0.95rem;
            color: #888;
            position: relative;
            z-index: 1;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-online {
            color: #4caf50;
        }

        .status-warning {
            color: #ff9800;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
            box-shadow: 0 6px 20px rgba(15, 76, 117, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(15, 76, 117, 0.1);
            color: #0f4c75;
            transform: translateY(-1px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 关键指标卡片 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 5px solid;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
        }

        .kpi-card.prediction { border-left-color: #2196f3; }
        .kpi-card.monitoring { border-left-color: #4caf50; }
        .kpi-card.dispatch { border-left-color: #ff9800; }
        .kpi-card.analysis { border-left-color: #9c27b0; }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .kpi-title {
            font-size: 1.1rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .kpi-trend {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .kpi-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #0f4c75;
            margin-bottom: 10px;
        }

        .kpi-unit {
            font-size: 1rem;
            color: #888;
            margin-left: 5px;
        }

        .kpi-comparison {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: #666;
        }

        /* 图表容器 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #0f4c75;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: white;
            color: #666;
            font-size: 0.85rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-btn.active {
            background: #0f4c75;
            color: white;
            border-color: #0f4c75;
        }

        .chart-container {
            position: relative;
            height: 350px;
        }

        /* 液位监控 */
        .level-monitor {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        .level-gauge {
            background: linear-gradient(180deg, #e3f2fd 0%, #bbdefb 100%);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .gauge-container {
            position: relative;
            width: 200px;
            height: 300px;
            margin: 0 auto;
            background: linear-gradient(180deg, #f5f5f5 0%, #e0e0e0 100%);
            border-radius: 10px;
            border: 3px solid #0f4c75;
            overflow: hidden;
        }

        .water-level {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            background: linear-gradient(180deg, #2196f3 0%, #1976d2 100%);
            transition: height 1s ease;
            border-radius: 0 0 7px 7px;
        }

        .level-markers {
            position: absolute;
            right: -30px;
            top: 0;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            font-size: 0.8rem;
            color: #666;
        }

        .level-value {
            font-size: 2rem;
            font-weight: bold;
            color: #0f4c75;
            margin-top: 20px;
        }

        /* 调度指令 */
        .dispatch-commands {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .command-item {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #0f4c75;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .command-content {
            flex: 1;
        }

        .command-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .command-detail {
            font-size: 0.9rem;
            color: #666;
        }

        .command-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending { background: #fff3cd; color: #856404; }
        .status-executing { background: #cce5ff; color: #004085; }
        .status-completed { background: #d4edda; color: #155724; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
            
            .level-monitor {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .system-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.2rem;
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                padding: 15px 20px;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .system-status {
                flex-direction: column;
                gap: 10px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 预警样式 */
        .alert {
            padding: 15px 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 500;
        }

        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }

        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #0f4c75, #3282b8);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(15, 76, 117, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="system-container">
        <!-- 系统头部 -->
        <div class="system-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tint"></i>
                水量智能预测调度系统
                <i class="fas fa-brain"></i>
            </h1>
            <p class="subtitle">Water Intelligent Prediction & Dispatch System</p>
            <div class="system-status">
                <div class="status-item status-online">
                    <i class="fas fa-circle"></i>
                    <span>预测模型：运行正常</span>
                </div>
                <div class="status-item status-online">
                    <i class="fas fa-circle"></i>
                    <span>监控系统：在线</span>
                </div>
                <div class="status-item status-warning">
                    <i class="fas fa-circle"></i>
                    <span>调度状态：执行中</span>
                </div>
                <div class="status-item status-online">
                    <i class="fas fa-sync-alt"></i>
                    <span>更新时间：14:30:25</span>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="prediction">
                <i class="fas fa-chart-line"></i>
                水量智能预测
            </button>
            <button class="nav-tab" data-tab="monitoring">
                <i class="fas fa-eye"></i>
                清水池监控
            </button>
            <button class="nav-tab" data-tab="dispatch">
                <i class="fas fa-cogs"></i>
                调度策略管理
            </button>
            <button class="nav-tab" data-tab="analysis">
                <i class="fas fa-chart-bar"></i>
                调度分析
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 水量智能预测标签页 -->
            <div id="prediction" class="tab-content active">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-chart-line"></i>
                    水量智能预测
                </h2>

                <!-- 预测指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card prediction">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-arrow-right"></i>
                                进厂水量预测
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.3%
                            </div>
                        </div>
                        <div class="kpi-value">
                            42,580
                            <span class="kpi-unit">m³/日</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>预测偏差：±3.2%</span>
                            <span>置信度：95.8%</span>
                        </div>
                    </div>

                    <div class="kpi-card prediction">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-arrow-left"></i>
                                出厂水量预测
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +0.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            39,650
                            <span class="kpi-unit">m³/日</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>预测偏差：±2.8%</span>
                            <span>置信度：97.2%</span>
                        </div>
                    </div>

                    <div class="kpi-card prediction">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-balance-scale"></i>
                                水量平衡度
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            93.1
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标值：95%</span>
                            <span>历史均值：92.5%</span>
                        </div>
                    </div>

                    <div class="kpi-card prediction">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bullseye"></i>
                                预测精度
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            96.4
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>昨日：95.9%</span>
                            <span>本月均值：96.1%</span>
                        </div>
                    </div>
                </div>

                <!-- 预测图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                水量预测趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchPredictionPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchPredictionPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchPredictionPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="predictionTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                影响因素分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="factorAnalysisChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 预测模型信息 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-brain"></i>
                            预测模型状态
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #1565c0; margin-bottom: 8px;">
                                <i class="fas fa-database"></i> 训练数据量
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #0d47a1;">2,847</div>
                            <div style="font-size: 0.8rem; color: #1976d2;">天</div>
                        </div>
                        
                        <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #2e7d32; margin-bottom: 8px;">
                                <i class="fas fa-sync-alt"></i> 模型更新频率
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #1b5e20;">每日</div>
                            <div style="font-size: 0.8rem; color: #388e3c;">自动更新</div>
                        </div>
                        
                        <div style="background: #fff3e0; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #ef6c00; margin-bottom: 8px;">
                                <i class="fas fa-cog"></i> 算法类型
                            </div>
                            <div style="font-size: 1.2rem; font-weight: bold; color: #e65100;">LSTM+RF</div>
                            <div style="font-size: 0.8rem; color: #f57c00;">混合模型</div>
                        </div>
                        
                        <div style="background: #fce4ec; padding: 20px; border-radius: 10px; text-align: center;">
                            <div style="font-size: 0.9rem; color: #c2185b; margin-bottom: 8px;">
                                <i class="fas fa-clock"></i> 预测周期
                            </div>
                            <div style="font-size: 1.8rem; font-weight: bold; color: #ad1457;">72</div>
                            <div style="font-size: 0.8rem; color: #e91e63;">小时</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 清水池监控标签页 -->
            <div id="monitoring" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-eye"></i>
                    清水池监控
                </h2>

                <!-- 监控预警 -->
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div>
                        <strong>液位预警：</strong>2号清水池液位接近上限阈值，当前液位4.8m，上限阈值5.0m
                        <div style="margin-top: 5px; font-size: 0.9rem;">建议调整出厂水泵运行参数或减少进厂水量</div>
                    </div>
                </div>

                <!-- 液位监控 -->
                <div class="level-monitor">
                    <div class="level-gauge">
                        <h3 style="color: #0f4c75; margin-bottom: 20px;">
                            <i class="fas fa-tint"></i>
                            1号清水池
                        </h3>
                        <div class="gauge-container">
                            <div class="water-level" id="level1" style="height: 65%;"></div>
                            <div class="level-markers">
                                <span>5.0m</span>
                                <span>4.0m</span>
                                <span>3.0m</span>
                                <span>2.0m</span>
                                <span>1.0m</span>
                                <span>0.0m</span>
                            </div>
                        </div>
                        <div class="level-value">3.25m</div>
                        <div style="color: #4caf50; font-size: 0.9rem;">
                            <i class="fas fa-check-circle"></i> 正常范围
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                液位变化趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchLevelPeriod('1h')">1小时</button>
                                <button class="chart-btn" onclick="switchLevelPeriod('6h')">6小时</button>
                                <button class="chart-btn" onclick="switchLevelPeriod('24h')">24小时</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="levelTrendChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 多池监控 -->
                <div class="kpi-grid">
                    <div class="kpi-card monitoring">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                1号清水池
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="kpi-value">
                            3.25
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>容量：65%</span>
                            <span>状态：稳定</span>
                        </div>
                    </div>

                    <div class="kpi-card monitoring">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                2号清水池
                            </div>
                            <div class="kpi-trend trend-up" style="background: #ffebee; color: #c62828;">
                                <i class="fas fa-exclamation-triangle"></i>
                                偏高
                            </div>
                        </div>
                        <div class="kpi-value">
                            4.80
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>容量：96%</span>
                            <span>状态：接近上限</span>
                        </div>
                    </div>

                    <div class="kpi-card monitoring">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                3号清水池
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                下降
                            </div>
                        </div>
                        <div class="kpi-value">
                            2.15
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>容量：43%</span>
                            <span>状态：正常</span>
                        </div>
                    </div>

                    <div class="kpi-card monitoring">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-calculator"></i>
                                总储水量
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +0.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            15,240
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>总容量：68%</span>
                            <span>安全余量：32%</span>
                        </div>
                    </div>
                </div>

                <!-- 监控参数设置 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-sliders-h"></i>
                            监控参数设置
                        </h3>
                        <button class="chart-btn" onclick="saveThresholds()">
                            <i class="fas fa-save"></i>
                            保存设置
                        </button>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">液位阈值设置</h4>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">上限阈值 (m)</label>
                                <input type="number" value="5.0" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">下限阈值 (m)</label>
                                <input type="number" value="1.0" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">波动阈值 (m/h)</label>
                                <input type="number" value="0.5" step="0.1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">预警设置</h4>
                            <div style="margin-bottom: 15px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked>
                                    <span>声光报警</span>
                                </label>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked>
                                    <span>短信通知</span>
                                </label>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked>
                                    <span>邮件通知</span>
                                </label>
                            </div>
                            <div>
                                <label style="display: block; margin-bottom: 5px; font-weight: 600;">通知延迟 (分钟)</label>
                                <input type="number" value="5" min="1" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">历史记录</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; font-size: 0.9rem;">
                                <div style="margin-bottom: 8px;">
                                    <strong>14:25</strong> - 2号池液位达到4.8m，触发预警
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>13:45</strong> - 1号池液位恢复正常范围
                                </div>
                                <div style="margin-bottom: 8px;">
                                    <strong>12:30</strong> - 3号池液位下降至2.1m
                                </div>
                                <div>
                                    <strong>11:15</strong> - 系统自动调整完成
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 传感器状态 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-satellite-dish"></i>
                            传感器状态监控
                        </h3>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>传感器编号</th>
                                <th>安装位置</th>
                                <th>传感器类型</th>
                                <th>当前读数</th>
                                <th>最后更新</th>
                                <th>通信状态</th>
                                <th>电池电量</th>
                                <th>运行状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>LS-001</td>
                                <td>1号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">3.25 m</td>
                                <td>14:30:15</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td class="number">85%</td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>LS-002</td>
                                <td>2号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">4.80 m</td>
                                <td>14:30:12</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td class="number">92%</td>
                                <td><span style="color: #ff9800;">预警</span></td>
                            </tr>
                            <tr>
                                <td>LS-003</td>
                                <td>3号清水池</td>
                                <td>超声波液位计</td>
                                <td class="number">2.15 m</td>
                                <td>14:30:18</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td class="number">78%</td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>PS-001</td>
                                <td>进水管道</td>
                                <td>压力传感器</td>
                                <td class="number">0.45 MPa</td>
                                <td>14:30:10</td>
                                <td><span style="color: #4caf50;">在线</span></td>
                                <td class="number">88%</td>
                                <td><span style="color: #4caf50;">正常</span></td>
                            </tr>
                            <tr>
                                <td>FS-001</td>
                                <td>出水管道</td>
                                <td>流量传感器</td>
                                <td class="number">1,652 m³/h</td>
                                <td>14:30:08</td>
                                <td><span style="color: #f44336;">离线</span></td>
                                <td class="number">15%</td>
                                <td><span style="color: #f44336;">故障</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 调度策略管理标签页 -->
            <div id="dispatch" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-cogs"></i>
                    调度策略管理
                </h2>

                <!-- 当前调度状态 -->
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>调度执行中：</strong>基于预测结果和液位监控，系统正在执行自动调度策略
                        <div style="margin-top: 5px; font-size: 0.9rem;">预计15分钟内完成调度调整，请关注执行结果</div>
                    </div>
                </div>

                <!-- 调度指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card dispatch">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-pump-soap"></i>
                                进厂水泵频率
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -5Hz
                            </div>
                        </div>
                        <div class="kpi-value">
                            45
                            <span class="kpi-unit">Hz</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：45Hz</span>
                            <span>范围：35-50Hz</span>
                        </div>
                    </div>

                    <div class="kpi-card dispatch">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-industry"></i>
                                制水车间负荷
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            85
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：85%</span>
                            <span>最大：95%</span>
                        </div>
                    </div>

                    <div class="kpi-card dispatch">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-pump-medical"></i>
                                出厂水泵台数
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1台
                            </div>
                        </div>
                        <div class="kpi-value">
                            3
                            <span class="kpi-unit">台</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>运行：3台</span>
                            <span>备用：1台</span>
                        </div>
                    </div>

                    <div class="kpi-card dispatch">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tachometer-alt"></i>
                                管网压力
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.42
                            <span class="kpi-unit">MPa</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：0.4-0.5</span>
                            <span>状态：正常</span>
                        </div>
                    </div>
                </div>

                <!-- 调度指令 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-list-alt"></i>
                            当前调度指令
                        </h3>
                        <button class="chart-btn" onclick="generateNewDispatch()">
                            <i class="fas fa-sync-alt"></i>
                            重新生成
                        </button>
                    </div>
                    <div class="dispatch-commands">
                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">调整2号出厂水泵运行参数</div>
                                <div class="command-detail">频率从40Hz调整至45Hz，预计增加出水量200m³/h</div>
                            </div>
                            <div class="command-status status-executing">执行中</div>
                        </div>

                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">启动4号出厂水泵</div>
                                <div class="command-detail">为应对2号池液位过高，启动备用水泵增加出水能力</div>
                            </div>
                            <div class="command-status status-pending">待执行</div>
                        </div>

                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">降低进厂水泵频率</div>
                                <div class="command-detail">1号进厂水泵频率从50Hz降至45Hz，减少进水量</div>
                            </div>
                            <div class="command-status status-completed">已完成</div>
                        </div>

                        <div class="command-item">
                            <div class="command-content">
                                <div class="command-title">调整制水车间生产负荷</div>
                                <div class="command-detail">生产负荷从90%调整至85%，优化水量平衡</div>
                            </div>
                            <div class="command-status status-completed">已完成</div>
                        </div>
                    </div>
                </div>

                <!-- 调度策略配置 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-cog"></i>
                                调度策略配置
                            </h3>
                        </div>
                        <div style="padding: 20px 0;">
                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">调度模式</label>
                                <select style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                    <option value="auto" selected>自动调度</option>
                                    <option value="manual">手动调度</option>
                                    <option value="hybrid">混合模式</option>
                                </select>
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">调度频率 (分钟)</label>
                                <input type="number" value="15" min="5" max="60" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                            </div>

                            <div style="margin-bottom: 20px;">
                                <label style="display: block; margin-bottom: 8px; font-weight: 600;">液位目标范围</label>
                                <div style="display: flex; gap: 10px;">
                                    <input type="number" value="2.5" step="0.1" placeholder="最低" style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                    <input type="number" value="4.0" step="0.1" placeholder="最高" style="flex: 1; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                                </div>
                            </div>

                            <div>
                                <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                    <input type="checkbox" checked>
                                    <span>启用预测性调度</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 10px;">
                                    <input type="checkbox" checked>
                                    <span>启用应急调度</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox">
                                    <span>节能优化模式</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                调度效果预测
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="dispatchEffectChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面开始加载...');

            // 等待Chart.js加载
            function waitForChart() {
                if (typeof Chart !== 'undefined') {
                    console.log('Chart.js已加载，版本:', Chart.version);
                    initTabEvents();
                    initializePredictionCharts();
                    startRealTimeUpdate();
                    animateCounters();
                } else {
                    console.log('等待Chart.js加载...');
                    setTimeout(waitForChart, 100);
                }
            }

            waitForChart();
        });

        // 显示备用图表（纯CSS实现）
        function showFallbackCharts() {
            console.log('使用备用图表显示');
            const chartContainers = document.querySelectorAll('.chart-container');
            chartContainers.forEach(container => {
                container.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; border-radius: 10px; color: #666;">
                        <div style="text-align: center;">
                            <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 15px; color: #0f4c75;"></i>
                            <div>图表数据加载中...</div>
                            <div style="font-size: 0.9rem; margin-top: 5px;">请检查网络连接</div>
                        </div>
                    </div>
                `;
            });
        }

        // 初始化标签页事件监听
        function initTabEvents() {
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    showTab(tabId, this);
                });
            });
        }

        // 标签页切换功能
        function showTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);

            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('标签页内容已显示:', tabId);
            } else {
                console.error('未找到标签页内容:', tabId);
            }

            // 激活选中的标签
            if (clickedTab) {
                clickedTab.classList.add('active');
            }

            // 根据标签页加载相应内容
            setTimeout(() => {
                if (tabId === 'monitoring') {
                    loadMonitoringTab();
                } else if (tabId === 'dispatch') {
                    loadDispatchTab();
                } else if (tabId === 'analysis') {
                    loadAnalysisTab();
                }
            }, 200);
        }

        // 初始化预测图表
        function initializePredictionCharts() {
            initPredictionTrendChart();
            initFactorAnalysisChart();
        }

        // 预测趋势图
        function initPredictionTrendChart() {
            const ctx = document.getElementById('predictionTrendChart');
            if (!ctx) {
                console.error('预测趋势图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                        datasets: [{
                            label: '进厂水量预测',
                            data: [1650, 1420, 1890, 2150, 2380, 2100, 1750],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '出厂水量预测',
                            data: [1580, 1350, 1720, 1980, 2200, 1950, 1650],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '实际进厂水量',
                            data: [1680, 1400, 1910, 2120, 2350, 2080, 1720],
                            borderColor: '#ff9800',
                            backgroundColor: 'transparent',
                            borderDash: [5, 5],
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '水量 (m³/h)'
                                }
                            }
                        }
                    }
                });
                console.log('预测趋势图初始化成功');
            } catch (error) {
                console.error('预测趋势图初始化失败:', error);
            }
        }

        // 影响因素分析图
        function initFactorAnalysisChart() {
            const ctx = document.getElementById('factorAnalysisChart');
            if (!ctx) {
                console.error('影响因素分析图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['历史规律', '季节因素', '节假日', '天气', '政策影响', '其他'],
                        datasets: [{
                            data: [35, 25, 15, 12, 8, 5],
                            backgroundColor: [
                                '#0f4c75',
                                '#3282b8',
                                '#bbe1fa',
                                '#0e4b99',
                                '#2e8bc0',
                                '#b1ceea'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('影响因素分析图初始化成功');
            } catch (error) {
                console.error('影响因素分析图初始化失败:', error);
            }
        }

        // 切换预测周期
        function switchPredictionPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以更新图表数据
            console.log('切换到' + period + '预测视图');
        }

        // 数值动画
        function animateCounters() {
            const counters = document.querySelectorAll('.kpi-value');
            counters.forEach(counter => {
                const target = parseFloat(counter.textContent.replace(/,/g, ''));
                const increment = target / 100;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    
                    const unit = counter.querySelector('.kpi-unit');
                    const unitText = unit ? unit.textContent : '';
                    
                    if (target < 100) {
                        counter.innerHTML = current.toFixed(1) + 
                            (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                    } else {
                        counter.innerHTML = Math.round(current).toLocaleString() + 
                            (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                    }
                }, 20);
            });
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新时间
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.querySelector('.system-status .status-item:last-child span').textContent = `更新时间：${timeStr}`;
                
                // 模拟数据微调
                updatePredictionData();
            }, 30000); // 每30秒更新一次
        }

        // 更新预测数据
        function updatePredictionData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const current = parseFloat(value.textContent.replace(/,/g, ''));
                const variation = (Math.random() - 0.5) * 0.02; // ±1%的变化
                const newValue = current * (1 + variation);
                
                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';
                
                if (current < 100) {
                    value.innerHTML = newValue.toFixed(1) + 
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newValue).toLocaleString() + 
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });
        }

            <!-- 调度分析标签页 -->
            <div id="analysis" class="tab-content">
                <h2 style="color: #0f4c75; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-chart-bar"></i>
                    调度分析
                </h2>

                <!-- 分析指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bullseye"></i>
                                调度成功率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.1%
                            </div>
                        </div>
                        <div class="kpi-value">
                            94.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>本月：94.8%</span>
                            <span>上月：92.7%</span>
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2min
                            </div>
                        </div>
                        <div class="kpi-value">
                            8.5
                            <span class="kpi-unit">分钟</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：<10分钟</span>
                            <span>最快：3.2分钟</span>
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-leaf"></i>
                                节能效果
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            12.3
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>节约电费：2,850元</span>
                            <span>本月累计：8,420元</span>
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-chart-line"></i>
                                预测精度提升
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            96.4
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>初始：89.2%</span>
                            <span>改进：+7.2%</span>
                        </div>
                    </div>
                </div>

                <!-- 分析图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                预测vs实际对比
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchAnalysisPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchAnalysisPeriod('30d')">30天</button>
                                <button class="chart-btn" onclick="switchAnalysisPeriod('90d')">90天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="predictionAccuracyChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                调度类型分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="dispatchTypeChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 详细分析数据 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-table"></i>
                            调度数据统计分析
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn" onclick="exportAnalysisData()">
                                <i class="fas fa-download"></i>
                                导出报告
                            </button>
                        </div>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>调度次数</th>
                                <th>成功次数</th>
                                <th>成功率</th>
                                <th>平均响应时间</th>
                                <th>液位稳定度</th>
                                <th>能耗节约</th>
                                <th>预测偏差</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15</td>
                                <td class="number">24</td>
                                <td class="number">23</td>
                                <td class="number">95.8%</td>
                                <td class="number">7.2分钟</td>
                                <td class="number">92.5%</td>
                                <td class="number">8.3%</td>
                                <td class="number">±2.1%</td>
                            </tr>
                            <tr>
                                <td>2024-01-14</td>
                                <td class="number">28</td>
                                <td class="number">26</td>
                                <td class="number">92.9%</td>
                                <td class="number">9.1分钟</td>
                                <td class="number">89.2%</td>
                                <td class="number">6.7%</td>
                                <td class="number">±3.5%</td>
                            </tr>
                            <tr>
                                <td>2024-01-13</td>
                                <td class="number">22</td>
                                <td class="number">21</td>
                                <td class="number">95.5%</td>
                                <td class="number">8.8分钟</td>
                                <td class="number">94.1%</td>
                                <td class="number">9.2%</td>
                                <td class="number">±2.8%</td>
                            </tr>
                            <tr>
                                <td>2024-01-12</td>
                                <td class="number">26</td>
                                <td class="number">24</td>
                                <td class="number">92.3%</td>
                                <td class="number">10.5分钟</td>
                                <td class="number">87.6%</td>
                                <td class="number">5.4%</td>
                                <td class="number">±4.2%</td>
                            </tr>
                            <tr>
                                <td>2024-01-11</td>
                                <td class="number">30</td>
                                <td class="number">29</td>
                                <td class="number">96.7%</td>
                                <td class="number">6.9分钟</td>
                                <td class="number">95.8%</td>
                                <td class="number">11.1%</td>
                                <td class="number">±1.9%</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 模型优化建议 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-lightbulb"></i>
                            模型优化建议
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 25px;">
                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">预测模型优化</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #2e7d32; margin-bottom: 8px;">
                                    <i class="fas fa-check-circle"></i> 建议采纳
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    增加天气数据权重，特别是降雨量对用水需求的影响，预计可提升预测精度2-3%
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #ff9800; margin-bottom: 8px;">
                                    <i class="fas fa-exclamation-triangle"></i> 待评估
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    考虑引入社会活动数据（如大型活动、停水通知等）作为预测因子
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <div style="font-weight: 600; color: #2196f3; margin-bottom: 8px;">
                                    <i class="fas fa-info-circle"></i> 长期规划
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    建立多模型集成预测，结合LSTM、ARIMA和随机森林算法
                                </div>
                            </div>
                        </div>

                        <div>
                            <h4 style="color: #0f4c75; margin-bottom: 15px;">调度策略优化</h4>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #2e7d32; margin-bottom: 8px;">
                                    <i class="fas fa-check-circle"></i> 已实施
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    优化水泵启停策略，减少频繁启停，延长设备寿命，节能效果提升15%
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                                <div style="font-weight: 600; color: #ff9800; margin-bottom: 8px;">
                                    <i class="fas fa-cog"></i> 调整中
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    引入分时电价策略，在低电价时段适当提高储水量，降低运行成本
                                </div>
                            </div>
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                <div style="font-weight: 600; color: #9c27b0; margin-bottom: 8px;">
                                    <i class="fas fa-star"></i> 创新方案
                                </div>
                                <div style="font-size: 0.9rem; line-height: 1.5;">
                                    研发基于强化学习的自适应调度算法，实现更智能的决策优化
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>


        // 切换预测周期
        function switchPredictionPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            // 这里可以更新图表数据
            console.log('切换到' + period + '预测视图');
        }

        // 数值动画
        function animateCounters() {
            const counters = document.querySelectorAll('.kpi-value');
            counters.forEach(counter => {
                const target = parseFloat(counter.textContent.replace(/,/g, ''));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }

                    const unit = counter.querySelector('.kpi-unit');
                    const unitText = unit ? unit.textContent : '';

                    if (target < 100) {
                        counter.innerHTML = current.toFixed(1) +
                            (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                    } else {
                        counter.innerHTML = Math.round(current).toLocaleString() +
                            (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                    }
                }, 20);
            });
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                // 更新时间
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.querySelector('.system-status .status-item:last-child span').textContent = `更新时间：${timeStr}`;

                // 模拟数据微调
                updatePredictionData();
                updateLevelData();
            }, 30000); // 每30秒更新一次
        }

        // 更新预测数据
        function updatePredictionData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const current = parseFloat(value.textContent.replace(/,/g, ''));
                const variation = (Math.random() - 0.5) * 0.02; // ±1%的变化
                const newValue = current * (1 + variation);

                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';

                if (current < 100) {
                    value.innerHTML = newValue.toFixed(1) +
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newValue).toLocaleString() +
                        (unit ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });
        }

        // 更新液位数据
        function updateLevelData() {
            const level1 = document.getElementById('level1');
            if (level1) {
                const currentHeight = parseFloat(level1.style.height);
                const variation = (Math.random() - 0.5) * 4; // ±2%的变化
                const newHeight = Math.max(20, Math.min(95, currentHeight + variation));
                level1.style.height = newHeight + '%';
            }
        }

        // 加载监控标签页
        function loadMonitoringTab() {
            setTimeout(() => {
                initLevelTrendChart();
            }, 100);
        }

        // 初始化液位趋势图
        function initLevelTrendChart() {
            const ctx = document.getElementById('levelTrendChart');
            if (!ctx) {
                console.error('液位趋势图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['14:00', '14:10', '14:20', '14:30', '14:40', '14:50', '15:00'],
                        datasets: [{
                            label: '1号池液位',
                            data: [3.2, 3.25, 3.3, 3.25, 3.2, 3.22, 3.25],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '2号池液位',
                            data: [4.6, 4.65, 4.7, 4.75, 4.8, 4.82, 4.8],
                            borderColor: '#f44336',
                            backgroundColor: 'rgba(244, 67, 54, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '3号池液位',
                            data: [2.3, 2.25, 2.2, 2.18, 2.15, 2.12, 2.15],
                            borderColor: '#4caf50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 6,
                                title: {
                                    display: true,
                                    text: '液位 (m)'
                                }
                            }
                        }
                    }
                });
                console.log('液位趋势图初始化成功');
            } catch (error) {
                console.error('液位趋势图初始化失败:', error);
            }
        }

        // 切换液位监控周期
        function switchLevelPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            console.log('切换到' + period + '液位视图');
        }

        // 保存阈值设置
        function saveThresholds() {
            showNotification('监控参数设置已保存！', 'success');
        }

        // 加载调度标签页
        function loadDispatchTab() {
            setTimeout(() => {
                initDispatchEffectChart();
            }, 100);
        }

        // 初始化调度效果图
        function initDispatchEffectChart() {
            const ctx = document.getElementById('dispatchEffectChart');
            if (!ctx) {
                console.error('调度效果图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['当前', '+15min', '+30min', '+45min', '+60min'],
                        datasets: [{
                            label: '预测液位',
                            data: [3.25, 3.15, 3.05, 2.95, 2.85],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '目标液位',
                            data: [3.0, 3.0, 3.0, 3.0, 3.0],
                            borderColor: '#4caf50',
                            backgroundColor: 'transparent',
                            borderDash: [5, 5]
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                max: 5,
                                title: {
                                    display: true,
                                    text: '液位 (m)'
                                }
                            }
                        }
                    }
                });
                console.log('调度效果图初始化成功');
            } catch (error) {
                console.error('调度效果图初始化失败:', error);
            }
        }

        // 生成新调度
        function generateNewDispatch() {
            showNotification('正在生成新的调度策略...', 'info');
            setTimeout(() => {
                showNotification('新调度策略已生成！', 'success');
            }, 2000);
        }

        // 加载分析标签页
        function loadAnalysisTab() {
            setTimeout(() => {
                initPredictionAccuracyChart();
                initDispatchTypeChart();
            }, 100);
        }

        // 初始化预测精度图
        function initPredictionAccuracyChart() {
            const ctx = document.getElementById('predictionAccuracyChart');
            if (!ctx) {
                console.error('预测精度图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: ['1/9', '1/10', '1/11', '1/12', '1/13', '1/14', '1/15'],
                        datasets: [{
                            label: '预测值',
                            data: [41500, 42200, 41800, 42500, 42100, 41900, 42580],
                            borderColor: '#2196f3',
                            backgroundColor: 'rgba(33, 150, 243, 0.1)',
                            tension: 0.4
                        }, {
                            label: '实际值',
                            data: [41680, 42150, 41750, 42480, 42180, 41850, 42520],
                            borderColor: '#ff9800',
                            backgroundColor: 'rgba(255, 152, 0, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'top'
                            }
                        },
                        scales: {
                            y: {
                                title: {
                                    display: true,
                                    text: '水量 (m³/日)'
                                }
                            }
                        }
                    }
                });
                console.log('预测精度图初始化成功');
            } catch (error) {
                console.error('预测精度图初始化失败:', error);
            }
        }

        // 初始化调度类型图
        function initDispatchTypeChart() {
            const ctx = document.getElementById('dispatchTypeChart');
            if (!ctx) {
                console.error('调度类型图canvas元素未找到');
                return;
            }

            // 如果图表已存在，先销毁
            const existingChart = Chart.getChart(ctx);
            if (existingChart) {
                existingChart.destroy();
            }

            try {
                new Chart(ctx, {
                    type: 'doughnut',
                    data: {
                        labels: ['预测性调度', '响应性调度', '应急调度', '节能调度'],
                        datasets: [{
                            data: [45, 30, 15, 10],
                            backgroundColor: [
                                '#0f4c75',
                                '#3282b8',
                                '#ff9800',
                                '#4caf50'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
                console.log('调度类型图初始化成功');
            } catch (error) {
                console.error('调度类型图初始化失败:', error);
            }
        }

        // 切换分析周期
        function switchAnalysisPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            console.log('切换到' + period + '分析视图');
        }

        // 导出分析数据
        function exportAnalysisData() {
            showNotification('正在生成分析报告...', 'info');
            setTimeout(() => {
                showNotification('分析报告导出成功！', 'success');
            }, 2000);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 300px;
            `;

            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
    </script>
</body>
</html>
