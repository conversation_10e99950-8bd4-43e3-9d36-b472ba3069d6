<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水务安全管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow-x: hidden;
        }

        /* 顶部导航栏 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 0 20px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-weight: bold;
            color: #2a5298;
        }

        .logo i {
            font-size: 2.5rem;
            background: linear-gradient(45deg, #1e3c72, #2a5298);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-menu {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .nav-menu a:hover,
        .nav-menu a.active {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(45deg, #2a5298, #1e3c72);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.8rem;
            color: #666;
        }

        /* 主要内容区域 */
        .main-container {
            display: flex;
            min-height: calc(100vh - 70px);
        }

        /* 侧边栏 */
        .sidebar {
            width: 280px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid rgba(0, 0, 0, 0.1);
            padding: 20px 0;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.1);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 15px 25px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            font-size: 1rem;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            border-left-color: #2a5298;
        }

        .sidebar-menu i {
            width: 24px;
            text-align: center;
            font-size: 1.2rem;
        }

        /* 内容区域 */
        .content-area {
            flex: 1;
            padding: 30px;
            background: #f8fafc;
            overflow-y: auto;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2rem;
            color: #2a5298;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 25px;
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .card-title {
            font-size: 1.3rem;
            color: #2a5298;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-actions {
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #2a5298, #1e3c72);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(42, 82, 152, 0.4);
        }

        .btn-secondary {
            background: rgba(42, 82, 152, 0.1);
            color: #2a5298;
            border: 1px solid rgba(42, 82, 152, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(42, 82, 152, 0.15);
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-danger:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
        }

        .stat-card.primary { border-left-color: #2a5298; }
        .stat-card.success { border-left-color: #27ae60; }
        .stat-card.warning { border-left-color: #f39c12; }
        .stat-card.danger { border-left-color: #e74c3c; }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-size: 1.8rem;
            color: white;
        }

        .stat-card.primary .stat-icon { background: linear-gradient(135deg, #2a5298, #1e3c72); }
        .stat-card.success .stat-icon { background: linear-gradient(135deg, #27ae60, #2ecc71); }
        .stat-card.warning .stat-icon { background: linear-gradient(135deg, #f39c12, #e67e22); }
        .stat-card.danger .stat-icon { background: linear-gradient(135deg, #e74c3c, #c0392b); }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 1rem;
            margin-bottom: 10px;
        }

        .stat-change {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .stat-change.positive { color: #27ae60; }
        .stat-change.negative { color: #e74c3c; }

        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .table {
            width: 100%;
            border-collapse: collapse;
        }

        .table th,
        .table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f1f5f9;
        }

        .table th {
            background: #f8fafc;
            font-weight: 600;
            color: #2a5298;
        }

        .table tr:hover {
            background: #f8fafc;
        }

        /* 状态标签 */
        .status-badge {
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-badge.approved { background: #d4edda; color: #155724; }
        .status-badge.pending { background: #fff3cd; color: #856404; }
        .status-badge.rejected { background: #f8d7da; color: #721c24; }
        .status-badge.normal { background: #d1ecf1; color: #0c5460; }
        .status-badge.warning { background: #fff3cd; color: #856404; }
        .status-badge.danger { background: #f8d7da; color: #721c24; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 250px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: auto;
                order: 2;
            }

            .content-area {
                order: 1;
                padding: 20px;
            }

            .nav-menu {
                display: none;
            }

            .stats-grid {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .header {
                padding: 0 15px;
            }

            .logo {
                font-size: 1.2rem;
            }

            .content-area {
                padding: 15px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes slideInRight {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 增强的交互效果 */
        .btn:active {
            transform: translateY(1px);
        }

        .card:hover .card-title {
            color: #1e3c72;
        }

        .table tr:hover {
            background: rgba(42, 82, 152, 0.05);
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(42, 82, 152, 0.3);
            border-radius: 50%;
            border-top-color: #2a5298;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: #333;
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.online {
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        .status-indicator.offline {
            background: #e74c3c;
        }

        .status-indicator.warning {
            background: #f39c12;
            animation: pulse 2s infinite;
        }

        /* 进度条 */
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e6ed;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2a5298, #1e3c72);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        /* 移动端优化 */
        @media (max-width: 480px) {
            .page-title {
                font-size: 1.3rem;
            }

            .card {
                padding: 15px;
                margin-bottom: 15px;
            }

            .btn {
                padding: 8px 15px;
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 10px 8px;
                font-size: 0.8rem;
            }

            .stat-card {
                padding: 15px;
            }

            .stat-number {
                font-size: 2rem;
            }

            .modal-content {
                margin: 10px;
                width: calc(100% - 20px);
            }
        }

        /* 打印样式 */
        @media print {
            .sidebar,
            .header,
            .btn,
            .card-actions {
                display: none !important;
            }

            .main-container {
                display: block;
            }

            .content-area {
                padding: 0;
            }

            .card {
                box-shadow: none;
                border: 1px solid #ddd;
                break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo">
            <i class="fas fa-tint"></i>
            <span>水务安全管理系统</span>
        </div>

        <nav>
            <ul class="nav-menu">
                <li><a href="#" class="active" onclick="showDashboard()"><i class="fas fa-tachometer-alt"></i>总览</a></li>
                <li><a href="#" onclick="showModule('special-work')"><i class="fas fa-hard-hat"></i>特种作业</a></li>
                <li><a href="#" onclick="showModule('safety-hazards')"><i class="fas fa-exclamation-triangle"></i>安全隐患</a></li>
                <li><a href="#" onclick="showModule('emergency-equipment')"><i class="fas fa-first-aid"></i>应急设备</a></li>
                <li><a href="#" onclick="showModule('safety-materials')"><i class="fas fa-folder"></i>安全资料</a></li>
            </ul>
        </nav>

        <div class="user-info">
            <div class="user-details">
                <div class="user-name">安全管理员</div>
                <div class="user-role">系统管理员</div>
            </div>
            <div class="user-avatar">A</div>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 侧边栏 -->
        <aside class="sidebar slide-in">
            <ul class="sidebar-menu">
                <li><a href="#" class="active" onclick="showDashboard()"><i class="fas fa-tachometer-alt"></i>系统总览</a></li>
                <li><a href="#" onclick="showModule('special-work')"><i class="fas fa-hard-hat"></i>特种作业管理</a></li>
                <li><a href="#" onclick="showModule('safety-hazards')"><i class="fas fa-exclamation-triangle"></i>安全隐患管理</a></li>
                <li><a href="#" onclick="showModule('emergency-equipment')"><i class="fas fa-first-aid"></i>应急设备管理</a></li>
                <li><a href="#" onclick="showModule('safety-materials')"><i class="fas fa-folder"></i>安全资料管理</a></li>
                <li><a href="#" onclick="showModule('reports')"><i class="fas fa-chart-bar"></i>报表统计</a></li>
                <li><a href="#" onclick="showModule('settings')"><i class="fas fa-cog"></i>系统设置</a></li>
            </ul>
        </aside>

        <!-- 内容区域 -->
        <main class="content-area">
            <div id="content-container">
                <!-- 内容将通过JavaScript动态加载 -->
            </div>
        </main>
    </div>

    <script>
        // 全局变量
        let currentModule = 'dashboard';

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            showDashboard();
        });

        // 显示仪表板
        function showDashboard() {
            currentModule = 'dashboard';
            updateActiveMenu('dashboard');
            
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-tachometer-alt"></i>
                        系统总览
                    </h1>
                    <p class="page-subtitle">水务安全管理系统运行状态和关键指标</p>
                </div>

                <!-- 统计卡片 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-hard-hat"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">特种作业人员</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +12 本月新增
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">待处理隐患</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-up"></i> +5 本周新增
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-first-aid"></i>
                        </div>
                        <div class="stat-number">89</div>
                        <div class="stat-label">应急设备</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 100% 完好率
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-times"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">即将到期证书</div>
                        <div class="stat-change negative">
                            <i class="fas fa-clock"></i> 30天内到期
                        </div>
                    </div>
                </div>

                <!-- 快速操作 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-rocket"></i>
                            快速操作
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <button class="btn btn-primary" onclick="showModule('special-work')">
                            <i class="fas fa-plus"></i> 新增特种作业审批
                        </button>
                        <button class="btn btn-secondary" onclick="showModule('safety-hazards')">
                            <i class="fas fa-map-marker-alt"></i> 标记安全隐患
                        </button>
                        <button class="btn btn-secondary" onclick="showModule('emergency-equipment')">
                            <i class="fas fa-tools"></i> 设备维护记录
                        </button>
                        <button class="btn btn-secondary" onclick="showModule('reports')">
                            <i class="fas fa-download"></i> 导出安全报表
                        </button>
                    </div>
                </div>

                <!-- 最近活动 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i>
                            最近活动
                        </h3>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>操作类型</th>
                                    <th>操作内容</th>
                                    <th>操作人员</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-15 14:30</td>
                                    <td>特种作业审批</td>
                                    <td>高空作业许可证申请</td>
                                    <td>张三</td>
                                    <td><span class="status-badge pending">待审批</span></td>
                                </tr>
                                <tr>
                                    <td>2024-01-15 13:45</td>
                                    <td>安全隐患</td>
                                    <td>水泵房电线老化隐患上报</td>
                                    <td>李四</td>
                                    <td><span class="status-badge approved">已处理</span></td>
                                </tr>
                                <tr>
                                    <td>2024-01-15 11:20</td>
                                    <td>设备维护</td>
                                    <td>应急发电机月度检查</td>
                                    <td>王五</td>
                                    <td><span class="status-badge approved">已完成</span></td>
                                </tr>
                                <tr>
                                    <td>2024-01-15 09:15</td>
                                    <td>资料更新</td>
                                    <td>安全操作规程修订</td>
                                    <td>赵六</td>
                                    <td><span class="status-badge approved">已发布</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;
        }

        // 显示模块
        function showModule(module) {
            currentModule = module;
            updateActiveMenu(module);
            
            // 根据模块显示不同内容
            switch(module) {
                case 'special-work':
                    showSpecialWorkModule();
                    break;
                case 'safety-hazards':
                    showSafetyHazardsModule();
                    break;
                case 'emergency-equipment':
                    showEmergencyEquipmentModule();
                    break;
                case 'safety-materials':
                    showSafetyMaterialsModule();
                    break;
                case 'reports':
                    showReportsModule();
                    break;
                default:
                    showDefaultModule(module);
            }
        }

        // 更新活动菜单
        function updateActiveMenu(module) {
            // 移除所有活动状态
            document.querySelectorAll('.nav-menu a, .sidebar-menu a').forEach(link => {
                link.classList.remove('active');
            });
            
            // 添加活动状态
            if (module === 'dashboard') {
                document.querySelector('.nav-menu a[onclick*="showDashboard"]').classList.add('active');
                document.querySelector('.sidebar-menu a[onclick*="showDashboard"]').classList.add('active');
            } else {
                document.querySelector(`.nav-menu a[onclick*="${module}"]`)?.classList.add('active');
                document.querySelector(`.sidebar-menu a[onclick*="${module}"]`)?.classList.add('active');
            }
        }

        // 特种作业管理模块
        function showSpecialWorkModule() {
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-hard-hat"></i>
                        特种作业管理
                    </h1>
                    <p class="page-subtitle">特种作业人员信息管理和作业审批流程</p>
                </div>

                <!-- 操作按钮 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i>
                            操作面板
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showAddPersonModal()">
                                <i class="fas fa-user-plus"></i> 新增人员
                            </button>
                            <button class="btn btn-primary" onclick="showAddApprovalModal()">
                                <i class="fas fa-plus"></i> 新增审批
                            </button>
                            <button class="btn btn-secondary" onclick="exportPersonnelData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">特种作业人员总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +12 本月新增
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-certificate"></i>
                        </div>
                        <div class="stat-number">142</div>
                        <div class="stat-label">有效证书数量</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 91% 持证率
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">即将到期证书</div>
                        <div class="stat-change negative">
                            <i class="fas fa-exclamation-triangle"></i> 30天内
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">5</div>
                        <div class="stat-label">待审批申请</div>
                        <div class="stat-change">
                            <i class="fas fa-hourglass-half"></i> 待处理
                        </div>
                    </div>
                </div>

                <!-- 人员列表 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            特种作业人员列表
                        </h3>
                        <div class="card-actions">
                            <input type="text" placeholder="搜索人员..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部工种</option>
                                <option value="electrician">电工</option>
                                <option value="welder">焊工</option>
                                <option value="height">高空作业</option>
                                <option value="confined">受限空间</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>姓名</th>
                                    <th>工号</th>
                                    <th>工种类型</th>
                                    <th>证书编号</th>
                                    <th>发证日期</th>
                                    <th>到期日期</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>张三</td>
                                    <td>WS001</td>
                                    <td>电工</td>
                                    <td>DG20240001</td>
                                    <td>2024-01-15</td>
                                    <td>2027-01-15</td>
                                    <td><span class="status-badge approved">有效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewPersonDetail('WS001')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="editPerson('WS001')">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>李四</td>
                                    <td>WS002</td>
                                    <td>焊工</td>
                                    <td>HG20240002</td>
                                    <td>2023-06-20</td>
                                    <td>2024-06-20</td>
                                    <td><span class="status-badge warning">即将到期</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewPersonDetail('WS002')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="editPerson('WS002')">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>王五</td>
                                    <td>WS003</td>
                                    <td>高空作业</td>
                                    <td>GK20240003</td>
                                    <td>2024-02-10</td>
                                    <td>2027-02-10</td>
                                    <td><span class="status-badge approved">有效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewPersonDetail('WS003')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="editPerson('WS003')">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>赵六</td>
                                    <td>WS004</td>
                                    <td>受限空间</td>
                                    <td>SX20240004</td>
                                    <td>2023-12-01</td>
                                    <td>2024-12-01</td>
                                    <td><span class="status-badge warning">即将到期</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewPersonDetail('WS004')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="editPerson('WS004')">
                                            <i class="fas fa-edit"></i> 编辑
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 审批流程 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clipboard-check"></i>
                            审批流程管理
                        </h3>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>申请编号</th>
                                    <th>申请人</th>
                                    <th>作业类型</th>
                                    <th>作业地点</th>
                                    <th>计划时间</th>
                                    <th>申请时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>SP20240001</td>
                                    <td>张三</td>
                                    <td>高空作业</td>
                                    <td>1号水泵房</td>
                                    <td>2024-01-16 09:00</td>
                                    <td>2024-01-15 14:30</td>
                                    <td><span class="status-badge pending">待审批</span></td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="approveApplication('SP20240001')">
                                            <i class="fas fa-check"></i> 审批
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SP20240002</td>
                                    <td>李四</td>
                                    <td>电气作业</td>
                                    <td>配电室</td>
                                    <td>2024-01-16 14:00</td>
                                    <td>2024-01-15 16:20</td>
                                    <td><span class="status-badge pending">待审批</span></td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="approveApplication('SP20240002')">
                                            <i class="fas fa-check"></i> 审批
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>SP20240003</td>
                                    <td>王五</td>
                                    <td>焊接作业</td>
                                    <td>管道维修区</td>
                                    <td>2024-01-15 10:00</td>
                                    <td>2024-01-14 15:45</td>
                                    <td><span class="status-badge approved">已批准</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewApprovalDetail('SP20240003')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;
        }

        function showSafetyHazardsModule() {
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        安全隐患管理
                    </h1>
                    <p class="page-subtitle">安全隐患点标记管理和一张图可视化展示</p>
                </div>

                <!-- 操作按钮 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i>
                            操作面板
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showAddHazardModal()">
                                <i class="fas fa-plus"></i> 新增隐患
                            </button>
                            <button class="btn btn-secondary" onclick="toggleMapView()">
                                <i class="fas fa-map"></i> 切换地图视图
                            </button>
                            <button class="btn btn-secondary" onclick="exportHazardData()">
                                <i class="fas fa-download"></i> 导出数据
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">23</div>
                        <div class="stat-label">待处理隐患</div>
                        <div class="stat-change negative">
                            <i class="fas fa-arrow-up"></i> +5 本周新增
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">处理中隐患</div>
                        <div class="stat-change">
                            <i class="fas fa-hourglass-half"></i> 平均3天
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">156</div>
                        <div class="stat-label">已处理隐患</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 本月完成
                        </div>
                    </div>

                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-number">87%</div>
                        <div class="stat-label">处理完成率</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +5% 环比
                        </div>
                    </div>
                </div>

                <!-- 安全隐患一张图 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-map-marked-alt"></i>
                            安全隐患一张图
                        </h3>
                        <div class="card-actions">
                            <select id="hazardFilter" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                                <option value="all">全部隐患</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                            <button class="btn btn-secondary" onclick="refreshMap()">
                                <i class="fas fa-sync"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div id="hazardMap" style="height: 400px; border-radius: 10px; overflow: hidden; background: #f8fafc; display: flex; align-items: center; justify-content: center; color: #666;">
                        <div style="text-align: center;">
                            <i class="fas fa-map" style="font-size: 3rem; margin-bottom: 15px; color: #2a5298;"></i>
                            <h3>安全隐患分布地图</h3>
                            <p>显示水务设施安全隐患点分布情况</p>
                            <button class="btn btn-primary" onclick="initializeMap()" style="margin-top: 15px;">
                                <i class="fas fa-play"></i> 加载地图
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 隐患列表 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            安全隐患列表
                        </h3>
                        <div class="card-actions">
                            <input type="text" placeholder="搜索隐患..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部状态</option>
                                <option value="pending">待处理</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>隐患编号</th>
                                    <th>隐患描述</th>
                                    <th>发现地点</th>
                                    <th>风险等级</th>
                                    <th>发现时间</th>
                                    <th>责任人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>YH20240001</td>
                                    <td>水泵房电线老化</td>
                                    <td>1号水泵房</td>
                                    <td><span class="status-badge danger">高风险</span></td>
                                    <td>2024-01-15 09:30</td>
                                    <td>张三</td>
                                    <td><span class="status-badge pending">待处理</span></td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="processHazard('YH20240001')">
                                            <i class="fas fa-wrench"></i> 处理
                                        </button>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="viewHazardDetail('YH20240001')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>YH20240002</td>
                                    <td>管道接头渗漏</td>
                                    <td>主管道区域</td>
                                    <td><span class="status-badge warning">中风险</span></td>
                                    <td>2024-01-14 14:20</td>
                                    <td>李四</td>
                                    <td><span class="status-badge warning">处理中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewHazardDetail('YH20240002')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="updateHazardStatus('YH20240002')">
                                            <i class="fas fa-edit"></i> 更新
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>YH20240003</td>
                                    <td>安全标识缺失</td>
                                    <td>配电室入口</td>
                                    <td><span class="status-badge normal">低风险</span></td>
                                    <td>2024-01-13 16:45</td>
                                    <td>王五</td>
                                    <td><span class="status-badge approved">已完成</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewHazardDetail('YH20240003')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>YH20240004</td>
                                    <td>防护栏杆松动</td>
                                    <td>沉淀池周围</td>
                                    <td><span class="status-badge warning">中风险</span></td>
                                    <td>2024-01-12 11:15</td>
                                    <td>赵六</td>
                                    <td><span class="status-badge pending">待处理</span></td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="processHazard('YH20240004')">
                                            <i class="fas fa-wrench"></i> 处理
                                        </button>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="viewHazardDetail('YH20240004')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;
        }

        function showEmergencyEquipmentModule() {
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-first-aid"></i>
                        应急设备管理
                    </h1>
                    <p class="page-subtitle">应急设备台账管理、维护记录和状态监控</p>
                </div>

                <!-- 操作按钮 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i>
                            操作面板
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showAddEquipmentModal()">
                                <i class="fas fa-plus"></i> 新增设备
                            </button>
                            <button class="btn btn-secondary" onclick="showMaintenanceModal()">
                                <i class="fas fa-wrench"></i> 维护记录
                            </button>
                            <button class="btn btn-secondary" onclick="exportEquipmentData()">
                                <i class="fas fa-download"></i> 导出台账
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-first-aid"></i>
                        </div>
                        <div class="stat-number">89</div>
                        <div class="stat-label">应急设备总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 100% 完好率
                        </div>
                    </div>

                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="stat-number">89</div>
                        <div class="stat-label">正常设备</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> 状态良好
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">12</div>
                        <div class="stat-label">即将维护</div>
                        <div class="stat-change">
                            <i class="fas fa-calendar"></i> 7天内
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="stat-number">0</div>
                        <div class="stat-label">故障设备</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 无故障
                        </div>
                    </div>
                </div>

                <!-- 设备分类统计 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-pie"></i>
                            设备分类统计
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="text-align: center; padding: 20px; background: #f8fafc; border-radius: 10px;">
                            <i class="fas fa-fire-extinguisher" style="font-size: 2.5rem; color: #e74c3c; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0; color: #333;">消防设备</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #e74c3c; margin: 5px 0;">32台</p>
                            <small style="color: #666;">灭火器、消防栓等</small>
                        </div>
                        <div style="text-align: center; padding: 20px; background: #f8fafc; border-radius: 10px;">
                            <i class="fas fa-bolt" style="font-size: 2.5rem; color: #f39c12; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0; color: #333;">电气设备</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #f39c12; margin: 5px 0;">18台</p>
                            <small style="color: #666;">应急发电机、UPS等</small>
                        </div>
                        <div style="text-align: center; padding: 20px; background: #f8fafc; border-radius: 10px;">
                            <i class="fas fa-hard-hat" style="font-size: 2.5rem; color: #2a5298; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0; color: #333;">防护设备</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #2a5298; margin: 5px 0;">25套</p>
                            <small style="color: #666;">防护服、呼吸器等</small>
                        </div>
                        <div style="text-align: center; padding: 20px; background: #f8fafc; border-radius: 10px;">
                            <i class="fas fa-medkit" style="font-size: 2.5rem; color: #27ae60; margin-bottom: 10px;"></i>
                            <h4 style="margin: 0; color: #333;">医疗设备</h4>
                            <p style="font-size: 1.5rem; font-weight: bold; color: #27ae60; margin: 5px 0;">14套</p>
                            <small style="color: #666;">急救箱、担架等</small>
                        </div>
                    </div>
                </div>

                <!-- 设备列表 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            应急设备台账
                        </h3>
                        <div class="card-actions">
                            <input type="text" placeholder="搜索设备..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部类型</option>
                                <option value="fire">消防设备</option>
                                <option value="electrical">电气设备</option>
                                <option value="protection">防护设备</option>
                                <option value="medical">医疗设备</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>设备编号</th>
                                    <th>设备名称</th>
                                    <th>设备类型</th>
                                    <th>存放位置</th>
                                    <th>购置日期</th>
                                    <th>下次维护</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>EQ20240001</td>
                                    <td>干粉灭火器</td>
                                    <td>消防设备</td>
                                    <td>1号水泵房</td>
                                    <td>2023-03-15</td>
                                    <td>2024-03-15</td>
                                    <td><span class="status-badge approved">正常</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewEquipmentDetail('EQ20240001')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="maintainEquipment('EQ20240001')">
                                            <i class="fas fa-wrench"></i> 维护
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EQ20240002</td>
                                    <td>应急发电机</td>
                                    <td>电气设备</td>
                                    <td>配电室</td>
                                    <td>2022-08-20</td>
                                    <td>2024-02-20</td>
                                    <td><span class="status-badge warning">即将维护</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewEquipmentDetail('EQ20240002')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="maintainEquipment('EQ20240002')">
                                            <i class="fas fa-wrench"></i> 维护
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EQ20240003</td>
                                    <td>防毒面具</td>
                                    <td>防护设备</td>
                                    <td>安全器材室</td>
                                    <td>2023-06-10</td>
                                    <td>2024-06-10</td>
                                    <td><span class="status-badge approved">正常</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewEquipmentDetail('EQ20240003')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="maintainEquipment('EQ20240003')">
                                            <i class="fas fa-wrench"></i> 维护
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EQ20240004</td>
                                    <td>急救箱</td>
                                    <td>医疗设备</td>
                                    <td>办公楼大厅</td>
                                    <td>2023-01-25</td>
                                    <td>2024-07-25</td>
                                    <td><span class="status-badge approved">正常</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewEquipmentDetail('EQ20240004')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="maintainEquipment('EQ20240004')">
                                            <i class="fas fa-wrench"></i> 维护
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 维护计划 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar-alt"></i>
                            维护计划
                        </h3>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>设备编号</th>
                                    <th>设备名称</th>
                                    <th>维护类型</th>
                                    <th>计划时间</th>
                                    <th>负责人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>EQ20240002</td>
                                    <td>应急发电机</td>
                                    <td>月度检查</td>
                                    <td>2024-01-20</td>
                                    <td>张三</td>
                                    <td><span class="status-badge pending">待执行</span></td>
                                    <td>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="executeMaintenance('EQ20240002')">
                                            <i class="fas fa-play"></i> 执行
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>EQ20240001</td>
                                    <td>干粉灭火器</td>
                                    <td>年度检测</td>
                                    <td>2024-03-15</td>
                                    <td>李四</td>
                                    <td><span class="status-badge warning">计划中</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewMaintenancePlan('EQ20240001')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;
        }

        function showSafetyMaterialsModule() {
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-folder"></i>
                        安全资料管理
                    </h1>
                    <p class="page-subtitle">安全文档分类管理、版本控制和在线查看</p>
                </div>

                <!-- 操作按钮 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i>
                            操作面板
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="showUploadModal()">
                                <i class="fas fa-upload"></i> 上传文档
                            </button>
                            <button class="btn btn-secondary" onclick="showCreateFolderModal()">
                                <i class="fas fa-folder-plus"></i> 新建文件夹
                            </button>
                            <button class="btn btn-secondary" onclick="exportMaterialsList()">
                                <i class="fas fa-download"></i> 导出清单
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div class="stats-grid fade-in">
                    <div class="stat-card primary">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">248</div>
                        <div class="stat-label">安全文档总数</div>
                        <div class="stat-change positive">
                            <i class="fas fa-arrow-up"></i> +15 本月新增
                        </div>
                    </div>

                    <div class="stat-card success">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-number">235</div>
                        <div class="stat-label">有效文档</div>
                        <div class="stat-change positive">
                            <i class="fas fa-check"></i> 94.8% 有效率
                        </div>
                    </div>

                    <div class="stat-card warning">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-number">13</div>
                        <div class="stat-label">即将过期</div>
                        <div class="stat-change">
                            <i class="fas fa-calendar"></i> 30天内
                        </div>
                    </div>

                    <div class="stat-card danger">
                        <div class="stat-icon">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div class="stat-number">8</div>
                        <div class="stat-label">待更新文档</div>
                        <div class="stat-change negative">
                            <i class="fas fa-exclamation-triangle"></i> 需更新
                        </div>
                    </div>
                </div>

                <!-- 文档分类 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-sitemap"></i>
                            文档分类
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                        <div class="folder-item" onclick="openFolder('regulations')" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-gavel" style="font-size: 2rem; color: #e74c3c; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">法规制度</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">安全法规、管理制度</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">45个文档</span>
                                <span style="color: #27ae60; font-size: 0.8rem;"><i class="fas fa-check-circle"></i> 最新</span>
                            </div>
                        </div>

                        <div class="folder-item" onclick="openFolder('procedures')" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-clipboard-list" style="font-size: 2rem; color: #2a5298; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">操作规程</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">安全操作规程、作业指导书</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">68个文档</span>
                                <span style="color: #f39c12; font-size: 0.8rem;"><i class="fas fa-clock"></i> 待更新</span>
                            </div>
                        </div>

                        <div class="folder-item" onclick="openFolder('emergency')" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #f39c12; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">应急预案</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">应急预案、处置方案</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">32个文档</span>
                                <span style="color: #27ae60; font-size: 0.8rem;"><i class="fas fa-check-circle"></i> 最新</span>
                            </div>
                        </div>

                        <div class="folder-item" onclick="openFolder('training')" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-graduation-cap" style="font-size: 2rem; color: #27ae60; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">培训资料</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">安全培训、教育材料</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">56个文档</span>
                                <span style="color: #27ae60; font-size: 0.8rem;"><i class="fas fa-check-circle"></i> 最新</span>
                            </div>
                        </div>

                        <div class="folder-item" onclick="openFolder('records')" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-archive" style="font-size: 2rem; color: #9b59b6; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">记录档案</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">检查记录、事故档案</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">47个文档</span>
                                <span style="color: #27ae60; font-size: 0.8rem;"><i class="fas fa-check-circle"></i> 最新</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 最近文档 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-clock"></i>
                            最近文档
                        </h3>
                        <div class="card-actions">
                            <input type="text" placeholder="搜索文档..." style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部类型</option>
                                <option value="pdf">PDF文档</option>
                                <option value="word">Word文档</option>
                                <option value="excel">Excel表格</option>
                                <option value="ppt">PPT演示</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>文档名称</th>
                                    <th>文档类型</th>
                                    <th>所属分类</th>
                                    <th>版本</th>
                                    <th>更新时间</th>
                                    <th>更新人</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-file-pdf" style="color: #e74c3c;"></i>
                                            <span>水务安全管理制度</span>
                                        </div>
                                    </td>
                                    <td>PDF</td>
                                    <td>法规制度</td>
                                    <td>v2.1</td>
                                    <td>2024-01-15 14:30</td>
                                    <td>张三</td>
                                    <td><span class="status-badge approved">有效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewDocument('DOC001')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="downloadDocument('DOC001')">
                                            <i class="fas fa-download"></i> 下载
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-file-word" style="color: #2a5298;"></i>
                                            <span>特种作业安全操作规程</span>
                                        </div>
                                    </td>
                                    <td>Word</td>
                                    <td>操作规程</td>
                                    <td>v1.5</td>
                                    <td>2024-01-14 09:20</td>
                                    <td>李四</td>
                                    <td><span class="status-badge warning">待更新</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewDocument('DOC002')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="updateDocument('DOC002')">
                                            <i class="fas fa-edit"></i> 更新
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-file-powerpoint" style="color: #d04423;"></i>
                                            <span>应急救援培训课件</span>
                                        </div>
                                    </td>
                                    <td>PPT</td>
                                    <td>培训资料</td>
                                    <td>v3.0</td>
                                    <td>2024-01-13 16:45</td>
                                    <td>王五</td>
                                    <td><span class="status-badge approved">有效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewDocument('DOC003')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="downloadDocument('DOC003')">
                                            <i class="fas fa-download"></i> 下载
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center; gap: 10px;">
                                            <i class="fas fa-file-excel" style="color: #27ae60;"></i>
                                            <span>安全检查记录表</span>
                                        </div>
                                    </td>
                                    <td>Excel</td>
                                    <td>记录档案</td>
                                    <td>v1.2</td>
                                    <td>2024-01-12 11:15</td>
                                    <td>赵六</td>
                                    <td><span class="status-badge approved">有效</span></td>
                                    <td>
                                        <button class="btn btn-secondary" style="padding: 5px 10px; font-size: 0.8rem;" onclick="viewDocument('DOC004')">
                                            <i class="fas fa-eye"></i> 查看
                                        </button>
                                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-left: 5px;" onclick="downloadDocument('DOC004')">
                                            <i class="fas fa-download"></i> 下载
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;

            // 添加文件夹悬停效果
            setTimeout(() => {
                const folderItems = document.querySelectorAll('.folder-item');
                folderItems.forEach(item => {
                    item.addEventListener('mouseenter', function() {
                        this.style.borderColor = '#2a5298';
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 15px rgba(42, 82, 152, 0.2)';
                    });

                    item.addEventListener('mouseleave', function() {
                        this.style.borderColor = 'transparent';
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = 'none';
                    });
                });
            }, 100);
        }

        function showReportsModule() {
            const content = `
                <div class="page-header fade-in">
                    <h1 class="page-title">
                        <i class="fas fa-chart-bar"></i>
                        报表统计
                    </h1>
                    <p class="page-subtitle">安全管理数据统计分析和报表生成</p>
                </div>

                <!-- 操作按钮 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-tools"></i>
                            报表操作
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-chart-line"></i> 生成报表
                            </button>
                            <button class="btn btn-secondary" onclick="exportAllReports()">
                                <i class="fas fa-download"></i> 导出报表
                            </button>
                            <button class="btn btn-secondary" onclick="scheduleReport()">
                                <i class="fas fa-clock"></i> 定时报表
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 报表类型选择 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            报表类型
                        </h3>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                        <div class="report-type-card" onclick="showSpecialWorkReport()" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-hard-hat" style="font-size: 2rem; color: #2a5298; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">特种作业报表</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">人员统计、证书管理、审批流程</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">156条记录</span>
                                <span style="color: #2a5298; font-size: 0.8rem;"><i class="fas fa-arrow-right"></i></span>
                            </div>
                        </div>

                        <div class="report-type-card" onclick="showHazardReport()" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; color: #f39c12; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">安全隐患报表</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">隐患统计、处理进度、风险分析</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">191条记录</span>
                                <span style="color: #2a5298; font-size: 0.8rem;"><i class="fas fa-arrow-right"></i></span>
                            </div>
                        </div>

                        <div class="report-type-card" onclick="showEquipmentReport()" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-first-aid" style="font-size: 2rem; color: #27ae60; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">应急设备报表</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">设备台账、维护记录、状态统计</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">89条记录</span>
                                <span style="color: #2a5298; font-size: 0.8rem;"><i class="fas fa-arrow-right"></i></span>
                            </div>
                        </div>

                        <div class="report-type-card" onclick="showMaterialReport()" style="padding: 20px; background: #f8fafc; border-radius: 10px; cursor: pointer; transition: all 0.3s ease; border: 2px solid transparent;">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <i class="fas fa-folder" style="font-size: 2rem; color: #9b59b6; margin-right: 15px;"></i>
                                <div>
                                    <h4 style="margin: 0; color: #333;">安全资料报表</h4>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">文档统计、版本管理、更新记录</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="color: #666; font-size: 0.9rem;">248条记录</span>
                                <span style="color: #2a5298; font-size: 0.8rem;"><i class="fas fa-arrow-right"></i></span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 综合统计图表 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-area"></i>
                            综合统计分析
                        </h3>
                        <div class="card-actions">
                            <select id="chartPeriod" style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px; margin-right: 10px;">
                                <option value="week">本周</option>
                                <option value="month" selected>本月</option>
                                <option value="quarter">本季度</option>
                                <option value="year">本年</option>
                            </select>
                            <button class="btn btn-secondary" onclick="refreshCharts()">
                                <i class="fas fa-sync"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
                        <div>
                            <h4 style="margin-bottom: 15px; color: #333;">安全事件趋势分析</h4>
                            <canvas id="trendChart" width="400" height="200"></canvas>
                        </div>
                        <div>
                            <h4 style="margin-bottom: 15px; color: #333;">风险等级分布</h4>
                            <canvas id="riskChart" width="200" height="200"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 数据汇总表 -->
                <div class="card fade-in">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-table"></i>
                            数据汇总
                        </h3>
                        <div class="card-actions">
                            <button class="btn btn-primary" onclick="exportSummaryData()">
                                <i class="fas fa-file-excel"></i> 导出Excel
                            </button>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>统计项目</th>
                                    <th>本周</th>
                                    <th>本月</th>
                                    <th>本季度</th>
                                    <th>本年</th>
                                    <th>环比变化</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><i class="fas fa-hard-hat" style="color: #2a5298; margin-right: 8px;"></i>特种作业审批</td>
                                    <td>12</td>
                                    <td>45</td>
                                    <td>128</td>
                                    <td>456</td>
                                    <td><span class="stat-change positive">+8.5%</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-exclamation-triangle" style="color: #f39c12; margin-right: 8px;"></i>安全隐患发现</td>
                                    <td>8</td>
                                    <td>23</td>
                                    <td>67</td>
                                    <td>191</td>
                                    <td><span class="stat-change negative">+12.3%</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-check-circle" style="color: #27ae60; margin-right: 8px;"></i>隐患处理完成</td>
                                    <td>15</td>
                                    <td>38</td>
                                    <td>89</td>
                                    <td>234</td>
                                    <td><span class="stat-change positive">+15.2%</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-wrench" style="color: #9b59b6; margin-right: 8px;"></i>设备维护次数</td>
                                    <td>25</td>
                                    <td>89</td>
                                    <td>267</td>
                                    <td>892</td>
                                    <td><span class="stat-change positive">+5.8%</span></td>
                                </tr>
                                <tr>
                                    <td><i class="fas fa-file-alt" style="color: #e74c3c; margin-right: 8px;"></i>文档更新次数</td>
                                    <td>6</td>
                                    <td>18</td>
                                    <td>45</td>
                                    <td>156</td>
                                    <td><span class="stat-change positive">****%</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;

            document.getElementById('content-container').innerHTML = content;

            // 添加报表卡片悬停效果
            setTimeout(() => {
                const reportCards = document.querySelectorAll('.report-type-card');
                reportCards.forEach(card => {
                    card.addEventListener('mouseenter', function() {
                        this.style.borderColor = '#2a5298';
                        this.style.transform = 'translateY(-2px)';
                        this.style.boxShadow = '0 4px 15px rgba(42, 82, 152, 0.2)';
                    });

                    card.addEventListener('mouseleave', function() {
                        this.style.borderColor = 'transparent';
                        this.style.transform = 'translateY(0)';
                        this.style.boxShadow = 'none';
                    });
                });

                // 初始化图表
                initializeReportCharts();
            }, 100);
        }

        function showDefaultModule(module) {
            document.getElementById('content-container').innerHTML = `<h2>${module}</h2><p>功能开发中...</p>`;
        }

        // 特种作业管理相关函数
        function showAddPersonModal() {
            showModal('新增特种作业人员', `
                <form id="addPersonForm" style="display: grid; gap: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">姓名 *</label>
                            <input type="text" name="name" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">工号 *</label>
                            <input type="text" name="workId" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">工种类型 *</label>
                            <select name="workType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择工种</option>
                                <option value="electrician">电工</option>
                                <option value="welder">焊工</option>
                                <option value="height">高空作业</option>
                                <option value="confined">受限空间</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">证书编号 *</label>
                            <input type="text" name="certNumber" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">发证日期 *</label>
                            <input type="date" name="issueDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">到期日期 *</label>
                            <input type="date" name="expireDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">联系电话</label>
                        <input type="tel" name="phone" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">备注</label>
                        <textarea name="remarks" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '保存', class: 'btn-primary', onclick: 'savePerson()' }
            ]);
        }

        function showAddApprovalModal() {
            showModal('新增特种作业审批', `
                <form id="addApprovalForm" style="display: grid; gap: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">申请人 *</label>
                            <select name="applicant" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择申请人</option>
                                <option value="WS001">张三 (WS001)</option>
                                <option value="WS002">李四 (WS002)</option>
                                <option value="WS003">王五 (WS003)</option>
                                <option value="WS004">赵六 (WS004)</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">作业类型 *</label>
                            <select name="workType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择作业类型</option>
                                <option value="height">高空作业</option>
                                <option value="electrical">电气作业</option>
                                <option value="welding">焊接作业</option>
                                <option value="confined">受限空间作业</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">作业地点 *</label>
                        <input type="text" name="location" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="请输入具体作业地点">
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">计划开始时间 *</label>
                            <input type="datetime-local" name="startTime" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">计划结束时间 *</label>
                            <input type="datetime-local" name="endTime" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">作业内容描述 *</label>
                        <textarea name="description" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="请详细描述作业内容和安全措施"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">风险评估</label>
                        <select name="riskLevel" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="low">低风险</option>
                            <option value="medium">中风险</option>
                            <option value="high">高风险</option>
                        </select>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '提交审批', class: 'btn-primary', onclick: 'submitApproval()' }
            ]);
        }

        function savePerson() {
            const form = document.getElementById('addPersonForm');
            const formData = new FormData(form);

            // 模拟保存操作
            showNotification('人员信息保存成功！', 'success');
            closeModal();

            // 实际应用中这里会发送到后端
            console.log('保存人员信息:', Object.fromEntries(formData));
        }

        function submitApproval() {
            const form = document.getElementById('addApprovalForm');
            const formData = new FormData(form);

            // 模拟提交操作
            showNotification('审批申请提交成功！', 'success');
            closeModal();

            // 实际应用中这里会发送到后端
            console.log('提交审批申请:', Object.fromEntries(formData));
        }

        function viewPersonDetail(workId) {
            showNotification(`查看人员详情: ${workId}`, 'info');
        }

        function editPerson(workId) {
            showNotification(`编辑人员信息: ${workId}`, 'info');
        }

        function approveApplication(appId) {
            if (confirm('确定要批准这个申请吗？')) {
                showNotification(`申请 ${appId} 已批准`, 'success');
            }
        }

        function viewApprovalDetail(appId) {
            showNotification(`查看审批详情: ${appId}`, 'info');
        }

        function exportPersonnelData() {
            showNotification('正在导出人员数据...', 'info');
            setTimeout(() => {
                showNotification('数据导出完成！', 'success');
            }, 2000);
        }

        // 通用模态框函数
        function showModal(title, content, buttons = []) {
            const modal = document.createElement('div');
            modal.id = 'modal';
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 10000;
                display: flex;
                align-items: center;
                justify-content: center;
                animation: fadeIn 0.3s ease-out;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                border-radius: 15px;
                width: 90%;
                max-width: 600px;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
                animation: slideIn 0.3s ease-out;
            `;

            const modalHeader = document.createElement('div');
            modalHeader.style.cssText = `
                background: linear-gradient(135deg, #2a5298, #1e3c72);
                color: white;
                padding: 20px 25px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;

            modalHeader.innerHTML = `
                <h3 style="margin: 0; font-size: 1.3rem;">${title}</h3>
                <button onclick="closeModal()" style="background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer; width: 30px; height: 30px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: background 0.3s ease;">×</button>
            `;

            const modalBody = document.createElement('div');
            modalBody.style.cssText = `
                padding: 25px;
                max-height: 60vh;
                overflow-y: auto;
            `;
            modalBody.innerHTML = content;

            const modalFooter = document.createElement('div');
            modalFooter.style.cssText = `
                padding: 20px 25px;
                border-top: 1px solid #f1f5f9;
                display: flex;
                justify-content: flex-end;
                gap: 10px;
            `;

            buttons.forEach(button => {
                const btn = document.createElement('button');
                btn.className = `btn ${button.class}`;
                btn.textContent = button.text;
                btn.onclick = new Function(button.onclick);
                modalFooter.appendChild(btn);
            });

            modalContent.appendChild(modalHeader);
            modalContent.appendChild(modalBody);
            modalContent.appendChild(modalFooter);
            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // 点击背景关闭
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });
        }

        function closeModal() {
            const modal = document.getElementById('modal');
            if (modal) {
                modal.remove();
            }
        }

        // 通知函数
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#2a5298'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10001;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 10px;
                max-width: 300px;
            `;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'slideInRight 0.3s ease-out reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 3000);
        }

        // 安全隐患管理相关函数
        function showAddHazardModal() {
            showModal('新增安全隐患', `
                <form id="addHazardForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">隐患描述 *</label>
                        <textarea name="description" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="请详细描述发现的安全隐患"></textarea>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">发现地点 *</label>
                            <input type="text" name="location" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="具体位置">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">风险等级 *</label>
                            <select name="riskLevel" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择风险等级</option>
                                <option value="high">高风险</option>
                                <option value="medium">中风险</option>
                                <option value="low">低风险</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">发现人员 *</label>
                            <input type="text" name="reporter" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">联系电话</label>
                            <input type="tel" name="phone" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">建议处理措施</label>
                        <textarea name="suggestion" rows="2" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="建议的处理方法或措施"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">现场照片</label>
                        <input type="file" name="photos" multiple accept="image/*" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        <small style="color: #666; font-size: 0.8rem;">支持多张图片上传，格式：JPG、PNG</small>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '保存', class: 'btn-primary', onclick: 'saveHazard()' }
            ]);
        }

        function saveHazard() {
            const form = document.getElementById('addHazardForm');
            const formData = new FormData(form);

            // 模拟保存操作
            showNotification('安全隐患记录保存成功！', 'success');
            closeModal();

            // 实际应用中这里会发送到后端
            console.log('保存隐患信息:', Object.fromEntries(formData));
        }

        function toggleMapView() {
            const mapContainer = document.getElementById('hazardMap');
            if (mapContainer.innerHTML.includes('加载地图')) {
                initializeMap();
            } else {
                showNotification('地图视图切换功能', 'info');
            }
        }

        function initializeMap() {
            const mapContainer = document.getElementById('hazardMap');

            // 模拟地图加载
            mapContainer.innerHTML = `
                <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%); position: relative; display: flex; align-items: center; justify-content: center;">
                    <div style="position: absolute; top: 20px; left: 20px; background: white; padding: 10px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h4 style="margin: 0 0 10px 0; color: #2a5298;">隐患分布统计</h4>
                        <div style="display: flex; gap: 15px; font-size: 0.9rem;">
                            <span style="color: #e74c3c;"><i class="fas fa-circle"></i> 高风险: 8</span>
                            <span style="color: #f39c12;"><i class="fas fa-circle"></i> 中风险: 12</span>
                            <span style="color: #27ae60;"><i class="fas fa-circle"></i> 低风险: 3</span>
                        </div>
                    </div>

                    <!-- 模拟隐患点标记 -->
                    <div style="position: absolute; top: 30%; left: 25%; width: 20px; height: 20px; background: #e74c3c; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8rem; cursor: pointer; animation: pulse 2s infinite;" onclick="showHazardPopup('YH20240001', '水泵房电线老化')">
                        <i class="fas fa-exclamation"></i>
                    </div>

                    <div style="position: absolute; top: 50%; left: 60%; width: 20px; height: 20px; background: #f39c12; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8rem; cursor: pointer; animation: pulse 2s infinite;" onclick="showHazardPopup('YH20240002', '管道接头渗漏')">
                        <i class="fas fa-exclamation"></i>
                    </div>

                    <div style="position: absolute; top: 70%; left: 40%; width: 20px; height: 20px; background: #27ae60; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8rem; cursor: pointer;" onclick="showHazardPopup('YH20240003', '安全标识缺失')">
                        <i class="fas fa-check"></i>
                    </div>

                    <div style="position: absolute; top: 40%; left: 80%; width: 20px; height: 20px; background: #f39c12; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 0.8rem; cursor: pointer; animation: pulse 2s infinite;" onclick="showHazardPopup('YH20240004', '防护栏杆松动')">
                        <i class="fas fa-exclamation"></i>
                    </div>

                    <div style="text-align: center; color: #666;">
                        <i class="fas fa-map-marked-alt" style="font-size: 4rem; margin-bottom: 15px; color: #2a5298; opacity: 0.3;"></i>
                        <h3 style="color: #2a5298;">水务设施安全隐患分布图</h3>
                        <p>点击标记查看隐患详情</p>
                    </div>
                </div>
            `;

            showNotification('地图加载完成', 'success');
        }

        function showHazardPopup(hazardId, description) {
            showNotification(`隐患详情: ${hazardId} - ${description}`, 'info');
        }

        function refreshMap() {
            showNotification('地图数据刷新中...', 'info');
            setTimeout(() => {
                showNotification('地图数据已更新', 'success');
            }, 1500);
        }

        function processHazard(hazardId) {
            showModal('处理安全隐患', `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2a5298; margin-bottom: 10px;">隐患编号: ${hazardId}</h4>
                </div>
                <form id="processHazardForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">处理措施 *</label>
                        <textarea name="solution" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="请详细描述处理措施"></textarea>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">处理人员 *</label>
                            <input type="text" name="handler" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">预计完成时间 *</label>
                            <input type="datetime-local" name="expectedTime" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">处理状态 *</label>
                        <select name="status" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="processing">开始处理</option>
                            <option value="completed">处理完成</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">处理照片</label>
                        <input type="file" name="photos" multiple accept="image/*" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '确认处理', class: 'btn-primary', onclick: 'confirmProcessHazard("' + hazardId + '")' }
            ]);
        }

        function confirmProcessHazard(hazardId) {
            const form = document.getElementById('processHazardForm');
            const formData = new FormData(form);

            showNotification(`隐患 ${hazardId} 处理记录已保存`, 'success');
            closeModal();

            console.log('处理隐患:', hazardId, Object.fromEntries(formData));
        }

        function viewHazardDetail(hazardId) {
            showNotification(`查看隐患详情: ${hazardId}`, 'info');
        }

        function updateHazardStatus(hazardId) {
            showNotification(`更新隐患状态: ${hazardId}`, 'info');
        }

        function exportHazardData() {
            showNotification('正在导出隐患数据...', 'info');
            setTimeout(() => {
                showNotification('隐患数据导出完成！', 'success');
            }, 2000);
        }

        // 应急设备管理相关函数
        function showAddEquipmentModal() {
            showModal('新增应急设备', `
                <form id="addEquipmentForm" style="display: grid; gap: 15px;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备名称 *</label>
                            <input type="text" name="name" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备编号 *</label>
                            <input type="text" name="equipmentId" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备类型 *</label>
                            <select name="type" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择设备类型</option>
                                <option value="fire">消防设备</option>
                                <option value="electrical">电气设备</option>
                                <option value="protection">防护设备</option>
                                <option value="medical">医疗设备</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">存放位置 *</label>
                            <input type="text" name="location" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">购置日期 *</label>
                            <input type="date" name="purchaseDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护周期(月) *</label>
                            <input type="number" name="maintenanceCycle" required min="1" max="60" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">供应商</label>
                            <input type="text" name="supplier" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">联系电话</label>
                            <input type="tel" name="phone" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备规格</label>
                        <textarea name="specifications" rows="2" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="设备型号、规格参数等"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">备注</label>
                        <textarea name="remarks" rows="2" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '保存', class: 'btn-primary', onclick: 'saveEquipment()' }
            ]);
        }

        function showMaintenanceModal() {
            showModal('设备维护记录', `
                <form id="maintenanceForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">选择设备 *</label>
                        <select name="equipmentId" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="">请选择设备</option>
                            <option value="EQ20240001">EQ20240001 - 干粉灭火器</option>
                            <option value="EQ20240002">EQ20240002 - 应急发电机</option>
                            <option value="EQ20240003">EQ20240003 - 防毒面具</option>
                            <option value="EQ20240004">EQ20240004 - 急救箱</option>
                        </select>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护类型 *</label>
                            <select name="maintenanceType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择维护类型</option>
                                <option value="routine">日常检查</option>
                                <option value="monthly">月度维护</option>
                                <option value="quarterly">季度检测</option>
                                <option value="annual">年度检修</option>
                                <option value="repair">故障维修</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护人员 *</label>
                            <input type="text" name="maintainer" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护日期 *</label>
                            <input type="date" name="maintenanceDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">下次维护日期</label>
                            <input type="date" name="nextMaintenanceDate" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护内容 *</label>
                        <textarea name="content" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="详细描述维护内容和检查结果"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备状态 *</label>
                        <select name="status" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="normal">正常</option>
                            <option value="warning">需要关注</option>
                            <option value="fault">故障</option>
                            <option value="scrapped">报废</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护照片</label>
                        <input type="file" name="photos" multiple accept="image/*" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '保存记录', class: 'btn-primary', onclick: 'saveMaintenance()' }
            ]);
        }

        function saveEquipment() {
            const form = document.getElementById('addEquipmentForm');
            const formData = new FormData(form);

            showNotification('应急设备信息保存成功！', 'success');
            closeModal();

            console.log('保存设备信息:', Object.fromEntries(formData));
        }

        function saveMaintenance() {
            const form = document.getElementById('maintenanceForm');
            const formData = new FormData(form);

            showNotification('维护记录保存成功！', 'success');
            closeModal();

            console.log('保存维护记录:', Object.fromEntries(formData));
        }

        function viewEquipmentDetail(equipmentId) {
            showNotification(`查看设备详情: ${equipmentId}`, 'info');
        }

        function maintainEquipment(equipmentId) {
            showModal('设备维护', `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2a5298; margin-bottom: 10px;">设备编号: ${equipmentId}</h4>
                </div>
                <form id="quickMaintenanceForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护类型 *</label>
                        <select name="type" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="routine">日常检查</option>
                            <option value="monthly">月度维护</option>
                            <option value="repair">故障维修</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">维护内容 *</label>
                        <textarea name="content" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">设备状态 *</label>
                        <select name="status" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="normal">正常</option>
                            <option value="warning">需要关注</option>
                            <option value="fault">故障</option>
                        </select>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '完成维护', class: 'btn-primary', onclick: 'completeQuickMaintenance("' + equipmentId + '")' }
            ]);
        }

        function completeQuickMaintenance(equipmentId) {
            const form = document.getElementById('quickMaintenanceForm');
            const formData = new FormData(form);

            showNotification(`设备 ${equipmentId} 维护完成`, 'success');
            closeModal();

            console.log('快速维护:', equipmentId, Object.fromEntries(formData));
        }

        function executeMaintenance(equipmentId) {
            if (confirm('确定要执行这个维护计划吗？')) {
                showNotification(`开始执行设备 ${equipmentId} 的维护计划`, 'success');
            }
        }

        function viewMaintenancePlan(equipmentId) {
            showNotification(`查看设备 ${equipmentId} 的维护计划`, 'info');
        }

        function exportEquipmentData() {
            showNotification('正在导出设备台账...', 'info');
            setTimeout(() => {
                showNotification('设备台账导出完成！', 'success');
            }, 2000);
        }

        // 安全资料管理相关函数
        function showUploadModal() {
            showModal('上传安全文档', `
                <form id="uploadForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">选择文件 *</label>
                        <input type="file" name="file" required accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        <small style="color: #666; font-size: 0.8rem;">支持格式：PDF、Word、Excel、PowerPoint</small>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">文档名称 *</label>
                            <input type="text" name="name" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">文档分类 *</label>
                            <select name="category" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">请选择分类</option>
                                <option value="regulations">法规制度</option>
                                <option value="procedures">操作规程</option>
                                <option value="emergency">应急预案</option>
                                <option value="training">培训资料</option>
                                <option value="records">记录档案</option>
                            </select>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">版本号 *</label>
                            <input type="text" name="version" required placeholder="如: v1.0" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">有效期</label>
                            <input type="date" name="expireDate" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">文档描述</label>
                        <textarea name="description" rows="3" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="简要描述文档内容和用途"></textarea>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">关键词</label>
                        <input type="text" name="keywords" placeholder="用逗号分隔多个关键词" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '上传', class: 'btn-primary', onclick: 'uploadDocument()' }
            ]);
        }

        function showCreateFolderModal() {
            showModal('新建文件夹', `
                <form id="createFolderForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">文件夹名称 *</label>
                        <input type="text" name="folderName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">上级文件夹</label>
                        <select name="parentFolder" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="">根目录</option>
                            <option value="regulations">法规制度</option>
                            <option value="procedures">操作规程</option>
                            <option value="emergency">应急预案</option>
                            <option value="training">培训资料</option>
                            <option value="records">记录档案</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">文件夹描述</label>
                        <textarea name="description" rows="2" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;"></textarea>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '创建', class: 'btn-primary', onclick: 'createFolder()' }
            ]);
        }

        function uploadDocument() {
            const form = document.getElementById('uploadForm');
            const formData = new FormData(form);

            showNotification('文档上传中...', 'info');

            // 模拟上传进度
            setTimeout(() => {
                showNotification('文档上传成功！', 'success');
                closeModal();
            }, 2000);

            console.log('上传文档:', Object.fromEntries(formData));
        }

        function createFolder() {
            const form = document.getElementById('createFolderForm');
            const formData = new FormData(form);

            showNotification('文件夹创建成功！', 'success');
            closeModal();

            console.log('创建文件夹:', Object.fromEntries(formData));
        }

        function openFolder(folderType) {
            const folderNames = {
                'regulations': '法规制度',
                'procedures': '操作规程',
                'emergency': '应急预案',
                'training': '培训资料',
                'records': '记录档案'
            };

            showNotification(`打开文件夹: ${folderNames[folderType]}`, 'info');
        }

        function viewDocument(docId) {
            showModal('文档预览', `
                <div style="text-align: center; padding: 40px;">
                    <i class="fas fa-file-alt" style="font-size: 4rem; color: #2a5298; margin-bottom: 20px;"></i>
                    <h3 style="color: #2a5298; margin-bottom: 15px;">文档预览</h3>
                    <p style="color: #666; margin-bottom: 20px;">文档ID: ${docId}</p>
                    <p style="color: #666;">在实际应用中，这里将显示文档的在线预览内容</p>
                    <div style="margin-top: 30px;">
                        <button class="btn btn-primary" onclick="downloadDocument('${docId}')">
                            <i class="fas fa-download"></i> 下载文档
                        </button>
                    </div>
                </div>
            `, [
                { text: '关闭', class: 'btn-secondary', onclick: 'closeModal()' }
            ]);
        }

        function downloadDocument(docId) {
            showNotification(`正在下载文档: ${docId}`, 'info');
            setTimeout(() => {
                showNotification('文档下载完成！', 'success');
            }, 1500);
        }

        function updateDocument(docId) {
            showModal('更新文档', `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2a5298; margin-bottom: 10px;">文档ID: ${docId}</h4>
                </div>
                <form id="updateDocForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">新版本文件 *</label>
                        <input type="file" name="file" required accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">新版本号 *</label>
                            <input type="text" name="version" required placeholder="如: v2.0" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">更新类型 *</label>
                            <select name="updateType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="minor">小版本更新</option>
                                <option value="major">大版本更新</option>
                                <option value="patch">补丁更新</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">更新说明 *</label>
                        <textarea name="updateNotes" rows="3" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px; resize: vertical;" placeholder="描述本次更新的主要内容"></textarea>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '更新', class: 'btn-primary', onclick: 'confirmUpdateDocument("' + docId + '")' }
            ]);
        }

        function confirmUpdateDocument(docId) {
            const form = document.getElementById('updateDocForm');
            const formData = new FormData(form);

            showNotification(`文档 ${docId} 更新成功！`, 'success');
            closeModal();

            console.log('更新文档:', docId, Object.fromEntries(formData));
        }

        function exportMaterialsList() {
            showNotification('正在导出资料清单...', 'info');
            setTimeout(() => {
                showNotification('资料清单导出完成！', 'success');
            }, 2000);
        }

        // 报表统计相关函数
        function generateReport() {
            showModal('生成报表', `
                <form id="generateReportForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">报表类型 *</label>
                        <select name="reportType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="">请选择报表类型</option>
                            <option value="special-work">特种作业报表</option>
                            <option value="hazard">安全隐患报表</option>
                            <option value="equipment">应急设备报表</option>
                            <option value="material">安全资料报表</option>
                            <option value="comprehensive">综合统计报表</option>
                        </select>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">开始日期 *</label>
                            <input type="date" name="startDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">结束日期 *</label>
                            <input type="date" name="endDate" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">报表格式 *</label>
                        <select name="format" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="excel">Excel格式</option>
                            <option value="pdf">PDF格式</option>
                            <option value="word">Word格式</option>
                        </select>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">包含内容</label>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-top: 10px;">
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="includeCharts" checked>
                                <span>统计图表</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="includeDetails" checked>
                                <span>详细数据</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="includeAnalysis">
                                <span>趋势分析</span>
                            </label>
                            <label style="display: flex; align-items: center; gap: 8px;">
                                <input type="checkbox" name="includeSummary" checked>
                                <span>数据汇总</span>
                            </label>
                        </div>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '生成', class: 'btn-primary', onclick: 'confirmGenerateReport()' }
            ]);
        }

        function confirmGenerateReport() {
            const form = document.getElementById('generateReportForm');
            const formData = new FormData(form);

            showNotification('正在生成报表...', 'info');
            closeModal();

            setTimeout(() => {
                showNotification('报表生成完成！', 'success');
            }, 3000);

            console.log('生成报表:', Object.fromEntries(formData));
        }

        function exportAllReports() {
            showNotification('正在导出所有报表...', 'info');
            setTimeout(() => {
                showNotification('所有报表导出完成！', 'success');
            }, 4000);
        }

        function scheduleReport() {
            showModal('定时报表设置', `
                <form id="scheduleReportForm" style="display: grid; gap: 15px;">
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">报表名称 *</label>
                        <input type="text" name="reportName" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="如：月度安全统计报表">
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">报表类型 *</label>
                        <select name="reportType" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                            <option value="">请选择报表类型</option>
                            <option value="comprehensive">综合统计报表</option>
                            <option value="special-work">特种作业报表</option>
                            <option value="hazard">安全隐患报表</option>
                            <option value="equipment">应急设备报表</option>
                        </select>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">生成频率 *</label>
                            <select name="frequency" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="daily">每日</option>
                                <option value="weekly">每周</option>
                                <option value="monthly">每月</option>
                                <option value="quarterly">每季度</option>
                            </select>
                        </div>
                        <div>
                            <label style="display: block; margin-bottom: 5px; font-weight: 600;">生成时间 *</label>
                            <input type="time" name="scheduleTime" required style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" value="09:00">
                        </div>
                    </div>
                    <div>
                        <label style="display: block; margin-bottom: 5px; font-weight: 600;">接收邮箱</label>
                        <input type="email" name="email" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 6px;" placeholder="报表将发送到此邮箱">
                    </div>
                    <div>
                        <label style="display: flex; align-items: center; gap: 8px;">
                            <input type="checkbox" name="autoSend" checked>
                            <span>自动发送邮件</span>
                        </label>
                    </div>
                </form>
            `, [
                { text: '取消', class: 'btn-secondary', onclick: 'closeModal()' },
                { text: '设置', class: 'btn-primary', onclick: 'confirmScheduleReport()' }
            ]);
        }

        function confirmScheduleReport() {
            const form = document.getElementById('scheduleReportForm');
            const formData = new FormData(form);

            showNotification('定时报表设置成功！', 'success');
            closeModal();

            console.log('定时报表设置:', Object.fromEntries(formData));
        }

        function showSpecialWorkReport() {
            showNotification('正在加载特种作业报表...', 'info');
        }

        function showHazardReport() {
            showNotification('正在加载安全隐患报表...', 'info');
        }

        function showEquipmentReport() {
            showNotification('正在加载应急设备报表...', 'info');
        }

        function showMaterialReport() {
            showNotification('正在加载安全资料报表...', 'info');
        }

        function refreshCharts() {
            showNotification('正在刷新图表数据...', 'info');
            setTimeout(() => {
                initializeReportCharts();
                showNotification('图表数据已更新', 'success');
            }, 1500);
        }

        function exportSummaryData() {
            showNotification('正在导出汇总数据...', 'info');
            setTimeout(() => {
                showNotification('汇总数据导出完成！', 'success');
            }, 2000);
        }

        // 初始化报表图表
        function initializeReportCharts() {
            // 趋势图表
            const trendCtx = document.getElementById('trendChart');
            if (trendCtx) {
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['第1周', '第2周', '第3周', '第4周'],
                        datasets: [{
                            label: '安全隐患',
                            data: [8, 12, 6, 15],
                            borderColor: '#f39c12',
                            backgroundColor: 'rgba(243, 156, 18, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '处理完成',
                            data: [10, 8, 14, 12],
                            borderColor: '#27ae60',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '特种作业',
                            data: [5, 8, 12, 10],
                            borderColor: '#2a5298',
                            backgroundColor: 'rgba(42, 82, 152, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 风险等级分布饼图
            const riskCtx = document.getElementById('riskChart');
            if (riskCtx) {
                new Chart(riskCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['高风险', '中风险', '低风险'],
                        datasets: [{
                            data: [8, 12, 3],
                            backgroundColor: [
                                '#e74c3c',
                                '#f39c12',
                                '#27ae60'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                            }
                        }
                    }
                });
            }
        }
    </script>
</body>
</html>
