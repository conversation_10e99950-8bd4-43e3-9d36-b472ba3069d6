<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能爬虫管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .nav-tab.active {
            background: white;
            color: #007bff;
            border-bottom: 3px solid #007bff;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .preview-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .preview-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .task-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .task-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-paused {
            background: #fff3cd;
            color: #856404;
        }

        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }

        .task-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: 500;
            color: #333;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 8px 15px;
            font-size: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 智能爬虫管理平台</h1>
            <p>可视化配置 · 实时预览 · 定时任务</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('config')">爬虫配置</button>
            <button class="nav-tab" onclick="showTab('tasks')">任务管理</button>
            <button class="nav-tab" onclick="showTab('logs')">运行日志</button>
        </div>

        <!-- 爬虫配置页面 -->
        <div id="config" class="tab-content active">
            <div class="config-section">
                <div class="section-title">基础配置</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="taskName">任务名称</label>
                        <input type="text" id="taskName" class="form-control" placeholder="请输入任务名称">
                    </div>
                    <div class="form-group">
                        <label for="targetUrl">目标网页URL</label>
                        <input type="url" id="targetUrl" class="form-control" placeholder="https://example.com">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="prompt">AI提示词</label>
                    <textarea id="prompt" class="form-control" placeholder="请描述你想要提取的数据内容，例如：提取页面中所有商品的名称、价格和描述信息"></textarea>
                </div>
            </div>

            <div class="config-section">
                <div class="section-title">输出配置</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="outputFormat">输出格式</label>
                        <select id="outputFormat" class="form-control">
                            <option value="json">JSON</option>
                            <option value="csv">CSV</option>
                            <option value="excel">Excel</option>
                            <option value="markdown">Markdown</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="outputPath">输出路径</label>
                        <input type="text" id="outputPath" class="form-control" placeholder="./output/">
                    </div>
                </div>
            </div>

            <div class="config-section">
                <div class="section-title">高级设置</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="delay">请求间隔(秒)</label>
                        <input type="number" id="delay" class="form-control" value="1" min="0.1" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="maxPages">最大页数</label>
                        <input type="number" id="maxPages" class="form-control" value="10" min="1">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="headers">自定义请求头 (JSON格式)</label>
                    <textarea id="headers" class="form-control" placeholder='{"User-Agent": "Mozilla/5.0..."}'></textarea>
                </div>
            </div>

            <div class="form-group">
                <button class="btn btn-primary" onclick="previewCrawl()">
                    <span id="previewLoading" class="loading" style="display: none;"></span>
                    实时预览
                </button>
                <button class="btn btn-success" onclick="saveCrawlConfig()">保存配置</button>
            </div>

            <div class="preview-section" id="previewSection" style="display: none;">
                <div class="preview-header">
                    <h3>预览结果</h3>
                    <button class="btn btn-warning btn-sm" onclick="exportPreview()">导出预览数据</button>
                </div>
                <div class="preview-content" id="previewContent">
                    <!-- 预览内容将在这里显示 -->
                </div>
            </div>
        </div>

        <!-- 任务管理页面 -->
        <div id="tasks" class="tab-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h2>定时任务管理</h2>
                <button class="btn btn-primary" onclick="showCreateTaskModal()">+ 创建新任务</button>
            </div>

            <div id="tasksList">
                <!-- 示例任务卡片 -->
                <div class="task-card">
                    <div class="task-header">
                        <div class="task-title">电商商品信息采集</div>
                        <div class="task-status status-active">运行中</div>
                    </div>
                    <div class="task-info">
                        <div class="info-item">
                            <div class="info-label">目标网站</div>
                            <div class="info-value">https://example-shop.com</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">执行频率</div>
                            <div class="info-value">每天 09:00</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">下次执行</div>
                            <div class="info-value">2024-01-15 09:00</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">输出格式</div>
                            <div class="info-value">Excel</div>
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-primary btn-sm" onclick="runTaskNow('task1')">立即执行</button>
                        <button class="btn btn-warning btn-sm" onclick="pauseTask('task1')">暂停</button>
                        <button class="btn btn-sm" onclick="editTask('task1')">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTask('task1')">删除</button>
                    </div>
                </div>

                <div class="task-card">
                    <div class="task-header">
                        <div class="task-title">新闻资讯监控</div>
                        <div class="task-status status-paused">已暂停</div>
                    </div>
                    <div class="task-info">
                        <div class="info-item">
                            <div class="info-label">目标网站</div>
                            <div class="info-value">https://news-site.com</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">执行频率</div>
                            <div class="info-value">每小时</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">最后执行</div>
                            <div class="info-value">2024-01-14 15:30</div>
                        </div>
                        <div class="info-item">
                            <div class="info-label">输出格式</div>
                            <div class="info-value">JSON</div>
                        </div>
                    </div>
                    <div class="task-actions">
                        <button class="btn btn-success btn-sm" onclick="resumeTask('task2')">恢复</button>
                        <button class="btn btn-sm" onclick="editTask('task2')">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTask('task2')">删除</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 运行日志页面 -->
        <div id="logs" class="tab-content">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                <h2>运行日志</h2>
                <div>
                    <select id="logFilter" class="form-control" style="width: auto; display: inline-block; margin-right: 10px;">
                        <option value="all">所有日志</option>
                        <option value="success">成功</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                    </select>
                    <button class="btn btn-sm" onclick="refreshLogs()">刷新</button>
                </div>
            </div>

            <div id="logsList">
                <div class="alert alert-success">
                    <strong>2024-01-14 16:30:25</strong> - 任务 "电商商品信息采集" 执行成功，采集到 156 条数据
                </div>
                <div class="alert alert-error">
                    <strong>2024-01-14 15:45:12</strong> - 任务 "新闻资讯监控" 执行失败：网络连接超时
                </div>
                <div class="alert alert-success">
                    <strong>2024-01-14 14:20:08</strong> - 任务 "电商商品信息采集" 执行成功，采集到 142 条数据
                </div>
            </div>
        </div>
    </div>

    <!-- 创建任务模态框 -->
    <div id="createTaskModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createTaskModal')">&times;</span>
            <h2>创建定时任务</h2>

            <div class="form-group">
                <label for="scheduleTaskName">任务名称</label>
                <input type="text" id="scheduleTaskName" class="form-control">
            </div>

            <div class="form-group">
                <label for="scheduleType">执行频率</label>
                <select id="scheduleType" class="form-control" onchange="updateScheduleOptions()">
                    <option value="once">仅执行一次</option>
                    <option value="hourly">每小时</option>
                    <option value="daily">每天</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                    <option value="custom">自定义 (Cron表达式)</option>
                </select>
            </div>

            <div id="scheduleOptions" class="form-group">
                <label for="scheduleTime">执行时间</label>
                <input type="time" id="scheduleTime" class="form-control" value="09:00">
            </div>

            <div id="cronExpression" class="form-group" style="display: none;">
                <label for="cronInput">Cron表达式</label>
                <input type="text" id="cronInput" class="form-control" placeholder="0 0 9 * * ?">
                <small style="color: #6c757d;">格式：秒 分 时 日 月 周</small>
            </div>

            <div class="form-group">
                <label for="configSelect">选择爬虫配置</label>
                <select id="configSelect" class="form-control">
                    <option value="">请选择已保存的配置</option>
                    <option value="config1">电商商品配置</option>
                    <option value="config2">新闻资讯配置</option>
                </select>
            </div>

            <div style="text-align: right; margin-top: 30px;">
                <button class="btn" onclick="closeModal('createTaskModal')">取消</button>
                <button class="btn btn-primary" onclick="createScheduledTask()">创建任务</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentConfig = {};
        let savedConfigs = [];
        let tasks = [];
        let logs = [];

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签页的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 实时预览功能
        async function previewCrawl() {
            const url = document.getElementById('targetUrl').value;
            const prompt = document.getElementById('prompt').value;
            const format = document.getElementById('outputFormat').value;

            if (!url || !prompt) {
                alert('请填写目标URL和提示词');
                return;
            }

            // 显示加载状态
            document.getElementById('previewLoading').style.display = 'inline-block';
            document.getElementById('previewSection').style.display = 'block';

            // 模拟API调用
            setTimeout(() => {
                const mockData = generateMockPreviewData(format);
                document.getElementById('previewContent').innerHTML = mockData;
                document.getElementById('previewLoading').style.display = 'none';
            }, 2000);
        }

        // 生成模拟预览数据
        function generateMockPreviewData(format) {
            const sampleData = [
                { name: "iPhone 15 Pro", price: "¥8999", description: "最新款苹果手机" },
                { name: "MacBook Air M2", price: "¥9499", description: "轻薄笔记本电脑" },
                { name: "AirPods Pro", price: "¥1899", description: "无线降噪耳机" }
            ];

            switch(format) {
                case 'json':
                    return `<pre>${JSON.stringify(sampleData, null, 2)}</pre>`;
                case 'csv':
                    return `<pre>name,price,description
iPhone 15 Pro,¥8999,最新款苹果手机
MacBook Air M2,¥9499,轻薄笔记本电脑
AirPods Pro,¥1899,无线降噪耳机</pre>`;
                case 'markdown':
                    return `<pre># 商品信息

| 名称 | 价格 | 描述 |
|------|------|------|
| iPhone 15 Pro | ¥8999 | 最新款苹果手机 |
| MacBook Air M2 | ¥9499 | 轻薄笔记本电脑 |
| AirPods Pro | ¥1899 | 无线降噪耳机 |</pre>`;
                default:
                    return '<pre>预览数据将在这里显示...</pre>';
            }
        }

        // 保存爬虫配置
        function saveCrawlConfig() {
            const config = {
                id: Date.now(),
                name: document.getElementById('taskName').value || '未命名配置',
                url: document.getElementById('targetUrl').value,
                prompt: document.getElementById('prompt').value,
                format: document.getElementById('outputFormat').value,
                outputPath: document.getElementById('outputPath').value,
                delay: document.getElementById('delay').value,
                maxPages: document.getElementById('maxPages').value,
                headers: document.getElementById('headers').value,
                createdAt: new Date().toLocaleString()
            };

            savedConfigs.push(config);
            updateConfigSelect();
            alert('配置保存成功！');
        }

        // 更新配置选择下拉框
        function updateConfigSelect() {
            const select = document.getElementById('configSelect');
            select.innerHTML = '<option value="">请选择已保存的配置</option>';
            savedConfigs.forEach(config => {
                const option = document.createElement('option');
                option.value = config.id;
                option.textContent = config.name;
                select.appendChild(option);
            });
        }

        // 导出预览数据
        function exportPreview() {
            const content = document.getElementById('previewContent').textContent;
            const format = document.getElementById('outputFormat').value;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `preview_data.${format}`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 显示创建任务模态框
        function showCreateTaskModal() {
            document.getElementById('createTaskModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 更新调度选项
        function updateScheduleOptions() {
            const scheduleType = document.getElementById('scheduleType').value;
            const scheduleOptions = document.getElementById('scheduleOptions');
            const cronExpression = document.getElementById('cronExpression');

            if (scheduleType === 'custom') {
                scheduleOptions.style.display = 'none';
                cronExpression.style.display = 'block';
            } else {
                scheduleOptions.style.display = 'block';
                cronExpression.style.display = 'none';
            }
        }

        // 创建定时任务
        function createScheduledTask() {
            const taskName = document.getElementById('scheduleTaskName').value;
            const scheduleType = document.getElementById('scheduleType').value;
            const configId = document.getElementById('configSelect').value;

            if (!taskName || !configId) {
                alert('请填写任务名称并选择配置');
                return;
            }

            const task = {
                id: Date.now(),
                name: taskName,
                scheduleType: scheduleType,
                configId: configId,
                status: 'active',
                createdAt: new Date().toLocaleString(),
                nextRun: calculateNextRun(scheduleType)
            };

            tasks.push(task);
            renderTasks();
            closeModal('createTaskModal');
            alert('任务创建成功！');
        }

        // 计算下次运行时间
        function calculateNextRun(scheduleType) {
            const now = new Date();
            switch(scheduleType) {
                case 'hourly':
                    return new Date(now.getTime() + 60 * 60 * 1000).toLocaleString();
                case 'daily':
                    const tomorrow = new Date(now);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(9, 0, 0, 0);
                    return tomorrow.toLocaleString();
                case 'weekly':
                    const nextWeek = new Date(now);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    return nextWeek.toLocaleString();
                default:
                    return '待计算';
            }
        }

        // 渲染任务列表
        function renderTasks() {
            // 这里可以动态更新任务列表
            console.log('Tasks updated:', tasks);
        }

        // 任务操作函数
        function runTaskNow(taskId) {
            alert(`正在执行任务 ${taskId}...`);
            addLog('success', `任务 ${taskId} 手动执行成功`);
        }

        function pauseTask(taskId) {
            alert(`任务 ${taskId} 已暂停`);
            addLog('warning', `任务 ${taskId} 已暂停`);
        }

        function resumeTask(taskId) {
            alert(`任务 ${taskId} 已恢复`);
            addLog('success', `任务 ${taskId} 已恢复运行`);
        }

        function editTask(taskId) {
            alert(`编辑任务 ${taskId}`);
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                alert(`任务 ${taskId} 已删除`);
                addLog('warning', `任务 ${taskId} 已被删除`);
            }
        }

        // 添加日志
        function addLog(type, message) {
            const log = {
                id: Date.now(),
                type: type,
                message: message,
                timestamp: new Date().toLocaleString()
            };
            logs.unshift(log);
            renderLogs();
        }

        // 渲染日志
        function renderLogs() {
            const logsList = document.getElementById('logsList');
            const filter = document.getElementById('logFilter').value;

            let filteredLogs = logs;
            if (filter !== 'all') {
                filteredLogs = logs.filter(log => log.type === filter);
            }

            logsList.innerHTML = filteredLogs.map(log => {
                const alertClass = log.type === 'success' ? 'alert-success' :
                                 log.type === 'error' ? 'alert-error' : 'alert-warning';
                return `<div class="alert ${alertClass}">
                    <strong>${log.timestamp}</strong> - ${log.message}
                </div>`;
            }).join('');
        }

        // 刷新日志
        function refreshLogs() {
            renderLogs();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化一些示例数据
            addLog('success', '系统启动成功');
            addLog('success', '爬虫引擎已就绪');

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            }
        });
    </script>
</body>
</html>
