<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能爬虫管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header-left p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .header-nav {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            padding: 10px 20px;
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-weight: 500;
        }

        .nav-btn:hover, .nav-btn.active {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-2px);
        }

        .breadcrumb {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            font-size: 14px;
            color: #6c757d;
        }

        .breadcrumb a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumb a:hover {
            text-decoration: underline;
        }

        .page-content {
            padding: 30px;
        }

        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 2em;
            color: #333;
            font-weight: 600;
        }

        .template-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .template-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
            position: relative;
        }

        .template-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .template-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
        }

        .template-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .template-url {
            color: #007bff;
            font-size: 0.9em;
            text-decoration: none;
            word-break: break-all;
        }

        .template-url:hover {
            text-decoration: underline;
        }

        .template-meta {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .meta-item {
            display: flex;
            flex-direction: column;
        }

        .meta-label {
            font-size: 0.8em;
            color: #6c757d;
            margin-bottom: 3px;
        }

        .meta-value {
            font-weight: 500;
            color: #333;
            font-size: 0.9em;
        }

        .template-actions {
            display: flex;
            gap: 8px;
            margin-top: 20px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        textarea.form-control {
            resize: vertical;
            min-height: 120px;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .radio-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .radio-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .radio-item:hover {
            border-color: #007bff;
            background: #f8f9ff;
        }

        .radio-item input[type="radio"] {
            margin: 0;
        }

        .radio-item.selected {
            border-color: #007bff;
            background: #e7f3ff;
        }

        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }

        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }

        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .toggle-slider {
            background-color: #007bff;
        }

        input:checked + .toggle-slider:before {
            transform: translateX(26px);
        }

        .tag-selector {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }

        .tag-item {
            padding: 6px 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .tag-item:hover {
            background: #e9ecef;
        }

        .tag-item.selected {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            border: 1px solid transparent;
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-outline {
            background: transparent;
            border: 1px solid #dee2e6;
            color: #6c757d;
        }

        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-lg {
            padding: 15px 30px;
            font-size: 16px;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state-icon {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .empty-state p {
            margin-bottom: 30px;
            font-size: 1.1em;
        }

        .config-form {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }

        .input-with-suggestions {
            position: relative;
        }

        .suggestions-list {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #dee2e6;
            border-top: none;
            border-radius: 0 0 8px 8px;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }

        .suggestion-item {
            padding: 10px 15px;
            cursor: pointer;
            border-bottom: 1px solid #f8f9fa;
        }

        .suggestion-item:hover {
            background: #f8f9fa;
        }

        .suggestion-item:last-child {
            border-bottom: none;
        }

        .regex-input-group {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .regex-input {
            flex: 1;
        }

        .regex-test-btn {
            padding: 12px 20px;
            background: #17a2b8;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
        }

        .regex-test-btn:hover {
            background: #138496;
        }

        .regex-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 0.9em;
        }

        .regex-result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .regex-result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .preview-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .preview-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .preview-content {
            background: white;
            border-radius: 8px;
            padding: 15px;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }

        .task-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #007bff;
        }

        .task-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .task-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .task-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-paused {
            background: #fff3cd;
            color: #856404;
        }

        .status-stopped {
            background: #f8d7da;
            color: #721c24;
        }

        .task-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
        }

        .info-label {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .info-value {
            font-weight: 500;
            color: #333;
        }

        .task-actions {
            display: flex;
            gap: 10px;
        }

        .btn-sm {
            padding: 8px 15px;
            font-size: 12px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }

        .close:hover {
            color: #000;
        }

        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        .alert-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .config-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 智能爬虫管理平台</h1>
            <p>可视化配置 · 实时预览 · 定时任务</p>
        </div>

        <div class="header-nav">
            <a href="#" class="nav-btn active" onclick="showPage('templates')">爬虫模板</a>
            <a href="#" class="nav-btn" onclick="showPage('tasks')">任务管理</a>
            <a href="#" class="nav-btn" onclick="showPage('logs')">运行日志</a>
        </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb" id="breadcrumb">
        <span>首页</span> > <span>爬虫模板列表</span>
    </div>

    <!-- 爬虫模板列表页面 -->
    <div id="templates-page" class="page-content">
        <div class="page-header">
            <h2 class="page-title">爬虫模板管理</h2>
            <button class="btn btn-primary btn-lg" onclick="showConfigPage()">
                + 新增模板
            </button>
        </div>

        <div id="templates-container">
            <!-- 模板列表将在这里动态生成 -->
            <div class="empty-state" id="empty-state">
                <div class="empty-state-icon">🕷️</div>
                <h3>还没有爬虫模板</h3>
                <p>创建你的第一个爬虫模板，开始数据采集之旅</p>
                <button class="btn btn-primary btn-lg" onclick="showConfigPage()">
                    创建第一个模板
                </button>
            </div>
        </div>
    </div>

    <!-- 爬虫配置页面 -->
    <div id="config-page" class="page-content" style="display: none;">
        <div class="page-header">
            <h2 class="page-title" id="config-page-title">新增爬虫模板</h2>
            <div>
                <button class="btn btn-outline" onclick="showPage('templates')">取消</button>
                <button class="btn btn-success" onclick="saveTemplate()">保存模板</button>
            </div>
        </div>

        <div class="config-form">
            <div class="form-section">
                <div class="section-title">基础信息</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="templateName">模板名称</label>
                        <input type="text" id="templateName" class="form-control" placeholder="请输入模板名称">
                    </div>
                    <div class="form-group">
                        <label for="targetUrl">目标网页URL</label>
                        <input type="url" id="targetUrl" class="form-control" placeholder="https://example.com">
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">模板描述</label>
                    <textarea id="description" class="form-control" placeholder="简要描述这个爬虫模板的用途"></textarea>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">数据提取配置</div>
                <div class="form-group">
                    <label for="prompt">AI提示词</label>
                    <textarea id="prompt" class="form-control" placeholder="请详细描述你想要提取的数据内容，例如：提取页面中所有商品的名称、价格、描述信息和图片链接"></textarea>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">输出设置</div>
                <div class="form-group">
                    <label>输出格式</label>
                    <div class="radio-group">
                        <div class="radio-item" onclick="selectRadio('outputFormat', 'json')">
                            <input type="radio" name="outputFormat" value="json" checked>
                            <span>JSON</span>
                        </div>
                        <div class="radio-item" onclick="selectRadio('outputFormat', 'csv')">
                            <input type="radio" name="outputFormat" value="csv">
                            <span>CSV</span>
                        </div>
                        <div class="radio-item" onclick="selectRadio('outputFormat', 'excel')">
                            <input type="radio" name="outputFormat" value="excel">
                            <span>Excel</span>
                        </div>
                        <div class="radio-item" onclick="selectRadio('outputFormat', 'markdown')">
                            <input type="radio" name="outputFormat" value="markdown">
                            <span>Markdown</span>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="outputPath">输出路径</label>
                    <div class="input-with-suggestions">
                        <input type="text" id="outputPath" class="form-control" placeholder="./output/" oninput="showPathSuggestions(this.value)">
                        <div class="suggestions-list" id="pathSuggestions"></div>
                    </div>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">高级设置</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="delay">请求间隔(秒)</label>
                        <input type="number" id="delay" class="form-control" value="1" min="0.1" step="0.1">
                    </div>
                    <div class="form-group">
                        <label for="maxPages">最大页数</label>
                        <input type="number" id="maxPages" class="form-control" value="10" min="1">
                    </div>
                </div>

                <div class="form-group">
                    <label>启用JavaScript渲染</label>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <label class="toggle-switch">
                            <input type="checkbox" id="enableJS">
                            <span class="toggle-slider"></span>
                        </label>
                        <span>适用于需要JavaScript动态加载内容的页面</span>
                    </div>
                </div>

                <div class="form-group">
                    <label>请求头标签</label>
                    <div class="tag-selector" id="headerTags">
                        <div class="tag-item" onclick="toggleTag(this, 'mobile')">移动端UA</div>
                        <div class="tag-item" onclick="toggleTag(this, 'desktop')">桌面端UA</div>
                        <div class="tag-item" onclick="toggleTag(this, 'chrome')">Chrome浏览器</div>
                        <div class="tag-item" onclick="toggleTag(this, 'firefox')">Firefox浏览器</div>
                        <div class="tag-item" onclick="toggleTag(this, 'accept-json')">接受JSON</div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="customHeaders">自定义请求头 (JSON格式)</label>
                    <textarea id="customHeaders" class="form-control" placeholder='{"User-Agent": "Mozilla/5.0...", "Accept": "application/json"}'></textarea>
                </div>
            </div>

            <div class="form-section">
                <div class="section-title">实时预览</div>
                <div style="text-align: center; margin-bottom: 20px;">
                    <button class="btn btn-primary btn-lg" onclick="previewCrawl()">
                        <span id="previewLoading" class="loading" style="display: none;"></span>
                        🔍 开始预览
                    </button>
                </div>

                <div class="preview-section" id="previewSection" style="display: none;">
                    <div class="preview-header">
                        <h3>预览结果</h3>
                        <button class="btn btn-warning btn-sm" onclick="exportPreview()">导出预览数据</button>
                    </div>
                    <div class="preview-content" id="previewContent">
                        <!-- 预览内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 任务管理页面 -->
    <div id="tasks-page" class="page-content" style="display: none;">
        <div class="page-header">
            <h2 class="page-title">定时任务管理</h2>
            <button class="btn btn-primary btn-lg" onclick="showCreateTaskModal()">+ 创建新任务</button>
        </div>

        <div id="tasksList">
            <!-- 示例任务卡片 -->
            <div class="task-card">
                <div class="task-header">
                    <div class="task-title">电商商品信息采集</div>
                    <div class="task-status status-active">运行中</div>
                </div>
                <div class="task-info">
                    <div class="info-item">
                        <div class="info-label">目标网站</div>
                        <div class="info-value">https://example-shop.com</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">执行频率</div>
                        <div class="info-value">每天 09:00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">时间匹配规则</div>
                        <div class="info-value">^(0[9]|1[0-8]):[0-5][0-9]$</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">下次执行</div>
                        <div class="info-value">2024-01-15 09:00</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">输出格式</div>
                        <div class="info-value">Excel</div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-primary btn-sm" onclick="runTaskNow('task1')">立即执行</button>
                    <button class="btn btn-warning btn-sm" onclick="pauseTask('task1')">暂停</button>
                    <button class="btn btn-outline btn-sm" onclick="editTask('task1')">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteTask('task1')">删除</button>
                </div>
            </div>

            <div class="task-card">
                <div class="task-header">
                    <div class="task-title">新闻资讯监控</div>
                    <div class="task-status status-paused">已暂停</div>
                </div>
                <div class="task-info">
                    <div class="info-item">
                        <div class="info-label">目标网站</div>
                        <div class="info-value">https://news-site.com</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">执行频率</div>
                        <div class="info-value">每小时</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">时间匹配规则</div>
                        <div class="info-value">^[0-2][0-9]:[0-5][0-9]$</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">最后执行</div>
                        <div class="info-value">2024-01-14 15:30</div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">输出格式</div>
                        <div class="info-value">JSON</div>
                    </div>
                </div>
                <div class="task-actions">
                    <button class="btn btn-success btn-sm" onclick="resumeTask('task2')">恢复</button>
                    <button class="btn btn-outline btn-sm" onclick="editTask('task2')">编辑</button>
                    <button class="btn btn-danger btn-sm" onclick="deleteTask('task2')">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 运行日志页面 -->
    <div id="logs-page" class="page-content" style="display: none;">
        <div class="page-header">
            <h2 class="page-title">运行日志</h2>
            <div>
                <select id="logFilter" class="form-control" style="width: auto; display: inline-block; margin-right: 10px;">
                    <option value="all">所有日志</option>
                    <option value="success">成功</option>
                    <option value="error">错误</option>
                    <option value="warning">警告</option>
                </select>
                <button class="btn btn-outline" onclick="refreshLogs()">刷新</button>
            </div>
        </div>

        <div id="logsList">
            <div class="alert alert-success">
                <strong>2024-01-14 16:30:25</strong> - 任务 "电商商品信息采集" 执行成功，采集到 156 条数据
            </div>
            <div class="alert alert-error">
                <strong>2024-01-14 15:45:12</strong> - 任务 "新闻资讯监控" 执行失败：网络连接超时
            </div>
            <div class="alert alert-success">
                <strong>2024-01-14 14:20:08</strong> - 任务 "电商商品信息采集" 执行成功，采集到 142 条数据
            </div>
        </div>
    </div>

    <!-- 创建任务模态框 -->
    <div id="createTaskModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createTaskModal')">&times;</span>
            <h2>创建定时任务</h2>

            <div class="form-group">
                <label for="scheduleTaskName">任务名称</label>
                <input type="text" id="scheduleTaskName" class="form-control">
            </div>

            <div class="form-group">
                <label>执行频率</label>
                <div class="radio-group">
                    <div class="radio-item" onclick="selectScheduleType('once')">
                        <input type="radio" name="scheduleType" value="once">
                        <span>仅执行一次</span>
                    </div>
                    <div class="radio-item" onclick="selectScheduleType('hourly')">
                        <input type="radio" name="scheduleType" value="hourly">
                        <span>每小时</span>
                    </div>
                    <div class="radio-item" onclick="selectScheduleType('daily')">
                        <input type="radio" name="scheduleType" value="daily" checked>
                        <span>每天</span>
                    </div>
                    <div class="radio-item" onclick="selectScheduleType('weekly')">
                        <input type="radio" name="scheduleType" value="weekly">
                        <span>每周</span>
                    </div>
                    <div class="radio-item" onclick="selectScheduleType('custom')">
                        <input type="radio" name="scheduleType" value="custom">
                        <span>自定义</span>
                    </div>
                </div>
            </div>

            <div id="timeRegexConfig" class="form-group">
                <label for="timeRegex">时间匹配正则表达式</label>
                <div class="regex-input-group">
                    <div class="regex-input">
                        <input type="text" id="timeRegex" class="form-control" placeholder="^(0[9]|1[0-8]):[0-5][0-9]$" value="^(0[9]|1[0-8]):[0-5][0-9]$">
                        <small style="color: #6c757d;">用于匹配执行时间的正则表达式，例如：^(0[9]|1[0-8]):[0-5][0-9]$ 匹配09:00-18:59</small>
                    </div>
                    <button type="button" class="regex-test-btn" onclick="testTimeRegex()">测试正则</button>
                </div>
                <div id="regexTestResult" class="regex-result" style="display: none;"></div>
            </div>

            <div id="scheduleOptions" class="form-group">
                <label for="scheduleTime">执行时间</label>
                <input type="time" id="scheduleTime" class="form-control" value="09:00">
            </div>

            <div class="form-group">
                <label for="configSelect">选择爬虫模板</label>
                <select id="configSelect" class="form-control">
                    <option value="">请选择已保存的模板</option>
                </select>
            </div>

            <div style="text-align: right; margin-top: 30px;">
                <button class="btn btn-outline" onclick="closeModal('createTaskModal')">取消</button>
                <button class="btn btn-primary" onclick="createScheduledTask()">创建任务</button>
            </div>
        </div>
    </div>

    <!-- 删除确认模态框 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content" style="max-width: 400px;">
            <span class="close" onclick="closeModal('deleteConfirmModal')">&times;</span>
            <h3>确认删除</h3>
            <p id="deleteConfirmText">确定要删除这个模板吗？此操作不可撤销。</p>
            <div style="text-align: right; margin-top: 30px;">
                <button class="btn btn-outline" onclick="closeModal('deleteConfirmModal')">取消</button>
                <button class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
            </div>
        </div>
    </div>

    <!-- 创建任务模态框 -->
    <div id="createTaskModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('createTaskModal')">&times;</span>
            <h2>创建定时任务</h2>

            <div class="form-group">
                <label for="scheduleTaskName">任务名称</label>
                <input type="text" id="scheduleTaskName" class="form-control">
            </div>

            <div class="form-group">
                <label for="scheduleType">执行频率</label>
                <select id="scheduleType" class="form-control" onchange="updateScheduleOptions()">
                    <option value="once">仅执行一次</option>
                    <option value="hourly">每小时</option>
                    <option value="daily">每天</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                    <option value="custom">自定义 (Cron表达式)</option>
                </select>
            </div>

            <div id="scheduleOptions" class="form-group">
                <label for="scheduleTime">执行时间</label>
                <input type="time" id="scheduleTime" class="form-control" value="09:00">
            </div>

            <div id="cronExpression" class="form-group" style="display: none;">
                <label for="cronInput">Cron表达式</label>
                <input type="text" id="cronInput" class="form-control" placeholder="0 0 9 * * ?">
                <small style="color: #6c757d;">格式：秒 分 时 日 月 周</small>
            </div>

            <div class="form-group">
                <label for="configSelect">选择爬虫配置</label>
                <select id="configSelect" class="form-control">
                    <option value="">请选择已保存的配置</option>
                    <option value="config1">电商商品配置</option>
                    <option value="config2">新闻资讯配置</option>
                </select>
            </div>

            <div style="text-align: right; margin-top: 30px;">
                <button class="btn" onclick="closeModal('createTaskModal')">取消</button>
                <button class="btn btn-primary" onclick="createScheduledTask()">创建任务</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentConfig = {};
        let savedTemplates = [];
        let tasks = [];
        let logs = [];
        let currentEditingId = null;
        let deleteTargetId = null;

        // 页面切换
        function showPage(pageName) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.style.display = 'none');

            // 移除所有导航按钮的激活状态
            const navBtns = document.querySelectorAll('.nav-btn');
            navBtns.forEach(btn => btn.classList.remove('active'));

            // 显示选中的页面
            document.getElementById(pageName + '-page').style.display = 'block';
            event.target.classList.add('active');

            // 更新面包屑
            updateBreadcrumb(pageName);
        }

        // 更新面包屑导航
        function updateBreadcrumb(pageName) {
            const breadcrumb = document.getElementById('breadcrumb');
            const pageNames = {
                'templates': '爬虫模板列表',
                'tasks': '任务管理',
                'logs': '运行日志',
                'config': '爬虫配置'
            };

            if (pageName === 'config') {
                breadcrumb.innerHTML = `<a href="#" onclick="showPage('templates')">首页</a> > <a href="#" onclick="showPage('templates')">爬虫模板列表</a> > <span>${currentEditingId ? '编辑模板' : '新增模板'}</span>`;
            } else {
                breadcrumb.innerHTML = `<span>首页</span> > <span>${pageNames[pageName]}</span>`;
            }
        }

        // 显示配置页面
        function showConfigPage(templateId = null) {
            currentEditingId = templateId;

            // 隐藏所有页面
            const pages = document.querySelectorAll('.page-content');
            pages.forEach(page => page.style.display = 'none');

            // 显示配置页面
            document.getElementById('config-page').style.display = 'block';

            // 更新页面标题和面包屑
            const title = document.getElementById('config-page-title');
            title.textContent = templateId ? '编辑爬虫模板' : '新增爬虫模板';
            updateBreadcrumb('config');

            // 如果是编辑模式，填充现有数据
            if (templateId) {
                loadTemplateData(templateId);
            } else {
                clearConfigForm();
            }
        }

        // 加载模板数据到表单
        function loadTemplateData(templateId) {
            const template = savedTemplates.find(t => t.id === templateId);
            if (template) {
                document.getElementById('templateName').value = template.name;
                document.getElementById('targetUrl').value = template.url;
                document.getElementById('description').value = template.description || '';
                document.getElementById('prompt').value = template.prompt;
                document.getElementById('outputPath').value = template.outputPath;
                document.getElementById('delay').value = template.delay;
                document.getElementById('maxPages').value = template.maxPages;
                document.getElementById('enableJS').checked = template.enableJS || false;
                document.getElementById('customHeaders').value = template.headers || '';

                // 设置输出格式单选按钮
                selectRadio('outputFormat', template.format);

                // 设置请求头标签
                if (template.headerTags) {
                    template.headerTags.forEach(tag => {
                        const tagElement = document.querySelector(`[onclick*="${tag}"]`);
                        if (tagElement) {
                            tagElement.classList.add('selected');
                        }
                    });
                }
            }
        }

        // 清空配置表单
        function clearConfigForm() {
            document.getElementById('templateName').value = '';
            document.getElementById('targetUrl').value = '';
            document.getElementById('description').value = '';
            document.getElementById('prompt').value = '';
            document.getElementById('outputPath').value = './output/';
            document.getElementById('delay').value = '1';
            document.getElementById('maxPages').value = '10';
            document.getElementById('enableJS').checked = false;
            document.getElementById('customHeaders').value = '';

            // 重置单选按钮
            selectRadio('outputFormat', 'json');

            // 清除所有标签选择
            document.querySelectorAll('.tag-item.selected').forEach(tag => {
                tag.classList.remove('selected');
            });
        }

        // 单选按钮选择
        function selectRadio(groupName, value) {
            // 移除所有同组的选中状态
            document.querySelectorAll(`input[name="${groupName}"]`).forEach(radio => {
                radio.checked = false;
                radio.closest('.radio-item').classList.remove('selected');
            });

            // 设置选中状态
            const selectedRadio = document.querySelector(`input[name="${groupName}"][value="${value}"]`);
            if (selectedRadio) {
                selectedRadio.checked = true;
                selectedRadio.closest('.radio-item').classList.add('selected');
            }
        }

        // 标签切换
        function toggleTag(element, tagValue) {
            element.classList.toggle('selected');
        }

        // 路径建议
        function showPathSuggestions(value) {
            const suggestions = [
                './output/',
                './data/',
                './downloads/',
                './exports/',
                './results/'
            ];

            const suggestionsList = document.getElementById('pathSuggestions');
            const filteredSuggestions = suggestions.filter(s => s.includes(value.toLowerCase()));

            if (filteredSuggestions.length > 0 && value) {
                suggestionsList.innerHTML = filteredSuggestions.map(s =>
                    `<div class="suggestion-item" onclick="selectPathSuggestion('${s}')">${s}</div>`
                ).join('');
                suggestionsList.style.display = 'block';
            } else {
                suggestionsList.style.display = 'none';
            }
        }

        // 选择路径建议
        function selectPathSuggestion(path) {
            document.getElementById('outputPath').value = path;
            document.getElementById('pathSuggestions').style.display = 'none';
        }

        // 实时预览功能
        async function previewCrawl() {
            const url = document.getElementById('targetUrl').value;
            const prompt = document.getElementById('prompt').value;
            const format = document.querySelector('input[name="outputFormat"]:checked')?.value || 'json';

            if (!url || !prompt) {
                alert('请填写目标URL和提示词');
                return;
            }

            // 显示加载状态
            document.getElementById('previewLoading').style.display = 'inline-block';
            document.getElementById('previewSection').style.display = 'block';

            // 模拟API调用
            setTimeout(() => {
                const mockData = generateMockPreviewData(format);
                document.getElementById('previewContent').innerHTML = mockData;
                document.getElementById('previewLoading').style.display = 'none';
            }, 2000);
        }

        // 生成模拟预览数据
        function generateMockPreviewData(format) {
            const sampleData = [
                { name: "iPhone 15 Pro", price: "¥8999", description: "最新款苹果手机" },
                { name: "MacBook Air M2", price: "¥9499", description: "轻薄笔记本电脑" },
                { name: "AirPods Pro", price: "¥1899", description: "无线降噪耳机" }
            ];

            switch(format) {
                case 'json':
                    return `<pre>${JSON.stringify(sampleData, null, 2)}</pre>`;
                case 'csv':
                    return `<pre>name,price,description
iPhone 15 Pro,¥8999,最新款苹果手机
MacBook Air M2,¥9499,轻薄笔记本电脑
AirPods Pro,¥1899,无线降噪耳机</pre>`;
                case 'markdown':
                    return `<pre># 商品信息

| 名称 | 价格 | 描述 |
|------|------|------|
| iPhone 15 Pro | ¥8999 | 最新款苹果手机 |
| MacBook Air M2 | ¥9499 | 轻薄笔记本电脑 |
| AirPods Pro | ¥1899 | 无线降噪耳机 |</pre>`;
                default:
                    return '<pre>预览数据将在这里显示...</pre>';
            }
        }

        // 保存模板
        function saveTemplate() {
            const name = document.getElementById('templateName').value;
            const url = document.getElementById('targetUrl').value;
            const prompt = document.getElementById('prompt').value;

            if (!name || !url || !prompt) {
                alert('请填写模板名称、目标URL和提示词');
                return;
            }

            const template = {
                id: currentEditingId || Date.now(),
                name: name,
                url: url,
                description: document.getElementById('description').value,
                prompt: prompt,
                format: document.querySelector('input[name="outputFormat"]:checked')?.value || 'json',
                outputPath: document.getElementById('outputPath').value,
                delay: document.getElementById('delay').value,
                maxPages: document.getElementById('maxPages').value,
                enableJS: document.getElementById('enableJS').checked,
                headers: document.getElementById('customHeaders').value,
                headerTags: Array.from(document.querySelectorAll('.tag-item.selected')).map(tag =>
                    tag.getAttribute('onclick').match(/'([^']+)'/)[1]
                ),
                createdAt: currentEditingId ?
                    savedTemplates.find(t => t.id === currentEditingId)?.createdAt :
                    new Date().toLocaleString(),
                updatedAt: new Date().toLocaleString()
            };

            if (currentEditingId) {
                // 更新现有模板
                const index = savedTemplates.findIndex(t => t.id === currentEditingId);
                if (index !== -1) {
                    savedTemplates[index] = template;
                }
            } else {
                // 添加新模板
                savedTemplates.push(template);
            }

            updateConfigSelect();
            renderTemplates();
            showPage('templates');
            alert(currentEditingId ? '模板更新成功！' : '模板保存成功！');
        }

        // 渲染模板列表
        function renderTemplates() {
            const container = document.getElementById('templates-container');
            const emptyState = document.getElementById('empty-state');

            if (savedTemplates.length === 0) {
                emptyState.style.display = 'block';
                return;
            }

            emptyState.style.display = 'none';

            const templatesHTML = savedTemplates.map(template => `
                <div class="template-card">
                    <div class="template-header">
                        <div>
                            <div class="template-title">${template.name}</div>
                            <a href="${template.url}" class="template-url" target="_blank">${template.url}</a>
                        </div>
                    </div>
                    <div class="template-meta">
                        <div class="meta-item">
                            <div class="meta-label">输出格式</div>
                            <div class="meta-value">${template.format.toUpperCase()}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">创建时间</div>
                            <div class="meta-value">${template.createdAt}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">最后修改</div>
                            <div class="meta-value">${template.updatedAt}</div>
                        </div>
                        <div class="meta-item">
                            <div class="meta-label">请求间隔</div>
                            <div class="meta-value">${template.delay}秒</div>
                        </div>
                    </div>
                    <div class="template-actions">
                        <button class="btn btn-primary btn-sm" onclick="showConfigPage(${template.id})">编辑</button>
                        <button class="btn btn-outline btn-sm" onclick="duplicateTemplate(${template.id})">复制</button>
                        <button class="btn btn-danger btn-sm" onclick="showDeleteConfirm(${template.id}, '${template.name}')">删除</button>
                    </div>
                </div>
            `).join('');

            container.innerHTML = templatesHTML;
        }

        // 复制模板
        function duplicateTemplate(templateId) {
            const template = savedTemplates.find(t => t.id === templateId);
            if (template) {
                const newTemplate = {
                    ...template,
                    id: Date.now(),
                    name: template.name + ' (副本)',
                    createdAt: new Date().toLocaleString(),
                    updatedAt: new Date().toLocaleString()
                };
                savedTemplates.push(newTemplate);
                renderTemplates();
                updateConfigSelect();
                alert('模板复制成功！');
            }
        }

        // 显示删除确认对话框
        function showDeleteConfirm(templateId, templateName) {
            deleteTargetId = templateId;
            document.getElementById('deleteConfirmText').textContent = `确定要删除模板 "${templateName}" 吗？此操作不可撤销。`;
            document.getElementById('deleteConfirmModal').style.display = 'block';
        }

        // 确认删除
        function confirmDelete() {
            if (deleteTargetId) {
                const index = savedTemplates.findIndex(t => t.id === deleteTargetId);
                if (index !== -1) {
                    savedTemplates.splice(index, 1);
                    renderTemplates();
                    updateConfigSelect();
                    alert('模板删除成功！');
                }
                deleteTargetId = null;
            }
            closeModal('deleteConfirmModal');
        }

        // 更新配置选择下拉框
        function updateConfigSelect() {
            const select = document.getElementById('configSelect');
            select.innerHTML = '<option value="">请选择已保存的配置</option>';
            savedConfigs.forEach(config => {
                const option = document.createElement('option');
                option.value = config.id;
                option.textContent = config.name;
                select.appendChild(option);
            });
        }

        // 导出预览数据
        function exportPreview() {
            const content = document.getElementById('previewContent').textContent;
            const format = document.getElementById('outputFormat').value;
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `preview_data.${format}`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 显示创建任务模态框
        function showCreateTaskModal() {
            document.getElementById('createTaskModal').style.display = 'block';
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 选择调度类型
        function selectScheduleType(type) {
            // 更新单选按钮
            selectRadio('scheduleType', type);

            // 根据类型更新时间正则表达式
            const timeRegexInput = document.getElementById('timeRegex');
            const regexPatterns = {
                'once': '^\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}$',
                'hourly': '^[0-5]?[0-9]$',
                'daily': '^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$',
                'weekly': '^(0[0-6]) (0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$',
                'custom': ''
            };

            if (regexPatterns[type]) {
                timeRegexInput.value = regexPatterns[type];
            }
        }

        // 测试时间正则表达式
        function testTimeRegex() {
            const regex = document.getElementById('timeRegex').value;
            const resultDiv = document.getElementById('regexTestResult');

            if (!regex) {
                resultDiv.style.display = 'none';
                return;
            }

            try {
                const regexObj = new RegExp(regex);
                const testCases = [
                    '09:00', '18:30', '23:59', '00:00',
                    '25:00', 'abc', '9:5', '12:60'
                ];

                const results = testCases.map(test => {
                    const match = regexObj.test(test);
                    return `${test}: ${match ? '✓ 匹配' : '✗ 不匹配'}`;
                });

                resultDiv.className = 'regex-result success';
                resultDiv.innerHTML = `
                    <strong>正则表达式测试结果：</strong><br>
                    ${results.join('<br>')}
                `;
                resultDiv.style.display = 'block';
            } catch (error) {
                resultDiv.className = 'regex-result error';
                resultDiv.innerHTML = `<strong>正则表达式错误：</strong> ${error.message}`;
                resultDiv.style.display = 'block';
            }
        }

        // 创建定时任务
        function createScheduledTask() {
            const taskName = document.getElementById('scheduleTaskName').value;
            const scheduleType = document.getElementById('scheduleType').value;
            const configId = document.getElementById('configSelect').value;

            if (!taskName || !configId) {
                alert('请填写任务名称并选择配置');
                return;
            }

            const task = {
                id: Date.now(),
                name: taskName,
                scheduleType: scheduleType,
                configId: configId,
                status: 'active',
                createdAt: new Date().toLocaleString(),
                nextRun: calculateNextRun(scheduleType)
            };

            tasks.push(task);
            renderTasks();
            closeModal('createTaskModal');
            alert('任务创建成功！');
        }

        // 计算下次运行时间
        function calculateNextRun(scheduleType) {
            const now = new Date();
            switch(scheduleType) {
                case 'hourly':
                    return new Date(now.getTime() + 60 * 60 * 1000).toLocaleString();
                case 'daily':
                    const tomorrow = new Date(now);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(9, 0, 0, 0);
                    return tomorrow.toLocaleString();
                case 'weekly':
                    const nextWeek = new Date(now);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    return nextWeek.toLocaleString();
                default:
                    return '待计算';
            }
        }

        // 渲染任务列表
        function renderTasks() {
            // 这里可以动态更新任务列表
            console.log('Tasks updated:', tasks);
        }

        // 任务操作函数
        function runTaskNow(taskId) {
            alert(`正在执行任务 ${taskId}...`);
            addLog('success', `任务 ${taskId} 手动执行成功`);
        }

        function pauseTask(taskId) {
            alert(`任务 ${taskId} 已暂停`);
            addLog('warning', `任务 ${taskId} 已暂停`);
        }

        function resumeTask(taskId) {
            alert(`任务 ${taskId} 已恢复`);
            addLog('success', `任务 ${taskId} 已恢复运行`);
        }

        function editTask(taskId) {
            alert(`编辑任务 ${taskId}`);
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                alert(`任务 ${taskId} 已删除`);
                addLog('warning', `任务 ${taskId} 已被删除`);
            }
        }

        // 添加日志
        function addLog(type, message) {
            const log = {
                id: Date.now(),
                type: type,
                message: message,
                timestamp: new Date().toLocaleString()
            };
            logs.unshift(log);
            renderLogs();
        }

        // 渲染日志
        function renderLogs() {
            const logsList = document.getElementById('logsList');
            const filter = document.getElementById('logFilter').value;

            let filteredLogs = logs;
            if (filter !== 'all') {
                filteredLogs = logs.filter(log => log.type === filter);
            }

            logsList.innerHTML = filteredLogs.map(log => {
                const alertClass = log.type === 'success' ? 'alert-success' :
                                 log.type === 'error' ? 'alert-error' : 'alert-warning';
                return `<div class="alert ${alertClass}">
                    <strong>${log.timestamp}</strong> - ${log.message}
                </div>`;
            }).join('');
        }

        // 刷新日志
        function refreshLogs() {
            renderLogs();
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 显示创建任务模态框
        function showCreateTaskModal() {
            document.getElementById('createTaskModal').style.display = 'block';
            selectScheduleType('daily'); // 默认选择每天
        }

        // 创建定时任务
        function createScheduledTask() {
            const taskName = document.getElementById('scheduleTaskName').value;
            const scheduleType = document.querySelector('input[name="scheduleType"]:checked')?.value;
            const timeRegex = document.getElementById('timeRegex').value;
            const configId = document.getElementById('configSelect').value;

            if (!taskName || !configId) {
                alert('请填写任务名称并选择模板');
                return;
            }

            const task = {
                id: Date.now(),
                name: taskName,
                scheduleType: scheduleType,
                timeRegex: timeRegex,
                configId: configId,
                status: 'active',
                createdAt: new Date().toLocaleString(),
                nextRun: calculateNextRun(scheduleType)
            };

            tasks.push(task);
            renderTasks();
            closeModal('createTaskModal');
            alert('任务创建成功！');
        }

        // 计算下次运行时间
        function calculateNextRun(scheduleType) {
            const now = new Date();
            switch(scheduleType) {
                case 'hourly':
                    return new Date(now.getTime() + 60 * 60 * 1000).toLocaleString();
                case 'daily':
                    const tomorrow = new Date(now);
                    tomorrow.setDate(tomorrow.getDate() + 1);
                    tomorrow.setHours(9, 0, 0, 0);
                    return tomorrow.toLocaleString();
                case 'weekly':
                    const nextWeek = new Date(now);
                    nextWeek.setDate(nextWeek.getDate() + 7);
                    return nextWeek.toLocaleString();
                default:
                    return '待计算';
            }
        }

        // 渲染任务列表
        function renderTasks() {
            console.log('Tasks updated:', tasks);
        }

        // 任务操作函数
        function runTaskNow(taskId) {
            alert(`正在执行任务 ${taskId}...`);
            addLog('success', `任务 ${taskId} 手动执行成功`);
        }

        function pauseTask(taskId) {
            alert(`任务 ${taskId} 已暂停`);
            addLog('warning', `任务 ${taskId} 已暂停`);
        }

        function resumeTask(taskId) {
            alert(`任务 ${taskId} 已恢复`);
            addLog('success', `任务 ${taskId} 已恢复运行`);
        }

        function editTask(taskId) {
            alert(`编辑任务 ${taskId}`);
        }

        function deleteTask(taskId) {
            if (confirm('确定要删除这个任务吗？')) {
                alert(`任务 ${taskId} 已删除`);
                addLog('warning', `任务 ${taskId} 已被删除`);
            }
        }

        // 添加日志
        function addLog(type, message) {
            const log = {
                id: Date.now(),
                type: type,
                message: message,
                timestamp: new Date().toLocaleString()
            };
            logs.unshift(log);
            renderLogs();
        }

        // 渲染日志
        function renderLogs() {
            const logsList = document.getElementById('logsList');
            const filter = document.getElementById('logFilter').value;

            let filteredLogs = logs;
            if (filter !== 'all') {
                filteredLogs = logs.filter(log => log.type === filter);
            }

            logsList.innerHTML = filteredLogs.map(log => {
                const alertClass = log.type === 'success' ? 'alert-success' :
                                 log.type === 'error' ? 'alert-error' : 'alert-warning';
                return `<div class="alert ${alertClass}">
                    <strong>${log.timestamp}</strong> - ${log.message}
                </div>`;
            }).join('');
        }

        // 刷新日志
        function refreshLogs() {
            renderLogs();
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化一些示例数据
            addLog('success', '系统启动成功');
            addLog('success', '爬虫引擎已就绪');

            // 初始化示例模板
            savedTemplates = [
                {
                    id: 1,
                    name: '电商商品信息采集',
                    url: 'https://example-shop.com',
                    description: '采集商品名称、价格、描述等信息',
                    prompt: '提取页面中所有商品的名称、价格和描述信息',
                    format: 'excel',
                    outputPath: './output/',
                    delay: '1',
                    maxPages: '10',
                    enableJS: false,
                    headers: '',
                    headerTags: ['desktop', 'chrome'],
                    createdAt: '2024-01-10 10:30:00',
                    updatedAt: '2024-01-14 15:20:00'
                },
                {
                    id: 2,
                    name: '新闻资讯监控',
                    url: 'https://news-site.com',
                    description: '监控最新新闻资讯',
                    prompt: '提取新闻标题、发布时间、内容摘要',
                    format: 'json',
                    outputPath: './news/',
                    delay: '0.5',
                    maxPages: '5',
                    enableJS: true,
                    headers: '',
                    headerTags: ['mobile'],
                    createdAt: '2024-01-12 14:15:00',
                    updatedAt: '2024-01-13 09:45:00'
                }
            ];

            renderTemplates();
            updateConfigSelect();

            // 点击模态框外部关闭
            window.onclick = function(event) {
                const modals = document.querySelectorAll('.modal');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        modal.style.display = 'none';
                    }
                });
            }

            // 点击页面其他地方关闭建议列表
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.input-with-suggestions')) {
                    document.querySelectorAll('.suggestions-list').forEach(list => {
                        list.style.display = 'none';
                    });
                }
            });
        });
    </script>
</body>
</html>
