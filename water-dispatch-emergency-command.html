<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供水调度应急指挥系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f2027 0%, #203a43 25%, #2c5364 50%, #0f4c75 75%, #3282b8 100%);
            min-height: 100vh;
            color: #333;
        }

        .command-container {
            max-width: 1920px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .command-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(30px);
            border-radius: 35px;
            padding: 45px 55px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .command-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(15, 32, 39, 0.08), transparent);
            animation: shimmer 5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3.8rem;
            background: linear-gradient(135deg, #0f2027, #203a43, #3282b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 35px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.5rem;
            margin-bottom: 35px;
            position: relative;
            z-index: 1;
        }

        .system-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 30px;
            margin-top: 40px;
            position: relative;
            z-index: 1;
        }

        .status-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.5s ease;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .status-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #0f2027, #3282b8, #0f4c75);
            transform: scaleX(0);
            transition: transform 0.5s ease;
        }

        .status-item:hover {
            transform: translateY(-10px);
            border-color: #3282b8;
            box-shadow: 0 20px 50px rgba(50, 130, 184, 0.3);
        }

        .status-item:hover::before {
            transform: scaleX(1);
        }

        .status-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #0f2027, #3282b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .status-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .status-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #0f2027;
            margin-bottom: 8px;
        }

        .status-desc {
            font-size: 1rem;
            color: #666;
        }

        /* 导航标签 */
        .nav-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
            display: flex;
            gap: 15px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            flex: 1;
            min-width: 220px;
            padding: 25px 35px;
            background: transparent;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: 700;
            color: #7f8c8d;
            transition: all 0.5s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #0f2027, #3282b8);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .nav-tab.active::before {
            left: 0;
        }

        .nav-tab.active {
            color: white;
            box-shadow: 0 15px 40px rgba(15, 32, 39, 0.4);
            transform: translateY(-5px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(15, 32, 39, 0.1);
            color: #0f2027;
            transform: translateY(-4px);
        }

        .nav-icon {
            font-size: 2rem;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 35px;
            padding: 50px;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
            min-height: 1000px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 1s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 模块标题 */
        .module-title {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #0f2027, #3282b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 20px;
            padding-bottom: 25px;
            border-bottom: 4px solid #f0f0f0;
            position: relative;
        }

        .module-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 120px;
            height: 4px;
            background: linear-gradient(135deg, #0f2027, #3282b8);
        }

        /* 监控面板 */
        .monitor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 35px;
            margin-bottom: 45px;
        }

        .monitor-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.5s ease;
            border-left: 10px solid;
            cursor: pointer;
        }

        .monitor-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
        }

        .monitor-card.monitoring { border-left-color: #2196f3; }
        .monitor-card.warning { border-left-color: #ff9800; }
        .monitor-card.dispatch { border-left-color: #4caf50; }
        .monitor-card.duty { border-left-color: #9c27b0; }
        .monitor-card.resource { border-left-color: #f44336; }
        .monitor-card.emergency { border-left-color: #e91e63; }

        .monitor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .monitor-title {
            font-size: 1.4rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 700;
        }

        .monitor-status {
            padding: 10px 18px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-normal { background: #e8f5e8; color: #2e7d32; }
        .status-warning { background: #fff3e0; color: #ef6c00; }
        .status-danger { background: #ffebee; color: #c62828; }
        .status-info { background: #e3f2fd; color: #1565c0; }

        .monitor-value {
            font-size: 3.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #0f2027, #3282b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            display: flex;
            align-items: baseline;
            gap: 15px;
        }

        .monitor-unit {
            font-size: 1.3rem;
            color: #888;
        }

        .monitor-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.7;
        }

        /* 图表区域 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 45px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
        }

        .chart-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 35px;
            padding-bottom: 25px;
            border-bottom: 3px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.6rem;
            font-weight: 700;
            background: linear-gradient(135deg, #0f2027, #3282b8);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chart-controls {
            display: flex;
            gap: 15px;
        }

        .chart-btn {
            padding: 12px 22px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            color: #666;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: 700;
        }

        .chart-btn.active {
            background: linear-gradient(135deg, #0f2027, #3282b8);
            color: white;
            border-color: transparent;
            box-shadow: 0 10px 30px rgba(15, 32, 39, 0.3);
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
            border-color: #0f2027;
        }

        .chart-container {
            position: relative;
            height: 500px;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 35px;
            background: white;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 20px 30px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #0f2027, #3282b8);
            color: white;
            font-weight: 700;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(15, 32, 39, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 700;
        }

        /* 按钮样式 */
        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            transition: all 0.4s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0f2027, #3282b8);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(15, 32, 39, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 35px;
            flex-wrap: wrap;
        }

        /* 预警面板 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 10px solid #ffc107;
            border-radius: 25px;
            padding: 30px 35px;
            margin-bottom: 35px;
            display: flex;
            align-items: center;
            gap: 25px;
            box-shadow: 0 15px 50px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.danger {
            background: linear-gradient(135deg, #f8d7da, #fab1a0);
            border-left-color: #dc3545;
            box-shadow: 0 15px 50px rgba(220, 53, 69, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 15px 50px rgba(40, 167, 69, 0.2);
        }

        .alert-icon {
            font-size: 3rem;
            color: #856404;
        }

        .alert-panel.danger .alert-icon { color: #721c24; }
        .alert-panel.success .alert-icon { color: #155724; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #856404;
        }

        .alert-panel.danger .alert-title { color: #721c24; }
        .alert-panel.success .alert-title { color: #155724; }

        .alert-message {
            font-size: 1.1rem;
            line-height: 1.7;
            color: #856404;
        }

        .alert-panel.danger .alert-message { color: #721c24; }
        .alert-panel.success .alert-message { color: #155724; }

        /* 响应式设计 */
        @media (max-width: 1600px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .command-container {
                padding: 15px;
            }

            .main-title {
                font-size: 3rem;
                flex-direction: column;
                gap: 25px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
                padding: 20px 30px;
                flex-direction: row;
                justify-content: center;
            }

            .monitor-grid {
                grid-template-columns: 1fr;
            }

            .system-status {
                grid-template-columns: repeat(2, 1fr);
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 1s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-50px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #0f2027;
            font-size: 1.3rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="command-container">
        <!-- 系统头部 -->
        <div class="command-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-satellite-dish"></i>
                供水调度应急指挥系统
                <i class="fas fa-shield-alt"></i>
            </h1>
            <p class="subtitle">Water Dispatch & Emergency Command System - 智能监测 · 精准调度 · 快速响应 · 高效指挥</p>
            
            <div class="system-status">
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <div class="status-title">运行监测</div>
                    <div class="status-value">正常</div>
                    <div class="status-desc">156个监测点</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="status-title">预警状态</div>
                    <div class="status-value">3</div>
                    <div class="status-desc">条活跃预警</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-tasks"></i>
                    </div>
                    <div class="status-title">调度工单</div>
                    <div class="status-value">8</div>
                    <div class="status-desc">个待处理</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-user-shield"></i>
                    </div>
                    <div class="status-title">值班状态</div>
                    <div class="status-value">在岗</div>
                    <div class="status-desc">24小时值守</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-toolbox"></i>
                    </div>
                    <div class="status-title">应急资源</div>
                    <div class="status-value">就绪</div>
                    <div class="status-desc">8支队伍</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">
                        <i class="fas fa-fire-extinguisher"></i>
                    </div>
                    <div class="status-title">应急事件</div>
                    <div class="status-value">1</div>
                    <div class="status-desc">起处置中</div>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="operation-monitoring">
                <i class="nav-icon fas fa-eye"></i>
                <span>运行监测</span>
            </button>
            <button class="nav-tab" data-tab="operation-warning">
                <i class="nav-icon fas fa-exclamation-triangle"></i>
                <span>运行预警</span>
            </button>
            <button class="nav-tab" data-tab="dispatch-management">
                <i class="nav-icon fas fa-tasks"></i>
                <span>调度管理</span>
            </button>
            <button class="nav-tab" data-tab="duty-guard">
                <i class="nav-icon fas fa-user-shield"></i>
                <span>值班值守</span>
            </button>
            <button class="nav-tab" data-tab="emergency-resource">
                <i class="nav-icon fas fa-toolbox"></i>
                <span>应急资源</span>
            </button>
            <button class="nav-tab" data-tab="emergency-event">
                <i class="nav-icon fas fa-fire-extinguisher"></i>
                <span>应急事件</span>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 运行监测 -->
            <div id="operation-monitoring" class="tab-content active">
                <h2 class="module-title">
                    <i class="fas fa-eye"></i>
                    运行监测管理
                </h2>

                <!-- 监测状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">系统运行状态良好</div>
                        <div class="alert-message">全市供水系统运行平稳，156个监测点数据正常，各项运行指标均在安全范围内，系统稳定高效运作</div>
                    </div>
                </div>

                <!-- 监测指标 -->
                <div class="monitor-grid">
                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-tint"></i>
                                供水总量
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="monitor-value">
                            42,580
                            <span class="monitor-unit">m³/日</span>
                        </div>
                        <div class="monitor-description">
                            日供水量稳定，较昨日增长2.3%，满足城市用水需求
                        </div>
                    </div>

                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-gauge"></i>
                                管网压力
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                稳定
                            </div>
                        </div>
                        <div class="monitor-value">
                            0.58
                            <span class="monitor-unit">MPa</span>
                        </div>
                        <div class="monitor-description">
                            平均管网压力保持稳定，各监测点压力均在正常范围
                        </div>
                    </div>

                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-flask"></i>
                                水质指标
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                达标
                            </div>
                        </div>
                        <div class="monitor-value">
                            99.8
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            水质达标率99.8%，各项指标符合国家饮用水标准
                        </div>
                    </div>

                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-cogs"></i>
                                设备运行
                            </div>
                            <div class="monitor-status status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                关注
                            </div>
                        </div>
                        <div class="monitor-value">
                            98.2
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            设备完好率98.2%，3台设备需要维护，已安排检修
                        </div>
                    </div>

                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-database"></i>
                                数据采集
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="monitor-value">
                            97.5
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            数据采集完整率97.5%，156个监测点实时数据传输正常
                        </div>
                    </div>

                    <div class="monitor-card monitoring">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-chart-line"></i>
                                运行效率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优良
                            </div>
                        </div>
                        <div class="monitor-value">
                            91.8
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            系统运行效率91.8%，能耗控制良好，运行成本优化
                        </div>
                    </div>
                </div>

                <!-- 监测图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                实时运行趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchMonitorPeriod('1h')">1小时</button>
                                <button class="chart-btn" onclick="switchMonitorPeriod('6h')">6小时</button>
                                <button class="chart-btn" onclick="switchMonitorPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchMonitorPeriod('7d')">7天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="monitoringTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                监测点状态分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="monitorStatusChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 监测点数据表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>监测点编号</th>
                            <th>监测点名称</th>
                            <th>监测类型</th>
                            <th>当前数值</th>
                            <th>正常范围</th>
                            <th>数据状态</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>MP-001</td>
                            <td>东区水厂出口</td>
                            <td>流量监测</td>
                            <td class="number">1,850 m³/h</td>
                            <td>1,500-2,000 m³/h</td>
                            <td class="status-normal">正常</td>
                            <td>14:58:30</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>MP-025</td>
                            <td>建设路压力点</td>
                            <td>压力监测</td>
                            <td class="number">0.62 MPa</td>
                            <td>0.35-0.70 MPa</td>
                            <td class="status-normal">正常</td>
                            <td>14:58:28</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>MP-048</td>
                            <td>南区水质监测</td>
                            <td>水质监测</td>
                            <td class="number">0.45 mg/L</td>
                            <td>0.30-1.00 mg/L</td>
                            <td class="status-normal">正常</td>
                            <td>14:58:25</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>MP-072</td>
                            <td>西区泵站监测</td>
                            <td>设备监测</td>
                            <td class="number">1,420 rpm</td>
                            <td>1,400-1,500 rpm</td>
                            <td class="status-warning">预警</td>
                            <td>14:58:20</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>MP-095</td>
                            <td>北区管网监测</td>
                            <td>流量监测</td>
                            <td class="number">2,150 m³/h</td>
                            <td>1,800-2,200 m³/h</td>
                            <td class="status-normal">正常</td>
                            <td>14:58:32</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 运行预警 -->
            <div id="operation-warning" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    运行预警管理
                </h2>

                <!-- 预警状态 -->
                <div class="alert-panel warning">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">当前有3条活跃预警</div>
                        <div class="alert-message">系统检测到西区泵站设备异常、南区管网压力波动、东区水质指标偏高，请及时关注并采取相应措施</div>
                    </div>
                </div>

                <!-- 预警统计 -->
                <div class="monitor-grid">
                    <div class="monitor-card warning">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-bell"></i>
                                活跃预警数量
                            </div>
                            <div class="monitor-status status-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                关注
                            </div>
                        </div>
                        <div class="monitor-value">
                            3
                            <span class="monitor-unit">条</span>
                        </div>
                        <div class="monitor-description">
                            高级预警1条，中级预警1条，低级预警1条
                        </div>
                    </div>

                    <div class="monitor-card warning">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                良好
                            </div>
                        </div>
                        <div class="monitor-value">
                            12
                            <span class="monitor-unit">分钟</span>
                        </div>
                        <div class="monitor-description">
                            预警响应时间持续优化，处置效率显著提升
                        </div>
                    </div>

                    <div class="monitor-card warning">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-percentage"></i>
                                预警准确率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优秀
                            </div>
                        </div>
                        <div class="monitor-value">
                            89.5
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            智能算法持续优化，预警准确性不断提升
                        </div>
                    </div>

                    <div class="monitor-card warning">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-check-circle"></i>
                                处置成功率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优秀
                            </div>
                        </div>
                        <div class="monitor-value">
                            96.2
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            预警处置成功率高，风险控制效果显著
                        </div>
                    </div>
                </div>

                <!-- 预警列表 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>预警编号</th>
                            <th>预警类型</th>
                            <th>预警等级</th>
                            <th>触发时间</th>
                            <th>预警内容</th>
                            <th>影响范围</th>
                            <th>处置状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>WRN-2024-001</td>
                            <td class="status-danger">设备异常</td>
                            <td class="status-danger">高级</td>
                            <td>14:25:30</td>
                            <td>西区泵站3号水泵振动异常</td>
                            <td>影响供水1.2万户</td>
                            <td class="status-warning">处置中</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">立即处置</button></td>
                        </tr>
                        <tr>
                            <td>WRN-2024-002</td>
                            <td class="status-warning">压力异常</td>
                            <td class="status-warning">中级</td>
                            <td>13:45:15</td>
                            <td>南区管网压力持续下降</td>
                            <td>影响供水8千户</td>
                            <td class="status-info">已派工</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>WRN-2024-003</td>
                            <td class="status-info">水质异常</td>
                            <td class="status-info">低级</td>
                            <td>12:30:45</td>
                            <td>东区水质余氯偏高</td>
                            <td>影响供水5千户</td>
                            <td class="status-normal">已处理</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 调度管理 -->
            <div id="dispatch-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-tasks"></i>
                    调度管理
                </h2>

                <!-- 调度状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">调度系统运行正常</div>
                        <div class="alert-message">调度规则运行良好，当前有8个工单待处理，调度效率持续优化，资源配置合理有效</div>
                    </div>
                </div>

                <!-- 调度统计 -->
                <div class="monitor-grid">
                    <div class="monitor-card dispatch">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-clipboard-list"></i>
                                待处理工单
                            </div>
                            <div class="monitor-status status-info">
                                <i class="fas fa-info-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="monitor-value">
                            8
                            <span class="monitor-unit">个</span>
                        </div>
                        <div class="monitor-description">
                            紧急工单2个，重要工单3个，一般工单3个
                        </div>
                    </div>

                    <div class="monitor-card dispatch">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-cogs"></i>
                                调度规则数量
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                完善
                            </div>
                        </div>
                        <div class="monitor-value">
                            25
                            <span class="monitor-unit">条</span>
                        </div>
                        <div class="monitor-description">
                            涵盖各种场景的调度规则，确保资源合理分配
                        </div>
                    </div>

                    <div class="monitor-card dispatch">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-users"></i>
                                调度人员在岗
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                充足
                            </div>
                        </div>
                        <div class="monitor-value">
                            12
                            <span class="monitor-unit">人</span>
                        </div>
                        <div class="monitor-description">
                            调度人员配置充足，24小时轮班制度完善
                        </div>
                    </div>

                    <div class="monitor-card dispatch">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-chart-line"></i>
                                调度效率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优秀
                            </div>
                        </div>
                        <div class="monitor-value">
                            94.8
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            工单处理效率高，平均完成时间缩短15%
                        </div>
                    </div>
                </div>

                <!-- 调度工单表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>工单编号</th>
                            <th>工单类型</th>
                            <th>优先级</th>
                            <th>创建时间</th>
                            <th>工单内容</th>
                            <th>分配人员</th>
                            <th>预计完成</th>
                            <th>工单状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DS-2024-001</td>
                            <td class="status-danger">设备维修</td>
                            <td class="status-danger">紧急</td>
                            <td>14:30</td>
                            <td>西区泵站3号水泵维修</td>
                            <td>张工程师</td>
                            <td>18:00</td>
                            <td class="status-warning">执行中</td>
                        </tr>
                        <tr>
                            <td>DS-2024-002</td>
                            <td class="status-warning">管网检修</td>
                            <td class="status-warning">重要</td>
                            <td>13:45</td>
                            <td>南区管网压力调节</td>
                            <td>李班长</td>
                            <td>16:30</td>
                            <td class="status-info">已分配</td>
                        </tr>
                        <tr>
                            <td>DS-2024-003</td>
                            <td class="status-info">水质调节</td>
                            <td class="status-info">一般</td>
                            <td>12:30</td>
                            <td>东区水质余氯调节</td>
                            <td>王技师</td>
                            <td>15:00</td>
                            <td class="status-normal">已完成</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 值班值守 -->
            <div id="duty-guard" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-user-shield"></i>
                    值班值守管理
                </h2>

                <!-- 值班状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">值班值守正常</div>
                        <div class="alert-message">24小时值班制度运行良好，当前值班人员6人在岗，值班记录完整，应急响应能力充足</div>
                    </div>
                </div>

                <!-- 值班统计 -->
                <div class="monitor-grid">
                    <div class="monitor-card duty">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-user-check"></i>
                                当前值班人员
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                在岗
                            </div>
                        </div>
                        <div class="monitor-value">
                            6
                            <span class="monitor-unit">人</span>
                        </div>
                        <div class="monitor-description">
                            调度员2人，技术员2人，管理员2人
                        </div>
                    </div>

                    <div class="monitor-card duty">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-clock"></i>
                                值班时长
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="monitor-value">
                            8
                            <span class="monitor-unit">小时</span>
                        </div>
                        <div class="monitor-description">
                            三班倒制度，每班8小时，无缝衔接
                        </div>
                    </div>

                    <div class="monitor-card duty">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-clipboard-check"></i>
                                值班记录完整率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                完整
                            </div>
                        </div>
                        <div class="monitor-value">
                            100
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            值班记录详细完整，交接班程序规范
                        </div>
                    </div>

                    <div class="monitor-card duty">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-phone"></i>
                                应急响应能力
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                充足
                            </div>
                        </div>
                        <div class="monitor-value">
                            24/7
                            <span class="monitor-unit">小时</span>
                        </div>
                        <div class="monitor-description">
                            24小时不间断监控，应急响应及时有效
                        </div>
                    </div>
                </div>

                <!-- 值班安排表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>值班时段</th>
                            <th>值班日期</th>
                            <th>值班长</th>
                            <th>调度员</th>
                            <th>技术员</th>
                            <th>值班状态</th>
                            <th>交接状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>白班 08:00-16:00</td>
                            <td>2024-01-15</td>
                            <td>张班长</td>
                            <td>李调度员</td>
                            <td>王技师</td>
                            <td class="status-normal">正常值班</td>
                            <td class="status-normal">已交接</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>中班 16:00-24:00</td>
                            <td>2024-01-15</td>
                            <td>赵班长</td>
                            <td>刘调度员</td>
                            <td>陈技师</td>
                            <td class="status-info">当前值班</td>
                            <td class="status-warning">待交接</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>夜班 00:00-08:00</td>
                            <td>2024-01-16</td>
                            <td>孙班长</td>
                            <td>周调度员</td>
                            <td>吴技师</td>
                            <td class="status-warning">待值班</td>
                            <td class="status-warning">待交接</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 应急资源管理 -->
            <div id="emergency-resource" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-toolbox"></i>
                    应急资源管理
                </h2>

                <!-- 资源状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">应急资源准备充分</div>
                        <div class="alert-message">应急队伍8支全部就绪，应急物资储备充足，应急设备运行正常，应急响应能力强，可随时投入应急处置</div>
                    </div>
                </div>

                <!-- 资源统计 -->
                <div class="monitor-grid">
                    <div class="monitor-card resource">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-users"></i>
                                应急队伍
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                就绪
                            </div>
                        </div>
                        <div class="monitor-value">
                            8
                            <span class="monitor-unit">支</span>
                        </div>
                        <div class="monitor-description">
                            专业应急队伍8支，总计96人，24小时待命
                        </div>
                    </div>

                    <div class="monitor-card resource">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-boxes"></i>
                                应急物资
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                充足
                            </div>
                        </div>
                        <div class="monitor-value">
                            95.8
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            应急物资储备充足，库存管理规范有序
                        </div>
                    </div>

                    <div class="monitor-card resource">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-truck"></i>
                                应急车辆
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                可用
                            </div>
                        </div>
                        <div class="monitor-value">
                            24
                            <span class="monitor-unit">辆</span>
                        </div>
                        <div class="monitor-description">
                            应急车辆24辆，包括抢修车、运输车、指挥车
                        </div>
                    </div>

                    <div class="monitor-card resource">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-tools"></i>
                                应急设备
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="monitor-value">
                            98.5
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            应急设备完好率98.5%，定期维护保养
                        </div>
                    </div>
                </div>

                <!-- 应急资源表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>资源类型</th>
                            <th>资源名称</th>
                            <th>数量/规模</th>
                            <th>存放位置</th>
                            <th>负责人</th>
                            <th>状态</th>
                            <th>最后检查</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>应急队伍</td>
                            <td>A队抢修队</td>
                            <td class="number">12人</td>
                            <td>东区基地</td>
                            <td>张队长</td>
                            <td class="status-normal">就绪</td>
                            <td>2024-01-15</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">调用资源</button></td>
                        </tr>
                        <tr>
                            <td>应急物资</td>
                            <td>管道抢修材料</td>
                            <td class="number">500套</td>
                            <td>中心仓库</td>
                            <td>李管理员</td>
                            <td class="status-normal">充足</td>
                            <td>2024-01-14</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">调用资源</button></td>
                        </tr>
                        <tr>
                            <td>应急车辆</td>
                            <td>抢修车001</td>
                            <td class="number">1辆</td>
                            <td>西区车库</td>
                            <td>王司机</td>
                            <td class="status-normal">可用</td>
                            <td>2024-01-15</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">调用资源</button></td>
                        </tr>
                        <tr>
                            <td>应急设备</td>
                            <td>应急发电机</td>
                            <td class="number">6台</td>
                            <td>设备库房</td>
                            <td>赵技师</td>
                            <td class="status-warning">维护中</td>
                            <td>2024-01-13</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 应急事件管理 -->
            <div id="emergency-event" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-fire-extinguisher"></i>
                    应急事件层级管理
                </h2>

                <!-- 事件状态 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">当前有1起应急事件处置中</div>
                        <div class="alert-message">建设路主管爆管事件（三级响应）正在处置中，已启动应急预案，调配A队和B队进行抢修，预计18:00完成</div>
                    </div>
                </div>

                <!-- 事件统计 -->
                <div class="monitor-grid">
                    <div class="monitor-card emergency">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-exclamation-circle"></i>
                                当前应急事件
                            </div>
                            <div class="monitor-status status-danger">
                                <i class="fas fa-exclamation-triangle"></i>
                                处置中
                            </div>
                        </div>
                        <div class="monitor-value">
                            1
                            <span class="monitor-unit">起</span>
                        </div>
                        <div class="monitor-description">
                            三级响应1起，影响用户2500户，抢修进行中
                        </div>
                    </div>

                    <div class="monitor-card emergency">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优秀
                            </div>
                        </div>
                        <div class="monitor-value">
                            15
                            <span class="monitor-unit">分钟</span>
                        </div>
                        <div class="monitor-description">
                            应急响应时间持续优化，较上月缩短3分钟
                        </div>
                    </div>

                    <div class="monitor-card emergency">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-layer-group"></i>
                                响应层级体系
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                完善
                            </div>
                        </div>
                        <div class="monitor-value">
                            4
                            <span class="monitor-unit">级</span>
                        </div>
                        <div class="monitor-description">
                            一级至四级响应体系完善，分级处置高效
                        </div>
                    </div>

                    <div class="monitor-card emergency">
                        <div class="monitor-header">
                            <div class="monitor-title">
                                <i class="fas fa-percentage"></i>
                                处置成功率
                            </div>
                            <div class="monitor-status status-normal">
                                <i class="fas fa-check-circle"></i>
                                优秀
                            </div>
                        </div>
                        <div class="monitor-value">
                            98.5
                            <span class="monitor-unit">%</span>
                        </div>
                        <div class="monitor-description">
                            应急事件处置成功率高，损失控制在最小范围
                        </div>
                    </div>
                </div>

                <!-- 应急事件表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>事件编号</th>
                            <th>事件类型</th>
                            <th>响应等级</th>
                            <th>发生时间</th>
                            <th>事件描述</th>
                            <th>影响范围</th>
                            <th>处置状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>EMG-2024-001</td>
                            <td class="status-danger">管道爆管</td>
                            <td class="status-warning">三级响应</td>
                            <td>14:15</td>
                            <td>建设路DN400主管爆管</td>
                            <td>2500户</td>
                            <td class="status-warning">抢修中</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">实时跟踪</button></td>
                        </tr>
                        <tr>
                            <td>EMG-2024-002</td>
                            <td class="status-warning">水质异常</td>
                            <td class="status-info">四级响应</td>
                            <td>昨日 16:30</td>
                            <td>东区水厂出水浊度偏高</td>
                            <td>1200户</td>
                            <td class="status-normal">已处置</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>EMG-2024-003</td>
                            <td class="status-info">设备故障</td>
                            <td class="status-info">四级响应</td>
                            <td>昨日 10:15</td>
                            <td>南区泵站2号水泵故障</td>
                            <td>800户</td>
                            <td class="status-normal">已处置</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>

                <!-- 应急响应层级说明 -->
                <div style="margin-top: 40px;">
                    <h3 style="font-size: 1.5rem; color: #0f2027; margin-bottom: 25px;">
                        <i class="fas fa-info-circle"></i>
                        应急响应层级说明
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 25px;">
                        <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #dc3545;">
                            <h4 style="color: #dc3545; margin-bottom: 15px;">
                                <i class="fas fa-exclamation-triangle"></i>
                                一级响应（特别重大）
                            </h4>
                            <p style="color: #666; line-height: 1.6;">影响全市供水安全，需要市政府统一指挥，调动全部应急资源</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #ff9800;">
                            <h4 style="color: #ff9800; margin-bottom: 15px;">
                                <i class="fas fa-exclamation-circle"></i>
                                二级响应（重大）
                            </h4>
                            <p style="color: #666; line-height: 1.6;">影响区域供水，需要公司级统一指挥，调动多个应急队伍</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #2196f3;">
                            <h4 style="color: #2196f3; margin-bottom: 15px;">
                                <i class="fas fa-info-circle"></i>
                                三级响应（较大）
                            </h4>
                            <p style="color: #666; line-height: 1.6;">影响局部供水，调度中心指挥，调动1-2个应急队伍处置</p>
                        </div>
                        <div style="background: #f8f9fa; padding: 25px; border-radius: 15px; border-left: 5px solid #4caf50;">
                            <h4 style="color: #4caf50; margin-bottom: 15px;">
                                <i class="fas fa-check-circle"></i>
                                四级响应（一般）
                            </h4>
                            <p style="color: #666; line-height: 1.6;">影响范围有限，现场负责人指挥，调动单个应急队伍处置</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'operation-monitoring';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('供水调度应急指挥系统初始化...');
            
            // 初始化导航事件
            initNavEvents();
            
            // 初始化监测图表
            initMonitoringCharts();
            
            console.log('系统初始化完成');
        });

        // 初始化导航事件
        function initNavEvents() {
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            } else {
                // 如果内容不存在，动态加载
                loadTabContent(tabId);
            }
            
            // 激活选中导航
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
        }

        // 动态加载标签页内容
        function loadTabContent(tabId) {
            const contentArea = document.querySelector('.content-area');
            
            // 显示加载动画
            contentArea.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    正在加载${getTabName(tabId)}模块...
                </div>
            `;
            
            // 模拟加载延迟
            setTimeout(() => {
                generateTabContent(tabId);
            }, 1000);
        }

        // 获取标签页名称
        function getTabName(tabId) {
            const names = {
                'operation-monitoring': '运行监测',
                'operation-warning': '运行预警',
                'dispatch-management': '调度管理',
                'duty-guard': '值班值守',
                'emergency-resource': '应急资源',
                'emergency-event': '应急事件'
            };
            return names[tabId] || '未知模块';
        }

        // 生成标签页内容
        function generateTabContent(tabId) {
            // 内容将在后续添加，这里先显示占位内容
            const contentArea = document.querySelector('.content-area');
            contentArea.innerHTML = `
                <div id="${tabId}" class="tab-content active">
                    <h2 class="module-title">
                        <i class="fas fa-cogs"></i>
                        ${getTabName(tabId)}模块
                    </h2>
                    <div class="alert-panel success">
                        <i class="fas fa-check-circle alert-icon"></i>
                        <div class="alert-content">
                            <div class="alert-title">模块加载成功</div>
                            <div class="alert-message">${getTabName(tabId)}模块已成功加载，详细功能正在开发中！</div>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="showNotification('功能开发中...', 'info')">
                            <i class="fas fa-cogs"></i>
                            功能配置
                        </button>
                        <button class="btn btn-success" onclick="showNotification('数据分析功能开发中...', 'info')">
                            <i class="fas fa-chart-bar"></i>
                            数据分析
                        </button>
                        <button class="btn btn-warning" onclick="showNotification('报告导出功能开发中...', 'info')">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>
            `;
        }

        // 初始化监测图表
        function initMonitoringCharts() {
            initMonitoringTrendChart();
            initMonitorStatusChart();
        }

        // 监测趋势图表
        function initMonitoringTrendChart() {
            const ctx = document.getElementById('monitoringTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            charts.monitoringTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['14:00', '14:10', '14:20', '14:30', '14:40', '14:50', '15:00'],
                    datasets: [{
                        label: '供水量 (m³/h)',
                        data: [1800, 1850, 1820, 1880, 1900, 1850, 1870],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '管网压力 (MPa)',
                        data: [0.56, 0.58, 0.57, 0.59, 0.60, 0.58, 0.59],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '供水量 (m³/h)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '压力 (MPa)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // 监测点状态图表
        function initMonitorStatusChart() {
            const ctx = document.getElementById('monitorStatusChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            charts.monitorStatus = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['正常运行', '需要关注', '离线状态', '维护中'],
                    datasets: [{
                        data: [142, 8, 4, 2],
                        backgroundColor: ['#4caf50', '#ff9800', '#f44336', '#9e9e9e'],
                        borderWidth: 4,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 监测周期切换
        function switchMonitorPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const periodNames = {
                '1h': '1小时',
                '6h': '6小时',
                '24h': '24小时',
                '7d': '7天'
            };
            
            showNotification(`已切换到${periodNames[period]}监测视图`, 'info');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 18px 25px;
                border-radius: 15px;
                z-index: 10000;
                box-shadow: 0 12px 35px rgba(0,0,0,0.15);
                max-width: 400px;
                animation: slideInRight 0.5s ease-out;
                font-weight: 700;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 12px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.5s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 4000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
