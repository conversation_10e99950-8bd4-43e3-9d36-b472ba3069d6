<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Element Field Visualization Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a1f2e 100%);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .dashboard-container {
            max-width: 1920px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Header Styles */
        .header {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #64b5f6, #42a5f5, #2196f3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #b0bec5;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .kpi-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .kpi-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .kpi-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .kpi-value {
            font-size: 2.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .kpi-label {
            color: #90a4ae;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .kpi-change {
            font-size: 0.8rem;
            margin-top: 5px;
        }

        .positive { color: #4caf50; }
        .negative { color: #f44336; }

        /* Main Grid Layout */
        .main-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 400px 350px;
            gap: 25px;
            margin-bottom: 25px;
        }

        .panel {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .panel h3 {
            font-size: 1.3rem;
            margin-bottom: 20px;
            color: #e3f2fd;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-icon {
            width: 24px;
            height: 24px;
            background: linear-gradient(45deg, #2196f3, #21cbf3);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        /* Spatial Visualization */
        .spatial-panel {
            grid-column: 1;
            grid-row: 1;
        }

        #spatial-viz {
            width: 100%;
            height: 300px;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.2);
        }

        /* Time Series Panel */
        .timeseries-panel {
            grid-column: 2;
            grid-row: 1;
        }

        #timeseriesChart {
            width: 100%;
            height: 300px;
        }

        /* Real-time Metrics */
        .metrics-panel {
            grid-column: 1;
            grid-row: 2;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            height: 250px;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.8rem;
            color: #90a4ae;
            text-align: center;
        }

        /* Flow Diagram */
        .flow-panel {
            grid-column: 2;
            grid-row: 2;
        }

        #flow-diagram {
            width: 100%;
            height: 250px;
        }

        /* Controls */
        .controls {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .control-group label {
            font-size: 0.9rem;
            color: #90a4ae;
        }

        select, input[type="range"] {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            padding: 8px 12px;
            color: #ffffff;
            font-size: 0.9rem;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #2196f3;
        }

        /* Footer */
        .footer {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
            color: #90a4ae;
            font-size: 0.9rem;
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .main-grid {
                grid-template-columns: 1fr;
                grid-template-rows: repeat(4, 400px);
            }
            
            .spatial-panel { grid-column: 1; grid-row: 1; }
            .timeseries-panel { grid-column: 1; grid-row: 2; }
            .metrics-panel { grid-column: 1; grid-row: 3; }
            .flow-panel { grid-column: 1; grid-row: 4; }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .kpi-container {
                grid-template-columns: 1fr 1fr;
            }
            
            .controls {
                flex-direction: column;
                align-items: stretch;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        /* Loading States */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Scrollbar Styling */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* Enhanced Focus States */
        select:focus, input:focus {
            outline: none;
            border-color: #2196f3;
            box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
        }

        /* Improved Button Styling for Controls */
        .control-group {
            position: relative;
        }

        .control-group::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .control-group:hover::before {
            opacity: 1;
        }

        /* Enhanced Panel Styling */
        .panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #2196f3, #21cbf3, #2196f3);
            border-radius: 15px 15px 0 0;
            opacity: 0.6;
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-online { background-color: #4caf50; }
        .status-warning { background-color: #ff9800; }
        .status-error { background-color: #f44336; }

        /* Tooltip Enhancements */
        .tooltip, .flow-tooltip {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Print Styles */
        @media print {
            body {
                background: white;
                color: black;
            }

            .controls {
                display: none;
            }

            .panel {
                background: white;
                border: 1px solid #ccc;
                break-inside: avoid;
            }
        }

        /* High Contrast Mode Support */
        @media (prefers-contrast: high) {
            .panel {
                border: 2px solid #ffffff;
            }

            .kpi-card {
                border: 2px solid #ffffff;
            }
        }

        /* Reduced Motion Support */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Dark Mode Enhancements */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #0a0e13 0%, #1a1f2e 100%);
            }
        }

        /* Additional Mobile Optimizations */
        @media (max-width: 480px) {
            .dashboard-container {
                padding: 10px;
            }

            .header h1 {
                font-size: 1.5rem;
            }

            .kpi-container {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .kpi-card {
                padding: 15px;
            }

            .main-grid {
                gap: 15px;
            }

            .panel {
                padding: 15px;
            }

            .controls {
                padding: 15px;
                gap: 15px;
            }
        }

        /* Accessibility Improvements */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Focus Visible for Better Keyboard Navigation */
        .kpi-card:focus-visible,
        .panel:focus-visible {
            outline: 2px solid #2196f3;
            outline-offset: 2px;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Header Section -->
        <header class="header fade-in">
            <h1>Data Element Field Visualization Dashboard</h1>
            <p>Real-time monitoring of data element value creation across spatiotemporal contexts</p>
            
            <div class="kpi-container">
                <div class="kpi-card" tabindex="0" role="button" aria-label="Total Data Value: $2.4M, up 12.5%">
                    <div class="status-indicator status-online" aria-hidden="true"></div>
                    <div class="kpi-value" style="color: #4caf50;">$2.4M</div>
                    <div class="kpi-label">Total Data Value</div>
                    <div class="kpi-change positive">+12.5% ↗</div>
                </div>
                <div class="kpi-card" tabindex="0" role="button" aria-label="Active Processes: 1,247, up 8.3%">
                    <div class="status-indicator status-online" aria-hidden="true"></div>
                    <div class="kpi-value" style="color: #2196f3;">1,247</div>
                    <div class="kpi-label">Active Processes</div>
                    <div class="kpi-change positive">+8.3% ↗</div>
                </div>
                <div class="kpi-card" tabindex="0" role="button" aria-label="Efficiency Rate: 94.2%, down 2.1%">
                    <div class="status-indicator status-warning" aria-hidden="true"></div>
                    <div class="kpi-value" style="color: #ff9800;">94.2%</div>
                    <div class="kpi-label">Efficiency Rate</div>
                    <div class="kpi-change negative">-2.1% ↘</div>
                </div>
                <div class="kpi-card" tabindex="0" role="button" aria-label="Data Sources: 156, up 5.7%">
                    <div class="status-indicator status-online" aria-hidden="true"></div>
                    <div class="kpi-value" style="color: #9c27b0;">156</div>
                    <div class="kpi-label">Data Sources</div>
                    <div class="kpi-change positive">+5.7% ↗</div>
                </div>
            </div>
        </header>

        <!-- Controls -->
        <div class="controls fade-in" role="toolbar" aria-label="Dashboard Controls">
            <div class="control-group">
                <label for="timeRange">Time Range</label>
                <select id="timeRange" aria-describedby="timeRange-desc">
                    <option value="1h">Last Hour</option>
                    <option value="24h" selected>Last 24 Hours</option>
                    <option value="7d">Last 7 Days</option>
                    <option value="30d">Last 30 Days</option>
                </select>
                <div id="timeRange-desc" class="sr-only">Select the time range for data visualization</div>
            </div>
            <div class="control-group">
                <label for="regionFilter">Region</label>
                <select id="regionFilter" aria-describedby="region-desc">
                    <option value="all" selected>All Regions</option>
                    <option value="north-america">North America</option>
                    <option value="europe">Europe</option>
                    <option value="asia-pacific">Asia Pacific</option>
                </select>
                <div id="region-desc" class="sr-only">Filter data by geographic region</div>
            </div>
            <div class="control-group">
                <label for="processFilter">Process Type</label>
                <select id="processFilter" aria-describedby="process-desc">
                    <option value="all" selected>All Processes</option>
                    <option value="computation">Computation</option>
                    <option value="analytics">Analytics</option>
                    <option value="ml">Machine Learning</option>
                </select>
                <div id="process-desc" class="sr-only">Filter by type of data processing</div>
            </div>
            <div class="control-group">
                <label for="intensitySlider">Data Intensity</label>
                <input type="range" id="intensitySlider" min="0" max="100" value="50"
                       aria-describedby="intensity-desc" aria-valuetext="50%">
                <div id="intensity-desc" class="sr-only">Adjust data visualization intensity from 0% to 100%</div>
            </div>
        </div>

        <!-- Main Visualization Grid -->
        <main class="main-grid fade-in" role="main" aria-label="Data Visualization Dashboard">
            <!-- Spatial Visualization Panel -->
            <section class="panel spatial-panel" role="img" aria-labelledby="spatial-title" tabindex="0">
                <h3 id="spatial-title"><div class="panel-icon" aria-hidden="true">🗺</div>Spatial Data Distribution</h3>
                <div id="spatial-viz" aria-label="Interactive map showing data element distribution across regions"></div>
                <div class="sr-only">
                    Interactive spatial visualization showing data value distribution across 6 global regions.
                    North America: $2.4K, Europe: $1.8K, Asia Pacific: $3.2K, South America: $900,
                    Africa: $600, Middle East: $1.1K. Click regions for detailed information.
                </div>
            </section>

            <!-- Time Series Panel -->
            <section class="panel timeseries-panel" role="img" aria-labelledby="timeseries-title" tabindex="0">
                <h3 id="timeseries-title"><div class="panel-icon" aria-hidden="true">📈</div>Value Creation Timeline</h3>
                <canvas id="timeseriesChart" aria-label="Time series chart showing value creation, processing volume, and efficiency over time"></canvas>
                <div class="sr-only">
                    Time series chart displaying three metrics over the last 24 hours:
                    Value Creation ranging from $80K to $120K, Processing Volume from 65GB to 95GB,
                    and Efficiency from 75% to 95%. Current trends show stable performance with minor fluctuations.
                </div>
            </section>

            <!-- Real-time Metrics Panel -->
            <section class="panel metrics-panel" role="region" aria-labelledby="metrics-title" tabindex="0">
                <h3 id="metrics-title"><div class="panel-icon" aria-hidden="true">⚡</div>Real-time Metrics</h3>
                <div class="metrics-grid" role="group" aria-label="Live performance metrics">
                    <div class="metric-item" role="status" aria-live="polite" aria-label="Data Throughput">
                        <div class="metric-value pulse" style="color: #4caf50;" id="throughput">2.4K</div>
                        <div class="metric-label">Data Throughput<br>(GB/sec)</div>
                    </div>
                    <div class="metric-item" role="status" aria-live="polite" aria-label="Processing Latency">
                        <div class="metric-value pulse" style="color: #2196f3;" id="latency">12ms</div>
                        <div class="metric-label">Avg Processing<br>Latency</div>
                    </div>
                    <div class="metric-item" role="status" aria-live="polite" aria-label="Resource Utilization">
                        <div class="metric-value pulse" style="color: #ff9800;" id="utilization">87%</div>
                        <div class="metric-label">Resource<br>Utilization</div>
                    </div>
                    <div class="metric-item" role="status" aria-live="polite" aria-label="Data Quality Score">
                        <div class="metric-value pulse" style="color: #9c27b0;" id="quality">99.1%</div>
                        <div class="metric-label">Data Quality<br>Score</div>
                    </div>
                </div>
            </section>

            <!-- Flow Diagram Panel -->
            <section class="panel flow-panel" role="img" aria-labelledby="flow-title" tabindex="0">
                <h3 id="flow-title"><div class="panel-icon" aria-hidden="true">🔄</div>Data Flow Processes</h3>
                <div id="flow-diagram" aria-label="Interactive flow diagram showing data processing pipeline"></div>
                <div class="sr-only">
                    Data flow diagram showing the processing pipeline: Data Sources → Data Ingestion →
                    Processing → Analytics/ML Models → Business Value. Each node shows current status
                    and performance metrics. Click nodes for detailed information.
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer fade-in" role="contentinfo">
            <p>
                <span class="status-indicator status-online" aria-label="System Online"></span>
                Data sources: Production Systems, Analytics Engines, ML Pipelines |
                Last updated: <span id="lastUpdated"></span> |
                Refresh rate: 5 seconds |
                <span style="margin-left: 20px;">
                    <kbd>Ctrl+R</kbd> Refresh | <kbd>Ctrl+F</kbd> Fullscreen
                </span>
            </p>
        </footer>
    </div>

    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            updateTimestamp();
            startRealTimeUpdates();
            setInterval(updateTimestamp, 1000);
        });

        function initializeDashboard() {
            createSpatialVisualization();
            createTimeSeriesChart();
            createFlowDiagram();
            setupEventListeners();

            // Initialize intensity display
            updateIntensityDisplay(document.getElementById('intensitySlider').value);

            // Add welcome notification
            setTimeout(() => {
                showNotification('Dashboard initialized successfully');
            }, 1000);
        }

        function updateTimestamp() {
            document.getElementById('lastUpdated').textContent = new Date().toLocaleString();
        }

        // Spatial Visualization Implementation
        function createSpatialVisualization() {
            const container = d3.select('#spatial-viz');
            const width = container.node().getBoundingClientRect().width;
            const height = 300;

            // Clear any existing content
            container.selectAll('*').remove();

            const svg = container.append('svg')
                .attr('width', width)
                .attr('height', height);

            // Sample data for regions with data element values
            const regions = [
                { name: 'North America', x: 100, y: 80, value: 2400, processes: 450, color: '#4caf50' },
                { name: 'Europe', x: 250, y: 60, value: 1800, processes: 320, color: '#2196f3' },
                { name: 'Asia Pacific', x: 400, y: 100, value: 3200, processes: 580, color: '#ff9800' },
                { name: 'South America', x: 150, y: 180, value: 900, processes: 180, color: '#9c27b0' },
                { name: 'Africa', x: 280, y: 160, value: 600, processes: 120, color: '#f44336' },
                { name: 'Middle East', x: 320, y: 120, value: 1100, processes: 200, color: '#00bcd4' }
            ];

            // Create tooltip
            const tooltip = d3.select('body').append('div')
                .attr('class', 'tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0, 0, 0, 0.8)')
                .style('color', 'white')
                .style('padding', '10px')
                .style('border-radius', '5px')
                .style('pointer-events', 'none')
                .style('opacity', 0)
                .style('font-size', '12px')
                .style('z-index', '1000');

            // Create background grid
            const gridSize = 20;
            for (let i = 0; i < width; i += gridSize) {
                for (let j = 0; j < height; j += gridSize) {
                    svg.append('rect')
                        .attr('x', i)
                        .attr('y', j)
                        .attr('width', gridSize)
                        .attr('height', gridSize)
                        .attr('fill', 'rgba(255, 255, 255, 0.02)')
                        .attr('stroke', 'rgba(255, 255, 255, 0.05)')
                        .attr('stroke-width', 0.5);
                }
            }

            // Create data flow connections
            const connections = [
                { source: regions[0], target: regions[1] },
                { source: regions[1], target: regions[2] },
                { source: regions[0], target: regions[3] },
                { source: regions[2], target: regions[4] },
                { source: regions[1], target: regions[5] }
            ];

            // Draw connections
            svg.selectAll('.connection')
                .data(connections)
                .enter()
                .append('line')
                .attr('class', 'connection')
                .attr('x1', d => d.source.x)
                .attr('y1', d => d.source.y)
                .attr('x2', d => d.target.x)
                .attr('y2', d => d.target.y)
                .attr('stroke', 'rgba(100, 181, 246, 0.3)')
                .attr('stroke-width', 2)
                .attr('stroke-dasharray', '5,5')
                .style('animation', 'dash 2s linear infinite');

            // Add CSS for animated dashes
            const style = document.createElement('style');
            style.textContent = `
                @keyframes dash {
                    to { stroke-dashoffset: -10; }
                }
            `;
            document.head.appendChild(style);

            // Create region circles
            const regionGroups = svg.selectAll('.region')
                .data(regions)
                .enter()
                .append('g')
                .attr('class', 'region')
                .attr('transform', d => `translate(${d.x}, ${d.y})`);

            // Add outer glow circles
            regionGroups.append('circle')
                .attr('r', d => Math.sqrt(d.value) / 8 + 15)
                .attr('fill', d => d.color)
                .attr('opacity', 0.2)
                .style('filter', 'blur(3px)');

            // Add main circles
            regionGroups.append('circle')
                .attr('r', d => Math.sqrt(d.value) / 10 + 8)
                .attr('fill', d => d.color)
                .attr('opacity', 0.8)
                .attr('stroke', d => d.color)
                .attr('stroke-width', 2)
                .style('cursor', 'pointer')
                .on('mouseover', function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr('r', Math.sqrt(d.value) / 10 + 12)
                        .attr('opacity', 1);

                    tooltip.transition()
                        .duration(200)
                        .style('opacity', 1);

                    tooltip.html(`
                        <strong>${d.name}</strong><br/>
                        Data Value: $${(d.value / 1000).toFixed(1)}K<br/>
                        Active Processes: ${d.processes}<br/>
                        Efficiency: ${(85 + Math.random() * 10).toFixed(1)}%
                    `)
                        .style('left', (event.pageX + 10) + 'px')
                        .style('top', (event.pageY - 10) + 'px');
                })
                .on('mouseout', function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr('r', Math.sqrt(d.value) / 10 + 8)
                        .attr('opacity', 0.8);

                    tooltip.transition()
                        .duration(200)
                        .style('opacity', 0);
                })
                .on('click', function(event, d) {
                    // Simulate region selection
                    document.getElementById('regionFilter').value = d.name.toLowerCase().replace(' ', '-');
                    updateVisualizationsForRegion(d.name);
                });

            // Add region labels
            regionGroups.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '0.35em')
                .attr('fill', 'white')
                .attr('font-size', '10px')
                .attr('font-weight', 'bold')
                .text(d => d.name.split(' ')[0])
                .style('pointer-events', 'none');

            // Add value labels
            regionGroups.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '1.5em')
                .attr('fill', 'rgba(255, 255, 255, 0.7)')
                .attr('font-size', '8px')
                .text(d => `$${(d.value / 1000).toFixed(1)}K`)
                .style('pointer-events', 'none');

            // Add pulsing animation to active regions
            regionGroups.selectAll('circle')
                .filter((d, i) => i === 1) // Only the main circles
                .style('animation', 'pulse 3s ease-in-out infinite');
        }

        function createTimeSeriesChart() {
            const ctx = document.getElementById('timeseriesChart').getContext('2d');

            // Generate sample time series data
            const now = new Date();
            const labels = [];
            const dataValueCreation = [];
            const dataProcessingVolume = [];
            const dataEfficiency = [];

            // Generate 24 hours of data points
            for (let i = 23; i >= 0; i--) {
                const time = new Date(now.getTime() - i * 60 * 60 * 1000);
                labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));

                // Simulate realistic data patterns
                const baseValue = 100 + Math.sin(i * 0.3) * 20;
                const noise = (Math.random() - 0.5) * 10;

                dataValueCreation.push(Math.max(0, baseValue + noise));
                dataProcessingVolume.push(Math.max(0, 80 + Math.sin(i * 0.2) * 15 + noise * 0.5));
                dataEfficiency.push(Math.max(70, Math.min(100, 85 + Math.sin(i * 0.4) * 8 + noise * 0.3)));
            }

            // Create gradient backgrounds
            const gradient1 = ctx.createLinearGradient(0, 0, 0, 300);
            gradient1.addColorStop(0, 'rgba(76, 175, 80, 0.3)');
            gradient1.addColorStop(1, 'rgba(76, 175, 80, 0.05)');

            const gradient2 = ctx.createLinearGradient(0, 0, 0, 300);
            gradient2.addColorStop(0, 'rgba(33, 150, 243, 0.3)');
            gradient2.addColorStop(1, 'rgba(33, 150, 243, 0.05)');

            const gradient3 = ctx.createLinearGradient(0, 0, 0, 300);
            gradient3.addColorStop(0, 'rgba(255, 152, 0, 0.3)');
            gradient3.addColorStop(1, 'rgba(255, 152, 0, 0.05)');

            window.timeseriesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Value Creation ($K)',
                        data: dataValueCreation,
                        borderColor: '#4caf50',
                        backgroundColor: gradient1,
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: '#4caf50',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 2
                    }, {
                        label: 'Processing Volume (GB)',
                        data: dataProcessingVolume,
                        borderColor: '#2196f3',
                        backgroundColor: gradient2,
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: '#2196f3',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 2
                    }, {
                        label: 'Efficiency (%)',
                        data: dataEfficiency,
                        borderColor: '#ff9800',
                        backgroundColor: gradient3,
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 6,
                        pointHoverBackgroundColor: '#ff9800',
                        pointHoverBorderColor: '#ffffff',
                        pointHoverBorderWidth: 2,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top',
                            labels: {
                                color: '#ffffff',
                                font: {
                                    size: 11
                                },
                                usePointStyle: true,
                                pointStyle: 'circle'
                            }
                        },
                        tooltip: {
                            backgroundColor: 'rgba(0, 0, 0, 0.8)',
                            titleColor: '#ffffff',
                            bodyColor: '#ffffff',
                            borderColor: 'rgba(255, 255, 255, 0.2)',
                            borderWidth: 1,
                            cornerRadius: 8,
                            displayColors: true,
                            callbacks: {
                                title: function(context) {
                                    return 'Time: ' + context[0].label;
                                },
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.dataset.label.includes('$')) {
                                        label += '$' + context.parsed.y.toFixed(1) + 'K';
                                    } else if (context.dataset.label.includes('%')) {
                                        label += context.parsed.y.toFixed(1) + '%';
                                    } else {
                                        label += context.parsed.y.toFixed(1) + ' GB';
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#90a4ae',
                                font: {
                                    size: 10
                                },
                                maxTicksLimit: 8
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            grid: {
                                color: 'rgba(255, 255, 255, 0.1)',
                                drawBorder: false
                            },
                            ticks: {
                                color: '#90a4ae',
                                font: {
                                    size: 10
                                },
                                callback: function(value) {
                                    return value.toFixed(0);
                                }
                            },
                            title: {
                                display: true,
                                text: 'Value ($K) / Volume (GB)',
                                color: '#90a4ae',
                                font: {
                                    size: 10
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            min: 70,
                            max: 100,
                            grid: {
                                drawOnChartArea: false,
                                color: 'rgba(255, 152, 0, 0.2)'
                            },
                            ticks: {
                                color: '#ff9800',
                                font: {
                                    size: 10
                                },
                                callback: function(value) {
                                    return value.toFixed(0) + '%';
                                }
                            },
                            title: {
                                display: true,
                                text: 'Efficiency (%)',
                                color: '#ff9800',
                                font: {
                                    size: 10
                                }
                            }
                        }
                    },
                    animation: {
                        duration: 1000,
                        easing: 'easeInOutQuart'
                    }
                }
            });
        }

        function createFlowDiagram() {
            const container = d3.select('#flow-diagram');
            const width = container.node().getBoundingClientRect().width;
            const height = 250;

            // Clear any existing content
            container.selectAll('*').remove();

            const svg = container.append('svg')
                .attr('width', width)
                .attr('height', height);

            // Define the data flow process nodes
            const nodes = [
                { id: 'data-source', name: 'Data Sources', x: 50, y: 125, type: 'source', color: '#4caf50' },
                { id: 'ingestion', name: 'Data Ingestion', x: 150, y: 80, type: 'process', color: '#2196f3' },
                { id: 'processing', name: 'Processing', x: 250, y: 125, type: 'process', color: '#ff9800' },
                { id: 'analytics', name: 'Analytics', x: 150, y: 170, type: 'process', color: '#9c27b0' },
                { id: 'ml-models', name: 'ML Models', x: 350, y: 80, type: 'process', color: '#f44336' },
                { id: 'business-value', name: 'Business Value', x: 450, y: 125, type: 'output', color: '#00bcd4' }
            ];

            // Define the flow connections
            const links = [
                { source: 'data-source', target: 'ingestion', value: 100 },
                { source: 'ingestion', target: 'processing', value: 85 },
                { source: 'processing', target: 'analytics', value: 60 },
                { source: 'processing', target: 'ml-models', value: 40 },
                { source: 'analytics', target: 'business-value', value: 45 },
                { source: 'ml-models', target: 'business-value', value: 35 }
            ];

            // Create node lookup for links
            const nodeMap = new Map(nodes.map(d => [d.id, d]));

            // Create tooltip for flow diagram
            const flowTooltip = d3.select('body').append('div')
                .attr('class', 'flow-tooltip')
                .style('position', 'absolute')
                .style('background', 'rgba(0, 0, 0, 0.9)')
                .style('color', 'white')
                .style('padding', '8px 12px')
                .style('border-radius', '6px')
                .style('pointer-events', 'none')
                .style('opacity', 0)
                .style('font-size', '11px')
                .style('z-index', '1001');

            // Draw flow lines with animation
            const linkElements = svg.selectAll('.flow-link')
                .data(links)
                .enter()
                .append('line')
                .attr('class', 'flow-link')
                .attr('x1', d => nodeMap.get(d.source).x + 20)
                .attr('y1', d => nodeMap.get(d.source).y)
                .attr('x2', d => nodeMap.get(d.target).x - 20)
                .attr('y2', d => nodeMap.get(d.target).y)
                .attr('stroke', '#64b5f6')
                .attr('stroke-width', d => Math.max(1, d.value / 20))
                .attr('opacity', 0.6)
                .attr('marker-end', 'url(#arrowhead)');

            // Add arrowhead marker
            svg.append('defs').append('marker')
                .attr('id', 'arrowhead')
                .attr('viewBox', '0 -5 10 10')
                .attr('refX', 8)
                .attr('refY', 0)
                .attr('markerWidth', 6)
                .attr('markerHeight', 6)
                .attr('orient', 'auto')
                .append('path')
                .attr('d', 'M0,-5L10,0L0,5')
                .attr('fill', '#64b5f6');

            // Create animated flow particles
            function createFlowParticles() {
                links.forEach((link, index) => {
                    const sourceNode = nodeMap.get(link.source);
                    const targetNode = nodeMap.get(link.target);

                    // Create multiple particles per link based on value
                    const particleCount = Math.max(1, Math.floor(link.value / 30));

                    for (let i = 0; i < particleCount; i++) {
                        setTimeout(() => {
                            const particle = svg.append('circle')
                                .attr('class', 'flow-particle')
                                .attr('r', 2)
                                .attr('fill', '#64b5f6')
                                .attr('opacity', 0.8)
                                .attr('cx', sourceNode.x + 20)
                                .attr('cy', sourceNode.y);

                            particle.transition()
                                .duration(2000 + Math.random() * 1000)
                                .ease(d3.easeLinear)
                                .attr('cx', targetNode.x - 20)
                                .attr('cy', targetNode.y)
                                .on('end', function() {
                                    d3.select(this).remove();
                                });
                        }, i * 500 + Math.random() * 1000);
                    }
                });
            }

            // Start particle animation and repeat
            createFlowParticles();
            setInterval(createFlowParticles, 3000);

            // Create node groups
            const nodeGroups = svg.selectAll('.flow-node')
                .data(nodes)
                .enter()
                .append('g')
                .attr('class', 'flow-node')
                .attr('transform', d => `translate(${d.x}, ${d.y})`);

            // Add node backgrounds (larger circles for glow effect)
            nodeGroups.append('circle')
                .attr('r', 25)
                .attr('fill', d => d.color)
                .attr('opacity', 0.2)
                .style('filter', 'blur(2px)');

            // Add main node circles
            nodeGroups.append('circle')
                .attr('r', 18)
                .attr('fill', d => d.color)
                .attr('opacity', 0.8)
                .attr('stroke', d => d.color)
                .attr('stroke-width', 2)
                .style('cursor', 'pointer')
                .on('mouseover', function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr('r', 22)
                        .attr('opacity', 1);

                    // Show process details
                    const details = getProcessDetails(d.type, d.name);
                    flowTooltip.transition()
                        .duration(200)
                        .style('opacity', 1);

                    flowTooltip.html(details)
                        .style('left', (event.pageX + 10) + 'px')
                        .style('top', (event.pageY - 10) + 'px');
                })
                .on('mouseout', function(event, d) {
                    d3.select(this)
                        .transition()
                        .duration(200)
                        .attr('r', 18)
                        .attr('opacity', 0.8);

                    flowTooltip.transition()
                        .duration(200)
                        .style('opacity', 0);
                })
                .on('click', function(event, d) {
                    // Highlight connected flows
                    highlightNodeConnections(d.id);
                });

            // Add node icons (simplified text representations)
            nodeGroups.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '0.35em')
                .attr('fill', 'white')
                .attr('font-size', '10px')
                .attr('font-weight', 'bold')
                .text(d => getNodeIcon(d.type))
                .style('pointer-events', 'none');

            // Add node labels
            nodeGroups.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '2.8em')
                .attr('fill', 'rgba(255, 255, 255, 0.9)')
                .attr('font-size', '9px')
                .text(d => d.name)
                .style('pointer-events', 'none');

            function getNodeIcon(type) {
                const icons = {
                    'source': '📊',
                    'process': '⚙️',
                    'output': '💎'
                };
                return icons[type] || '●';
            }

            function getProcessDetails(type, name) {
                const details = {
                    'Data Sources': 'IoT sensors, databases, APIs<br/>Volume: 2.4TB/day<br/>Quality: 99.1%',
                    'Data Ingestion': 'Real-time streaming<br/>Throughput: 50K events/sec<br/>Latency: 12ms avg',
                    'Processing': 'ETL pipelines, validation<br/>CPU: 87% utilization<br/>Memory: 64GB active',
                    'Analytics': 'Statistical analysis, reporting<br/>Queries: 1.2K/hour<br/>Response: 150ms avg',
                    'ML Models': '12 active models<br/>Inference: 500/sec<br/>Accuracy: 94.2%',
                    'Business Value': 'Revenue optimization<br/>Cost savings: $2.4M<br/>Efficiency: +12.5%'
                };
                return details[name] || `${name}<br/>Status: Active<br/>Performance: Optimal`;
            }

            function highlightNodeConnections(nodeId) {
                // Reset all links
                linkElements.attr('opacity', 0.6).attr('stroke-width', d => Math.max(1, d.value / 20));

                // Highlight connected links
                linkElements
                    .filter(d => d.source === nodeId || d.target === nodeId)
                    .attr('opacity', 1)
                    .attr('stroke-width', d => Math.max(2, d.value / 15));

                // Reset after 2 seconds
                setTimeout(() => {
                    linkElements.attr('opacity', 0.6).attr('stroke-width', d => Math.max(1, d.value / 20));
                }, 2000);
            }
        }

        function updateRealTimeMetrics() {
            // Simulate real-time data updates with realistic variations
            const metrics = {
                throughput: {
                    element: document.getElementById('throughput'),
                    baseValue: 2400,
                    unit: 'K',
                    variation: 200
                },
                latency: {
                    element: document.getElementById('latency'),
                    baseValue: 12,
                    unit: 'ms',
                    variation: 3
                },
                utilization: {
                    element: document.getElementById('utilization'),
                    baseValue: 87,
                    unit: '%',
                    variation: 8
                },
                quality: {
                    element: document.getElementById('quality'),
                    baseValue: 99.1,
                    unit: '%',
                    variation: 0.5
                }
            };

            Object.keys(metrics).forEach(key => {
                const metric = metrics[key];
                const variation = (Math.random() - 0.5) * metric.variation;
                let newValue = metric.baseValue + variation;

                // Ensure values stay within realistic bounds
                if (key === 'utilization' || key === 'quality') {
                    newValue = Math.max(0, Math.min(100, newValue));
                } else if (key === 'latency') {
                    newValue = Math.max(1, newValue);
                } else if (key === 'throughput') {
                    newValue = Math.max(0, newValue);
                }

                // Format the display value
                let displayValue;
                if (key === 'throughput') {
                    displayValue = (newValue / 1000).toFixed(1) + metric.unit;
                } else if (key === 'quality') {
                    displayValue = newValue.toFixed(1) + metric.unit;
                } else {
                    displayValue = Math.round(newValue) + metric.unit;
                }

                // Update with smooth animation
                if (metric.element) {
                    metric.element.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        metric.element.textContent = displayValue;
                        metric.element.style.transform = 'scale(1)';
                    }, 150);
                }

                // Update base value for next iteration (trending)
                metric.baseValue = newValue * 0.1 + metric.baseValue * 0.9;
            });

            // Update KPI cards with new values
            updateKPICards();
        }

        function updateKPICards() {
            const kpiCards = document.querySelectorAll('.kpi-card');

            // Simulate KPI updates
            const updates = [
                {
                    selector: '.kpi-card:nth-child(1) .kpi-value',
                    baseValue: 2.4,
                    unit: 'M',
                    prefix: '$'
                },
                {
                    selector: '.kpi-card:nth-child(2) .kpi-value',
                    baseValue: 1247,
                    unit: '',
                    prefix: ''
                },
                {
                    selector: '.kpi-card:nth-child(3) .kpi-value',
                    baseValue: 94.2,
                    unit: '%',
                    prefix: ''
                },
                {
                    selector: '.kpi-card:nth-child(4) .kpi-value',
                    baseValue: 156,
                    unit: '',
                    prefix: ''
                }
            ];

            updates.forEach((update, index) => {
                const element = document.querySelector(update.selector);
                if (element) {
                    const variation = (Math.random() - 0.5) * 0.1;
                    let newValue = update.baseValue * (1 + variation);

                    let displayValue;
                    if (update.unit === 'M') {
                        displayValue = update.prefix + newValue.toFixed(1) + update.unit;
                    } else if (update.unit === '%') {
                        displayValue = newValue.toFixed(1) + update.unit;
                    } else {
                        displayValue = update.prefix + Math.round(newValue) + update.unit;
                    }

                    element.textContent = displayValue;
                }
            });
        }

        // Advanced metrics simulation
        function simulateDataProcessingActivity() {
            const activities = [
                'ML Model Training',
                'Data Transformation',
                'Real-time Analytics',
                'Batch Processing',
                'Stream Processing',
                'Data Validation',
                'Feature Engineering',
                'Model Inference'
            ];

            // This could be expanded to show a live activity feed
            const currentActivity = activities[Math.floor(Math.random() * activities.length)];

            // Could add a notification or activity log here
            console.log(`Processing: ${currentActivity} - ${new Date().toLocaleTimeString()}`);
        }

        // Initialize real-time updates
        function startRealTimeUpdates() {
            // Update metrics every 5 seconds
            setInterval(updateRealTimeMetrics, 5000);

            // Simulate processing activities every 3 seconds
            setInterval(simulateDataProcessingActivity, 3000);

            // Update time series chart every 30 seconds with new data point
            setInterval(updateTimeSeriesChart, 30000);
        }

        function updateTimeSeriesChart() {
            if (window.timeseriesChart) {
                const chart = window.timeseriesChart;
                const now = new Date();
                const newLabel = now.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });

                // Add new data point
                chart.data.labels.push(newLabel);

                // Generate new data points
                const baseValue = 100 + Math.sin(Date.now() * 0.001) * 20;
                const noise = (Math.random() - 0.5) * 10;

                chart.data.datasets[0].data.push(Math.max(0, baseValue + noise));
                chart.data.datasets[1].data.push(Math.max(0, 80 + Math.sin(Date.now() * 0.0008) * 15 + noise * 0.5));
                chart.data.datasets[2].data.push(Math.max(70, Math.min(100, 85 + Math.sin(Date.now() * 0.0012) * 8 + noise * 0.3)));

                // Remove old data points (keep last 24 points)
                if (chart.data.labels.length > 24) {
                    chart.data.labels.shift();
                    chart.data.datasets.forEach(dataset => {
                        dataset.data.shift();
                    });
                }

                chart.update('none'); // Update without animation for real-time feel
            }
        }

        function setupEventListeners() {
            // Time range selector
            document.getElementById('timeRange').addEventListener('change', function(e) {
                const timeRange = e.target.value;
                updateTimeRange(timeRange);
                showNotification(`Time range updated to: ${getTimeRangeLabel(timeRange)}`);
            });

            // Region filter
            document.getElementById('regionFilter').addEventListener('change', function(e) {
                const region = e.target.value;
                updateRegionFilter(region);
                showNotification(`Region filter: ${getRegionLabel(region)}`);
            });

            // Process type filter
            document.getElementById('processFilter').addEventListener('change', function(e) {
                const processType = e.target.value;
                updateProcessFilter(processType);
                showNotification(`Process filter: ${getProcessLabel(processType)}`);
            });

            // Data intensity slider
            document.getElementById('intensitySlider').addEventListener('input', function(e) {
                const intensity = e.target.value;
                updateDataIntensity(intensity);
                updateIntensityDisplay(intensity);
            });

            // Add keyboard shortcuts
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case 'r':
                            e.preventDefault();
                            refreshAllData();
                            break;
                        case 'f':
                            e.preventDefault();
                            toggleFullscreen();
                            break;
                    }
                }
            });

            // Add resize listener for responsive updates
            window.addEventListener('resize', debounce(function() {
                resizeVisualizations();
            }, 250));
        }

        function updateTimeRange(timeRange) {
            // Update time series chart with new data range
            if (window.timeseriesChart) {
                const newData = generateTimeSeriesData(timeRange);
                window.timeseriesChart.data.labels = newData.labels;
                window.timeseriesChart.data.datasets.forEach((dataset, index) => {
                    dataset.data = newData.datasets[index];
                });
                window.timeseriesChart.update('active');
            }
        }

        function updateRegionFilter(region) {
            // Update spatial visualization to highlight selected region
            const spatialContainer = d3.select('#spatial-viz svg');

            if (region === 'all') {
                // Show all regions normally
                spatialContainer.selectAll('.region circle')
                    .transition()
                    .duration(500)
                    .attr('opacity', 0.8);
            } else {
                // Highlight selected region, dim others
                spatialContainer.selectAll('.region')
                    .transition()
                    .duration(500)
                    .style('opacity', function(d) {
                        const regionName = d.name.toLowerCase().replace(' ', '-');
                        return regionName === region ? 1 : 0.3;
                    });
            }
        }

        function updateProcessFilter(processType) {
            // Update flow diagram to highlight selected process type
            const flowContainer = d3.select('#flow-diagram svg');

            if (processType === 'all') {
                flowContainer.selectAll('.flow-node')
                    .transition()
                    .duration(500)
                    .style('opacity', 1);
            } else {
                // This would filter based on actual process types in a real implementation
                flowContainer.selectAll('.flow-node')
                    .transition()
                    .duration(500)
                    .style('opacity', function(d) {
                        return d.type === 'process' ? 1 : 0.5;
                    });
            }
        }

        function updateDataIntensity(intensity) {
            // Update visualizations based on data intensity
            const normalizedIntensity = intensity / 100;

            // Update spatial visualization particle density
            const spatialContainer = d3.select('#spatial-viz svg');
            spatialContainer.selectAll('.region circle')
                .transition()
                .duration(300)
                .attr('r', function(d) {
                    const baseRadius = Math.sqrt(d.value) / 10 + 8;
                    return baseRadius * (0.5 + normalizedIntensity * 0.5);
                });

            // Update flow diagram particle frequency
            // This would adjust the particle creation interval in a real implementation
        }

        function updateIntensityDisplay(intensity) {
            // Add a display label for the intensity slider
            let label = document.querySelector('.intensity-label');
            if (!label) {
                label = document.createElement('span');
                label.className = 'intensity-label';
                label.style.color = '#90a4ae';
                label.style.fontSize = '0.8rem';
                label.style.marginLeft = '10px';
                document.getElementById('intensitySlider').parentNode.appendChild(label);
            }
            label.textContent = `${intensity}%`;
        }

        function generateTimeSeriesData(timeRange) {
            const now = new Date();
            const labels = [];
            const datasets = [[], [], []];

            let points, interval;
            switch(timeRange) {
                case '1h':
                    points = 12;
                    interval = 5 * 60 * 1000; // 5 minutes
                    break;
                case '24h':
                    points = 24;
                    interval = 60 * 60 * 1000; // 1 hour
                    break;
                case '7d':
                    points = 14;
                    interval = 12 * 60 * 60 * 1000; // 12 hours
                    break;
                case '30d':
                    points = 30;
                    interval = 24 * 60 * 60 * 1000; // 1 day
                    break;
                default:
                    points = 24;
                    interval = 60 * 60 * 1000;
            }

            for (let i = points - 1; i >= 0; i--) {
                const time = new Date(now.getTime() - i * interval);

                if (timeRange === '7d' || timeRange === '30d') {
                    labels.push(time.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
                } else {
                    labels.push(time.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }));
                }

                // Generate data with different patterns based on time range
                const baseValue = 100 + Math.sin(i * 0.3) * 20;
                const noise = (Math.random() - 0.5) * 10;

                datasets[0].push(Math.max(0, baseValue + noise));
                datasets[1].push(Math.max(0, 80 + Math.sin(i * 0.2) * 15 + noise * 0.5));
                datasets[2].push(Math.max(70, Math.min(100, 85 + Math.sin(i * 0.4) * 8 + noise * 0.3)));
            }

            return { labels, datasets };
        }

        function getTimeRangeLabel(value) {
            const labels = {
                '1h': 'Last Hour',
                '24h': 'Last 24 Hours',
                '7d': 'Last 7 Days',
                '30d': 'Last 30 Days'
            };
            return labels[value] || value;
        }

        function getRegionLabel(value) {
            const labels = {
                'all': 'All Regions',
                'north-america': 'North America',
                'europe': 'Europe',
                'asia-pacific': 'Asia Pacific'
            };
            return labels[value] || value;
        }

        function getProcessLabel(value) {
            const labels = {
                'all': 'All Processes',
                'computation': 'Computation',
                'analytics': 'Analytics',
                'ml': 'Machine Learning'
            };
            return labels[value] || value;
        }

        function showNotification(message) {
            // Create a temporary notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: rgba(33, 150, 243, 0.9);
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 0.9rem;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            // Add slide-in animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
            `;
            document.head.appendChild(style);

            // Remove after 3 seconds
            setTimeout(() => {
                notification.style.animation = 'slideIn 0.3s ease-out reverse';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        function refreshAllData() {
            showNotification('Refreshing all data...');

            // Refresh all visualizations
            createSpatialVisualization();
            updateTimeRange(document.getElementById('timeRange').value);
            updateRealTimeMetrics();

            setTimeout(() => {
                showNotification('Data refreshed successfully');
            }, 1000);
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
                showNotification('Entered fullscreen mode (Ctrl+F to exit)');
            } else {
                document.exitFullscreen();
                showNotification('Exited fullscreen mode');
            }
        }

        function resizeVisualizations() {
            // Recreate visualizations on resize
            createSpatialVisualization();
            createFlowDiagram();

            if (window.timeseriesChart) {
                window.timeseriesChart.resize();
            }
        }

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Function to handle region updates from spatial visualization clicks
        function updateVisualizationsForRegion(regionName) {
            const regionValue = regionName.toLowerCase().replace(' ', '-');
            updateRegionFilter(regionValue);

            // Update other visualizations based on region selection
            // This would filter data in a real implementation
        }

        // Comprehensive testing and validation functions
        function runDashboardTests() {
            console.log('🧪 Running Dashboard Tests...');

            const tests = [
                testVisualizationInitialization,
                testControlFunctionality,
                testResponsiveDesign,
                testAccessibility,
                testDataUpdates
            ];

            let passed = 0;
            let failed = 0;

            tests.forEach(test => {
                try {
                    test();
                    console.log(`✅ ${test.name} - PASSED`);
                    passed++;
                } catch (error) {
                    console.error(`❌ ${test.name} - FAILED:`, error);
                    failed++;
                }
            });

            console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`);
            return { passed, failed };
        }

        function testVisualizationInitialization() {
            // Test that all visualizations are properly initialized
            const spatialViz = document.querySelector('#spatial-viz svg');
            const timeseriesChart = document.getElementById('timeseriesChart');
            const flowDiagram = document.querySelector('#flow-diagram svg');
            const metrics = document.querySelectorAll('.metric-value');

            if (!spatialViz) throw new Error('Spatial visualization not initialized');
            if (!timeseriesChart) throw new Error('Time series chart not initialized');
            if (!flowDiagram) throw new Error('Flow diagram not initialized');
            if (metrics.length !== 4) throw new Error('Real-time metrics not properly initialized');
        }

        function testControlFunctionality() {
            // Test that all controls are functional
            const timeRange = document.getElementById('timeRange');
            const regionFilter = document.getElementById('regionFilter');
            const processFilter = document.getElementById('processFilter');
            const intensitySlider = document.getElementById('intensitySlider');

            if (!timeRange || !regionFilter || !processFilter || !intensitySlider) {
                throw new Error('Control elements not found');
            }

            // Test that controls have proper event listeners
            const hasEventListeners = timeRange.onchange || regionFilter.onchange ||
                                     processFilter.onchange || intensitySlider.oninput;

            if (!hasEventListeners) {
                // Event listeners are attached via addEventListener, so this is expected
                // Just verify the elements exist and are functional
            }
        }

        function testResponsiveDesign() {
            // Test responsive design elements
            const mainGrid = document.querySelector('.main-grid');
            const kpiContainer = document.querySelector('.kpi-container');

            if (!mainGrid || !kpiContainer) {
                throw new Error('Responsive layout elements not found');
            }

            // Check that CSS grid is properly applied
            const gridStyle = window.getComputedStyle(mainGrid);
            if (!gridStyle.display.includes('grid')) {
                throw new Error('CSS Grid not properly applied');
            }
        }

        function testAccessibility() {
            // Test accessibility features
            const ariaLabels = document.querySelectorAll('[aria-label]');
            const roles = document.querySelectorAll('[role]');
            const srOnly = document.querySelectorAll('.sr-only');

            if (ariaLabels.length === 0) throw new Error('No ARIA labels found');
            if (roles.length === 0) throw new Error('No ARIA roles found');
            if (srOnly.length === 0) throw new Error('No screen reader content found');
        }

        function testDataUpdates() {
            // Test that data update functions exist and are callable
            if (typeof updateRealTimeMetrics !== 'function') {
                throw new Error('updateRealTimeMetrics function not found');
            }
            if (typeof updateTimeRange !== 'function') {
                throw new Error('updateTimeRange function not found');
            }
            if (typeof updateRegionFilter !== 'function') {
                throw new Error('updateRegionFilter function not found');
            }
        }

        // Performance monitoring
        function monitorPerformance() {
            if ('performance' in window) {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('📈 Performance Metrics:');
                console.log(`DOM Content Loaded: ${perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart}ms`);
                console.log(`Page Load Complete: ${perfData.loadEventEnd - perfData.loadEventStart}ms`);

                // Monitor memory usage if available
                if ('memory' in performance) {
                    console.log(`Memory Usage: ${(performance.memory.usedJSHeapSize / 1048576).toFixed(2)}MB`);
                }
            }
        }

        // Add development helpers
        window.dashboardAPI = {
            runTests: runDashboardTests,
            monitorPerformance: monitorPerformance,
            refreshData: refreshAllData,
            updateTimeRange: updateTimeRange,
            updateRegion: updateRegionFilter,
            updateProcess: updateProcessFilter,
            showNotification: showNotification
        };

        // Run tests in development mode
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            setTimeout(() => {
                runDashboardTests();
                monitorPerformance();
            }, 2000);
        }
    </script>
</body>
</html>
