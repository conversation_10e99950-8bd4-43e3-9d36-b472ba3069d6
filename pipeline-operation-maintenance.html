<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管网运维综合管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            color: #333;
        }

        .maintenance-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .maintenance-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .maintenance-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3.2rem;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.3rem;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }

        .system-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
            position: relative;
            z-index: 1;
        }

        .overview-item {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .overview-item:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }

        .overview-icon {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 10px;
        }

        .overview-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .overview-desc {
            font-size: 0.9rem;
            color: #666;
        }

        /* 导航菜单 */
        .nav-menu {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 15px;
            margin-bottom: 30px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 10px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-menu::-webkit-scrollbar {
            display: none;
        }

        .nav-item {
            flex: 1;
            min-width: 180px;
            padding: 20px 25px;
            background: transparent;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            white-space: nowrap;
        }

        .nav-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            transform: translateY(-3px);
        }

        .nav-item:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-2px);
        }

        .nav-icon {
            font-size: 1.5rem;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            min-height: 800px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 模块标题 */
        .module-title {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
            padding-bottom: 15px;
            border-bottom: 3px solid #f0f0f0;
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .stat-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 6px solid;
            cursor: pointer;
        }

        .stat-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .stat-card.archives { border-left-color: #2196f3; }
        .stat-card.inspection { border-left-color: #4caf50; }
        .stat-card.repair { border-left-color: #ff9800; }
        .stat-card.maintenance { border-left-color: #9c27b0; }
        .stat-card.detection { border-left-color: #f44336; }
        .stat-card.emergency { border-left-color: #e91e63; }
        .stat-card.evaluation { border-left-color: #00bcd4; }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .stat-title {
            font-size: 1.2rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .stat-trend {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .stat-value {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
            gap: 10px;
        }

        .stat-unit {
            font-size: 1rem;
            color: #888;
        }

        .stat-description {
            font-size: 0.95rem;
            color: #666;
            line-height: 1.5;
        }

        /* 功能面板 */
        .function-panel {
            background: #f8f9fa;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 6px solid #667eea;
        }

        .panel-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .function-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .function-item {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .function-item:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
        }

        .function-icon {
            font-size: 2rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .function-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .function-desc {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.5;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 25px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(102, 126, 234, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }

        .status-normal { color: #4caf50; font-weight: 600; }
        .status-warning { color: #ff9800; font-weight: 600; }
        .status-danger { color: #f44336; font-weight: 600; }
        .status-info { color: #2196f3; font-weight: 600; }

        /* 图表容器 */
        .charts-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 35px;
        }

        .chart-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #667eea;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .chart-container {
            position: relative;
            height: 350px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
            flex-wrap: wrap;
        }

        /* 预警面板 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 6px solid #ffc107;
            border-radius: 15px;
            padding: 20px 25px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.danger {
            background: linear-gradient(135deg, #f8d7da, #fab1a0);
            border-left-color: #dc3545;
            box-shadow: 0 8px 32px rgba(220, 53, 69, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
        }

        .alert-icon {
            font-size: 2rem;
            color: #856404;
        }

        .alert-panel.danger .alert-icon { color: #721c24; }
        .alert-panel.success .alert-icon { color: #155724; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #856404;
        }

        .alert-panel.danger .alert-title { color: #721c24; }
        .alert-panel.success .alert-title { color: #155724; }

        .alert-message {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #856404;
        }

        .alert-panel.danger .alert-message { color: #721c24; }
        .alert-panel.success .alert-message { color: #155724; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .maintenance-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.5rem;
                flex-direction: column;
                gap: 15px;
            }

            .nav-menu {
                flex-direction: column;
            }

            .nav-item {
                min-width: auto;
                padding: 15px 20px;
                flex-direction: row;
                justify-content: center;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .system-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .function-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #667eea;
            font-size: 1.1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <!-- 系统头部 -->
        <div class="maintenance-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tools"></i>
                管网运维综合管理系统
                <i class="fas fa-cogs"></i>
            </h1>
            <p class="subtitle">Pipeline Operation & Maintenance Management System - 全生命周期运维管理平台</p>
            
            <div class="system-overview">
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="overview-title">台账管理</div>
                    <div class="overview-desc">设施档案完整</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="overview-title">巡检管理</div>
                    <div class="overview-desc">定期巡检到位</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-wrench"></i>
                    </div>
                    <div class="overview-title">维修管理</div>
                    <div class="overview-desc">故障快速响应</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="overview-title">养护管理</div>
                    <div class="overview-desc">预防性维护</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-microscope"></i>
                    </div>
                    <div class="overview-title">检测管理</div>
                    <div class="overview-desc">专业检测评估</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="overview-title">应急处置</div>
                    <div class="overview-desc">快速应急响应</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="overview-title">运行评估</div>
                    <div class="overview-desc">持续优化改进</div>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <div class="nav-menu slide-in">
            <button class="nav-item active" data-tab="archives-management">
                <i class="nav-icon fas fa-database"></i>
                <span>台账管理</span>
            </button>
            <button class="nav-item" data-tab="inspection-management">
                <i class="nav-icon fas fa-search"></i>
                <span>巡检管理</span>
            </button>
            <button class="nav-item" data-tab="repair-management">
                <i class="nav-icon fas fa-wrench"></i>
                <span>维修管理</span>
            </button>
            <button class="nav-item" data-tab="maintenance-management">
                <i class="nav-icon fas fa-shield-alt"></i>
                <span>养护管理</span>
            </button>
            <button class="nav-item" data-tab="detection-management">
                <i class="nav-icon fas fa-microscope"></i>
                <span>检测管理</span>
            </button>
            <button class="nav-item" data-tab="emergency-management">
                <i class="nav-icon fas fa-exclamation-triangle"></i>
                <span>应急处置</span>
            </button>
            <button class="nav-item" data-tab="evaluation-management">
                <i class="nav-icon fas fa-chart-line"></i>
                <span>运行评估</span>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 台账管理 -->
            <div id="archives-management" class="tab-content active">
                <h2 class="module-title">
                    <i class="fas fa-database"></i>
                    管网台账管理
                </h2>

                <!-- 台账统计 -->
                <div class="stats-grid">
                    <div class="stat-card archives">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-layer-group"></i>
                                管道总长度
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.5km
                            </div>
                        </div>
                        <div class="stat-value">
                            1,258.6
                            <span class="stat-unit">km</span>
                        </div>
                        <div class="stat-description">
                            包含主干管、支管、服务管等各类管道，覆盖全市供水网络
                        </div>
                    </div>

                    <div class="stat-card archives">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-cogs"></i>
                                设施设备数量
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="stat-value">
                            3,456
                            <span class="stat-unit">台/套</span>
                        </div>
                        <div class="stat-description">
                            水泵、阀门、监测设备等各类设施设备完整建档
                        </div>
                    </div>

                    <div class="stat-card archives">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-file-alt"></i>
                                档案完整率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.2%
                            </div>
                        </div>
                        <div class="stat-value">
                            98.7
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            设施档案信息完整，包含技术参数、建设年代、维护记录等
                        </div>
                    </div>

                    <div class="stat-card archives">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-sync-alt"></i>
                                数据更新频率
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="stat-value">
                            实时
                            <span class="stat-unit">更新</span>
                        </div>
                        <div class="stat-description">
                            台账数据实时更新，确保信息准确性和时效性
                        </div>
                    </div>
                </div>

                <!-- 台账管理功能 -->
                <div class="function-panel">
                    <div class="panel-title">
                        <i class="fas fa-tools"></i>
                        台账管理功能
                    </div>
                    
                    <div class="function-grid">
                        <div class="function-item" onclick="openArchivesFunction('pipeline')">
                            <div class="function-icon">
                                <i class="fas fa-road"></i>
                            </div>
                            <div class="function-name">管道档案管理</div>
                            <div class="function-desc">管道基本信息、技术参数、建设资料、运行状态等档案管理</div>
                        </div>

                        <div class="function-item" onclick="openArchivesFunction('equipment')">
                            <div class="function-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="function-name">设备档案管理</div>
                            <div class="function-desc">水泵、阀门、监测设备等设施设备档案信息管理</div>
                        </div>

                        <div class="function-item" onclick="openArchivesFunction('maintenance')">
                            <div class="function-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <div class="function-name">维护记录管理</div>
                            <div class="function-desc">设施设备维护保养记录、故障处理记录等历史档案</div>
                        </div>

                        <div class="function-item" onclick="openArchivesFunction('drawing')">
                            <div class="function-icon">
                                <i class="fas fa-drafting-compass"></i>
                            </div>
                            <div class="function-name">图纸资料管理</div>
                            <div class="function-desc">工程图纸、竣工资料、技术文档等资料档案管理</div>
                        </div>

                        <div class="function-item" onclick="openArchivesFunction('gis')">
                            <div class="function-icon">
                                <i class="fas fa-map"></i>
                            </div>
                            <div class="function-name">GIS空间档案</div>
                            <div class="function-desc">管网空间位置信息、地理坐标、拓扑关系等GIS档案</div>
                        </div>

                        <div class="function-item" onclick="openArchivesFunction('report')">
                            <div class="function-icon">
                                <i class="fas fa-chart-bar"></i>
                            </div>
                            <div class="function-name">统计分析报告</div>
                            <div class="function-desc">台账数据统计分析、资产评估报告、运行状况分析</div>
                        </div>
                    </div>
                </div>

                <!-- 台账数据表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>设施编号</th>
                            <th>设施名称</th>
                            <th>设施类型</th>
                            <th>技术参数</th>
                            <th>建设年代</th>
                            <th>运行状态</th>
                            <th>最后更新</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>P-001-2015</td>
                            <td>建设路主干管</td>
                            <td>给水管道</td>
                            <td>DN600/球墨铸铁</td>
                            <td>2015年</td>
                            <td class="status-normal">正常运行</td>
                            <td>2024-01-15</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>V-003-2018</td>
                            <td>春华路控制阀</td>
                            <td>闸阀</td>
                            <td>DN400/电动</td>
                            <td>2018年</td>
                            <td class="status-warning">需要维护</td>
                            <td>2024-01-14</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>PU-002-2020</td>
                            <td>东区增压泵站</td>
                            <td>离心泵</td>
                            <td>500m³/h/75kW</td>
                            <td>2020年</td>
                            <td class="status-normal">正常运行</td>
                            <td>2024-01-15</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>M-015-2019</td>
                            <td>压力监测点15</td>
                            <td>压力传感器</td>
                            <td>0-1.6MPa/4-20mA</td>
                            <td>2019年</td>
                            <td class="status-info">在线监测</td>
                            <td>2024-01-15</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>P-025-2012</td>
                            <td>老城区支管网</td>
                            <td>给水管道</td>
                            <td>DN300/铸铁管</td>
                            <td>2012年</td>
                            <td class="status-danger">需要更换</td>
                            <td>2024-01-13</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 巡检管理 -->
            <div id="inspection-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-search"></i>
                    管网巡检管理
                </h2>

                <!-- 巡检预警 -->
                <div class="alert-panel warning">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">巡检任务提醒</div>
                        <div class="alert-message">今日还有8个巡检点位未完成巡检，其中3个为重点监控区域，请及时安排巡检人员前往检查</div>
                    </div>
                </div>

                <!-- 巡检统计 -->
                <div class="stats-grid">
                    <div class="stat-card inspection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-calendar-check"></i>
                                今日巡检完成率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5.2%
                            </div>
                        </div>
                        <div class="stat-value">
                            78.5
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            已完成156个点位巡检，剩余43个点位待巡检
                        </div>
                    </div>

                    <div class="stat-card inspection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-exclamation-circle"></i>
                                发现问题数量
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -2个
                            </div>
                        </div>
                        <div class="stat-value">
                            12
                            <span class="stat-unit">个</span>
                        </div>
                        <div class="stat-description">
                            一般问题9个，重要问题3个，已全部上报处理
                        </div>
                    </div>

                    <div class="stat-card inspection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-users"></i>
                                巡检人员在岗
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="stat-value">
                            24
                            <span class="stat-unit">人</span>
                        </div>
                        <div class="stat-description">
                            分布在6个巡检区域，实时位置可追踪
                        </div>
                    </div>

                    <div class="stat-card inspection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-clock"></i>
                                平均巡检时长
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8分钟
                            </div>
                        </div>
                        <div class="stat-value">
                            25
                            <span class="stat-unit">分钟</span>
                        </div>
                        <div class="stat-description">
                            单个点位平均巡检时长，效率持续提升
                        </div>
                    </div>
                </div>

                <!-- 巡检功能 -->
                <div class="function-panel">
                    <div class="panel-title">
                        <i class="fas fa-route"></i>
                        巡检管理功能
                    </div>

                    <div class="function-grid">
                        <div class="function-item" onclick="openInspectionFunction('plan')">
                            <div class="function-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="function-name">巡检计划制定</div>
                            <div class="function-desc">制定日常巡检计划、专项巡检计划、应急巡检计划</div>
                        </div>

                        <div class="function-item" onclick="openInspectionFunction('route')">
                            <div class="function-icon">
                                <i class="fas fa-route"></i>
                            </div>
                            <div class="function-name">巡检路线优化</div>
                            <div class="function-desc">智能规划巡检路线，提高巡检效率，降低巡检成本</div>
                        </div>

                        <div class="function-item" onclick="openInspectionFunction('mobile')">
                            <div class="function-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="function-name">移动巡检终端</div>
                            <div class="function-desc">手机APP巡检，GPS定位，拍照记录，实时上传</div>
                        </div>

                        <div class="function-item" onclick="openInspectionFunction('record')">
                            <div class="function-icon">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <div class="function-name">巡检记录管理</div>
                            <div class="function-desc">巡检记录存档、问题跟踪、整改验收、效果评估</div>
                        </div>

                        <div class="function-item" onclick="openInspectionFunction('analysis')">
                            <div class="function-icon">
                                <i class="fas fa-chart-pie"></i>
                            </div>
                            <div class="function-name">巡检数据分析</div>
                            <div class="function-desc">巡检数据统计分析，问题趋势分析，效率评估</div>
                        </div>

                        <div class="function-item" onclick="openInspectionFunction('quality')">
                            <div class="function-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="function-name">巡检质量评估</div>
                            <div class="function-desc">巡检质量考核，人员绩效评估，奖惩机制</div>
                        </div>
                    </div>
                </div>

                <!-- 巡检记录表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>巡检编号</th>
                            <th>巡检区域</th>
                            <th>巡检人员</th>
                            <th>巡检时间</th>
                            <th>巡检点位</th>
                            <th>发现问题</th>
                            <th>处理状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>XJ-2024-0115-001</td>
                            <td>东区管网</td>
                            <td>张巡检员</td>
                            <td>14:30-15:15</td>
                            <td>25个</td>
                            <td class="status-warning">阀门渗漏</td>
                            <td class="status-info">已上报</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>XJ-2024-0115-002</td>
                            <td>西区管网</td>
                            <td>李巡检员</td>
                            <td>13:45-14:20</td>
                            <td>18个</td>
                            <td class="status-normal">无异常</td>
                            <td class="status-normal">已完成</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>XJ-2024-0115-003</td>
                            <td>南区管网</td>
                            <td>王巡检员</td>
                            <td>12:30-13:25</td>
                            <td>32个</td>
                            <td class="status-danger">管道破损</td>
                            <td class="status-warning">维修中</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 维修管理 -->
            <div id="repair-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-wrench"></i>
                    管网维修管理
                </h2>

                <!-- 维修预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">紧急维修任务</div>
                        <div class="alert-message">建设路DN400主管爆管，维修工单WX-2024-001已派发，预计影响用户2500户，请立即组织抢修</div>
                    </div>
                </div>

                <!-- 维修统计 -->
                <div class="stats-grid">
                    <div class="stat-card repair">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-clipboard-list"></i>
                                待处理工单
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3单
                            </div>
                        </div>
                        <div class="stat-value">
                            15
                            <span class="stat-unit">单</span>
                        </div>
                        <div class="stat-description">
                            紧急工单3单，一般工单12单，平均响应时间18分钟
                        </div>
                    </div>

                    <div class="stat-card repair">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-tools"></i>
                                维修队伍状态
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                就绪
                            </div>
                        </div>
                        <div class="stat-value">
                            8
                            <span class="stat-unit">队</span>
                        </div>
                        <div class="stat-description">
                            专业维修队伍8支，维修人员32人，设备车辆齐全
                        </div>
                    </div>

                    <div class="stat-card repair">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-clock"></i>
                                平均修复时间
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -0.5h
                            </div>
                        </div>
                        <div class="stat-value">
                            4.2
                            <span class="stat-unit">小时</span>
                        </div>
                        <div class="stat-description">
                            故障平均修复时间，较上月缩短0.5小时
                        </div>
                    </div>

                    <div class="stat-card repair">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-percentage"></i>
                                一次修复成功率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.1%
                            </div>
                        </div>
                        <div class="stat-value">
                            92.8
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            维修质量持续提升，返工率显著降低
                        </div>
                    </div>
                </div>

                <!-- 维修工单表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>工单编号</th>
                            <th>故障类型</th>
                            <th>故障位置</th>
                            <th>紧急程度</th>
                            <th>创建时间</th>
                            <th>负责队伍</th>
                            <th>预计完成</th>
                            <th>工单状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>WX-2024-001</td>
                            <td class="status-danger">管道爆管</td>
                            <td>建设路DN400</td>
                            <td class="status-danger">紧急</td>
                            <td>14:15</td>
                            <td>A队</td>
                            <td>18:00</td>
                            <td class="status-warning">抢修中</td>
                        </tr>
                        <tr>
                            <td>WX-2024-002</td>
                            <td class="status-warning">阀门故障</td>
                            <td>春华路V-003</td>
                            <td class="status-warning">重要</td>
                            <td>13:30</td>
                            <td>B队</td>
                            <td>16:00</td>
                            <td class="status-info">已派工</td>
                        </tr>
                        <tr>
                            <td>WX-2024-003</td>
                            <td class="status-info">设备维护</td>
                            <td>东区泵站</td>
                            <td class="status-normal">一般</td>
                            <td>12:45</td>
                            <td>C队</td>
                            <td>明日</td>
                            <td class="status-normal">待处理</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 养护管理 -->
            <div id="maintenance-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-shield-alt"></i>
                    管网养护管理
                </h2>

                <!-- 养护提醒 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">预防性养护计划</div>
                        <div class="alert-message">本月预防性养护计划执行良好，已完成85%的养护任务，有效降低了设备故障率15.2%</div>
                    </div>
                </div>

                <!-- 养护统计 -->
                <div class="stats-grid">
                    <div class="stat-card maintenance">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-calendar-check"></i>
                                养护计划完成率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +8.5%
                            </div>
                        </div>
                        <div class="stat-value">
                            85.2
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            本月计划养护156项，已完成133项，剩余23项
                        </div>
                    </div>

                    <div class="stat-card maintenance">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-chart-line"></i>
                                设备健康度提升
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +12.3%
                            </div>
                        </div>
                        <div class="stat-value">
                            92.8
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            通过预防性养护，设备整体健康度显著提升
                        </div>
                    </div>

                    <div class="stat-card maintenance">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-dollar-sign"></i>
                                养护成本节约
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -15.2%
                            </div>
                        </div>
                        <div class="stat-value">
                            28.5
                            <span class="stat-unit">万元</span>
                        </div>
                        <div class="stat-description">
                            预防性养护相比故障维修节约成本28.5万元
                        </div>
                    </div>

                    <div class="stat-card maintenance">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-clock"></i>
                                设备使用寿命
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.5年
                            </div>
                        </div>
                        <div class="stat-value">
                            15.8
                            <span class="stat-unit">年</span>
                        </div>
                        <div class="stat-description">
                            通过科学养护，设备平均使用寿命延长2.5年
                        </div>
                    </div>
                </div>

                <!-- 养护计划表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>养护编号</th>
                            <th>养护对象</th>
                            <th>养护类型</th>
                            <th>计划时间</th>
                            <th>负责人员</th>
                            <th>养护内容</th>
                            <th>执行状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>YH-2024-001</td>
                            <td>1号水泵</td>
                            <td>定期保养</td>
                            <td>2024-01-20</td>
                            <td>张技师</td>
                            <td>轴承润滑、密封检查</td>
                            <td class="status-normal">计划中</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>YH-2024-002</td>
                            <td>主控阀门</td>
                            <td>预防性维护</td>
                            <td>2024-01-18</td>
                            <td>李工程师</td>
                            <td>阀门清洗、密封更换</td>
                            <td class="status-info">进行中</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>YH-2024-003</td>
                            <td>管道防腐</td>
                            <td>防腐处理</td>
                            <td>2024-01-15</td>
                            <td>王班长</td>
                            <td>防腐涂层检查修补</td>
                            <td class="status-normal">已完成</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 检测管理 -->
            <div id="detection-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-microscope"></i>
                    管网检测管理
                </h2>

                <!-- 检测提醒 -->
                <div class="alert-panel warning">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">检测计划提醒</div>
                        <div class="alert-message">老城区DN300铸铁管段已使用12年，建议进行管道内窥检测和壁厚检测，评估管道健康状况</div>
                    </div>
                </div>

                <!-- 检测统计 -->
                <div class="stats-grid">
                    <div class="stat-card detection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-search"></i>
                                年度检测完成率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +12.5%
                            </div>
                        </div>
                        <div class="stat-value">
                            68.5
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            已完成检测85.6km，剩余39.2km待检测
                        </div>
                    </div>

                    <div class="stat-card detection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-exclamation-circle"></i>
                                发现隐患数量
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8个
                            </div>
                        </div>
                        <div class="stat-value">
                            23
                            <span class="stat-unit">处</span>
                        </div>
                        <div class="stat-description">
                            严重隐患5处，一般隐患18处，已制定处理方案
                        </div>
                    </div>

                    <div class="stat-card detection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-chart-pie"></i>
                                管道健康评级
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                良好
                            </div>
                        </div>
                        <div class="stat-value">
                            B+
                            <span class="stat-unit">级</span>
                        </div>
                        <div class="stat-description">
                            整体健康状况良好，局部区域需要重点关注
                        </div>
                    </div>

                    <div class="stat-card detection">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-calendar-alt"></i>
                                检测周期达标率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5.8%
                            </div>
                        </div>
                        <div class="stat-value">
                            92.3
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            按规定周期完成检测，检测质量持续提升
                        </div>
                    </div>
                </div>

                <!-- 检测项目表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>检测编号</th>
                            <th>检测对象</th>
                            <th>检测类型</th>
                            <th>检测时间</th>
                            <th>检测机构</th>
                            <th>检测结果</th>
                            <th>处理建议</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>JC-2024-001</td>
                            <td>建设路主管</td>
                            <td>内窥检测</td>
                            <td>2024-01-10</td>
                            <td>专业检测公司A</td>
                            <td class="status-normal">良好</td>
                            <td>继续监测</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                        <tr>
                            <td>JC-2024-002</td>
                            <td>老城区支管</td>
                            <td>壁厚检测</td>
                            <td>2024-01-12</td>
                            <td>专业检测公司B</td>
                            <td class="status-warning">轻微腐蚀</td>
                            <td>加强防腐</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                        <tr>
                            <td>JC-2024-003</td>
                            <td>东区泵站</td>
                            <td>振动检测</td>
                            <td>2024-01-14</td>
                            <td>设备检测中心</td>
                            <td class="status-danger">异常振动</td>
                            <td>立即维修</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 应急处置 -->
            <div id="emergency-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    爆管应急处置管理
                </h2>

                <!-- 应急状态 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">应急事件进行中</div>
                        <div class="alert-message">建设路DN400主管爆管事件正在处置中，已启动三级应急响应，抢修队伍到位，预计18:00完成抢修</div>
                    </div>
                </div>

                <!-- 应急统计 -->
                <div class="stats-grid">
                    <div class="stat-card emergency">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-exclamation-circle"></i>
                                当前应急事件
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1事件
                            </div>
                        </div>
                        <div class="stat-value">
                            1
                            <span class="stat-unit">起</span>
                        </div>
                        <div class="stat-description">
                            三级响应1起，影响用户2500户，抢修进行中
                        </div>
                    </div>

                    <div class="stat-card emergency">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -5分钟
                            </div>
                        </div>
                        <div class="stat-value">
                            18
                            <span class="stat-unit">分钟</span>
                        </div>
                        <div class="stat-description">
                            应急响应时间持续优化，较上月缩短5分钟
                        </div>
                    </div>

                    <div class="stat-card emergency">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-tools"></i>
                                应急队伍状态
                            </div>
                            <div class="stat-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                就绪
                            </div>
                        </div>
                        <div class="stat-value">
                            8
                            <span class="stat-unit">队</span>
                        </div>
                        <div class="stat-description">
                            专业应急队伍8支，24小时待命，装备齐全
                        </div>
                    </div>

                    <div class="stat-card emergency">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-percentage"></i>
                                应急处置成功率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.5%
                            </div>
                        </div>
                        <div class="stat-value">
                            98.5
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            应急处置成功率高，损失控制在最小范围
                        </div>
                    </div>
                </div>

                <!-- 应急事件表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>事件编号</th>
                            <th>事件类型</th>
                            <th>发生位置</th>
                            <th>影响范围</th>
                            <th>响应等级</th>
                            <th>处置状态</th>
                            <th>负责队伍</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>YJ-2024-001</td>
                            <td class="status-danger">管道爆管</td>
                            <td>建设路DN400</td>
                            <td>2500户</td>
                            <td class="status-warning">三级响应</td>
                            <td class="status-info">抢修中</td>
                            <td>A队、B队</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">实时跟踪</button></td>
                        </tr>
                        <tr>
                            <td>YJ-2024-002</td>
                            <td class="status-warning">水质异常</td>
                            <td>东区水厂</td>
                            <td>1200户</td>
                            <td class="status-normal">四级响应</td>
                            <td class="status-normal">已处置</td>
                            <td>C队</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>YJ-2024-003</td>
                            <td class="status-info">设备故障</td>
                            <td>南区泵站</td>
                            <td>800户</td>
                            <td class="status-normal">四级响应</td>
                            <td class="status-normal">已处置</td>
                            <td>D队</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 运行评估 -->
            <div id="evaluation-management" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-chart-line"></i>
                    管网运行评估管理
                </h2>

                <!-- 评估概况 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">运行评估报告</div>
                        <div class="alert-message">2024年第一季度管网运行评估已完成，整体运行状况良好，供水保证率99.2%，漏损率控制在6.8%以内</div>
                    </div>
                </div>

                <!-- 评估指标 -->
                <div class="stats-grid">
                    <div class="stat-card evaluation">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-tint"></i>
                                供水保证率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.8%
                            </div>
                        </div>
                        <div class="stat-value">
                            99.2
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            供水保证率持续提升，用户满意度显著改善
                        </div>
                    </div>

                    <div class="stat-card evaluation">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                管网漏损率
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2%
                            </div>
                        </div>
                        <div class="stat-value">
                            6.8
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            漏损率持续下降，节水效果显著
                        </div>
                    </div>

                    <div class="stat-card evaluation">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-cogs"></i>
                                设备完好率
                            </div>
                            <div class="stat-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.5%
                            </div>
                        </div>
                        <div class="stat-value">
                            98.5
                            <span class="stat-unit">%</span>
                        </div>
                        <div class="stat-description">
                            设备维护效果良好，故障率明显降低
                        </div>
                    </div>

                    <div class="stat-card evaluation">
                        <div class="stat-header">
                            <div class="stat-title">
                                <i class="fas fa-dollar-sign"></i>
                                运维成本效益
                            </div>
                            <div class="stat-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8.5%
                            </div>
                        </div>
                        <div class="stat-value">
                            0.85
                            <span class="stat-unit">元/m³</span>
                        </div>
                        <div class="stat-description">
                            单位供水运维成本下降，经济效益提升
                        </div>
                    </div>
                </div>

                <!-- 图表分析 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                关键指标趋势分析
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="evaluationTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <div class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                运维工作分布
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="maintenanceDistChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 评估报告表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>评估期间</th>
                            <th>评估类型</th>
                            <th>评估结果</th>
                            <th>主要问题</th>
                            <th>改进建议</th>
                            <th>实施状态</th>
                            <th>效果评价</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>2024年Q1</td>
                            <td>季度评估</td>
                            <td class="status-normal">良好</td>
                            <td>局部漏损偏高</td>
                            <td>加强老旧管网改造</td>
                            <td class="status-info">实施中</td>
                            <td class="status-normal">有效</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                        <tr>
                            <td>2023年度</td>
                            <td>年度评估</td>
                            <td class="status-normal">优秀</td>
                            <td>设备老化加剧</td>
                            <td>制定设备更新计划</td>
                            <td class="status-normal">已完成</td>
                            <td class="status-normal">显著</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                        <tr>
                            <td>2023年Q4</td>
                            <td>专项评估</td>
                            <td class="status-warning">一般</td>
                            <td>应急响应能力不足</td>
                            <td>完善应急预案体系</td>
                            <td class="status-normal">已完成</td>
                            <td class="status-normal">良好</td>
                            <td><button class="btn btn-primary" style="padding: 6px 12px; font-size: 0.8rem;">查看报告</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'archives-management';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('管网运维综合管理系统初始化...');
            
            // 初始化导航事件
            initNavEvents();
            
            console.log('系统初始化完成');
        });

        // 初始化导航事件
        function initNavEvents() {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => {
                item.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedItem) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            } else {
                // 如果内容不存在，动态加载
                loadTabContent(tabId);
            }
            
            // 激活选中导航
            if (clickedItem) {
                clickedItem.classList.add('active');
            }
        }

        // 动态加载标签页内容
        function loadTabContent(tabId) {
            const contentArea = document.querySelector('.content-area');
            
            // 显示加载动画
            contentArea.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    正在加载${getTabName(tabId)}模块...
                </div>
            `;
            
            // 模拟加载延迟
            setTimeout(() => {
                generateTabContent(tabId);
            }, 1000);
        }

        // 获取标签页名称
        function getTabName(tabId) {
            const names = {
                'archives-management': '台账管理',
                'inspection-management': '巡检管理',
                'repair-management': '维修管理',
                'maintenance-management': '养护管理',
                'detection-management': '检测管理',
                'emergency-management': '应急处置',
                'evaluation-management': '运行评估'
            };
            return names[tabId] || '未知模块';
        }

        // 生成标签页内容
        function generateTabContent(tabId) {
            // 内容已经在HTML中预定义，这里只需要初始化对应的图表
            setTimeout(() => {
                initTabCharts(tabId);
            }, 100);
        }

        // 初始化标签页图表
        function initTabCharts(tabId) {
            switch(tabId) {
                case 'evaluation-management':
                    initEvaluationCharts();
                    break;
                default:
                    console.log(`${tabId} 图表初始化`);
                    break;
            }
        }

        // 初始化评估图表
        function initEvaluationCharts() {
            initEvaluationTrendChart();
            initMaintenanceDistChart();
        }

        // 评估趋势图表
        function initEvaluationTrendChart() {
            const ctx = document.getElementById('evaluationTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.evaluationTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['2023-Q1', '2023-Q2', '2023-Q3', '2023-Q4', '2024-Q1'],
                    datasets: [{
                        label: '供水保证率 (%)',
                        data: [97.8, 98.2, 98.6, 98.9, 99.2],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '设备完好率 (%)',
                        data: [94.5, 95.2, 96.8, 97.5, 98.5],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '漏损率 (%)',
                        data: [8.5, 8.2, 7.8, 7.2, 6.8],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '保证率/完好率 (%)' },
                            min: 90,
                            max: 100
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '漏损率 (%)' },
                            grid: { drawOnChartArea: false },
                            min: 0,
                            max: 10
                        }
                    }
                }
            });
        }

        // 运维工作分布图表
        function initMaintenanceDistChart() {
            const ctx = document.getElementById('maintenanceDistChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.maintenanceDist = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['日常巡检', '预防性养护', '故障维修', '专项检测', '应急处置', '其他'],
                    datasets: [{
                        data: [35, 25, 20, 10, 5, 5],
                        backgroundColor: [
                            '#2196f3',
                            '#4caf50',
                            '#ff9800',
                            '#9c27b0',
                            '#f44336',
                            '#607d8b'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 台账功能
        function openArchivesFunction(type) {
            const typeNames = {
                'pipeline': '管道档案管理',
                'equipment': '设备档案管理',
                'maintenance': '维护记录管理',
                'drawing': '图纸资料管理',
                'gis': 'GIS空间档案',
                'report': '统计分析报告'
            };

            showNotification(`正在打开${typeNames[type]}功能...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已打开！`, 'success');
            }, 1500);
        }

        // 巡检功能
        function openInspectionFunction(type) {
            const typeNames = {
                'plan': '巡检计划制定',
                'route': '巡检路线优化',
                'mobile': '移动巡检终端',
                'record': '巡检记录管理',
                'analysis': '巡检数据分析',
                'quality': '巡检质量评估'
            };

            showNotification(`正在启动${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已启动！`, 'success');
            }, 1500);
        }

        // 维修功能
        function openRepairFunction(type) {
            const typeNames = {
                'dispatch': '工单派发',
                'track': '维修跟踪',
                'resource': '资源调配',
                'quality': '质量验收',
                'cost': '成本核算',
                'analysis': '维修分析'
            };

            showNotification(`正在打开${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已打开！`, 'success');
            }, 1500);
        }

        // 养护功能
        function openMaintenanceFunction(type) {
            const typeNames = {
                'plan': '养护计划制定',
                'schedule': '养护调度安排',
                'record': '养护记录管理',
                'effect': '养护效果评估',
                'cost': '养护成本分析',
                'optimization': '养护策略优化'
            };

            showNotification(`正在启动${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已启动！`, 'success');
            }, 1500);
        }

        // 检测功能
        function openDetectionFunction(type) {
            const typeNames = {
                'plan': '检测计划制定',
                'schedule': '检测安排调度',
                'report': '检测报告管理',
                'analysis': '检测数据分析',
                'evaluation': '健康状况评估',
                'recommendation': '处理建议制定'
            };

            showNotification(`正在打开${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已打开！`, 'success');
            }, 1500);
        }

        // 应急功能
        function openEmergencyFunction(type) {
            const typeNames = {
                'plan': '应急预案管理',
                'response': '应急响应启动',
                'resource': '应急资源调配',
                'communication': '应急通信协调',
                'recovery': '恢复重建管理',
                'evaluation': '应急效果评估'
            };

            showNotification(`正在启动${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已启动！`, 'success');
            }, 1500);
        }

        // 评估功能
        function openEvaluationFunction(type) {
            const typeNames = {
                'indicator': '指标体系管理',
                'data': '数据采集分析',
                'report': '评估报告生成',
                'comparison': '对比分析',
                'optimization': '优化建议制定',
                'tracking': '改进效果跟踪'
            };

            showNotification(`正在打开${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}功能已打开！`, 'success');
            }, 1500);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 350px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
