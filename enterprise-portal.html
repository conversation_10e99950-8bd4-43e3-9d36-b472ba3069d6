<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业统一门户管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        /* 登录界面样式 */
        .login-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .login-box {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            width: 400px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            text-align: center;
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .login-logo {
            margin-bottom: 30px;
        }

        .login-logo i {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 15px;
            display: block;
        }

        .login-logo h1 {
            font-size: 1.8rem;
            color: #333;
            margin-bottom: 10px;
        }

        .login-logo p {
            color: #666;
            font-size: 0.9rem;
        }

        .login-form {
            margin-top: 30px;
        }

        .form-group {
            margin-bottom: 20px;
            position: relative;
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            z-index: 1;
        }

        .form-input {
            width: 100%;
            padding: 15px 15px 15px 45px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            font-size: 1rem;
            outline: none;
            transition: all 0.3s ease;
            background: rgba(255, 255, 255, 0.8);
        }

        .form-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .role-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 20px;
        }

        .role-option {
            padding: 12px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            background: rgba(255, 255, 255, 0.5);
        }

        .role-option:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .role-option.selected {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
        }

        .login-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9rem;
            color: #666;
        }

        .remember-me {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .forgot-password {
            color: #667eea;
            text-decoration: none;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        /* 门户主界面样式 */
        .portal-container {
            display: none;
            min-height: 100vh;
            background: #f5f7fa;
        }

        .portal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0 20px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .portal-logo {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 1.5rem;
            font-weight: 600;
        }

        .portal-logo i {
            font-size: 2rem;
        }

        .portal-nav {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .portal-nav a {
            color: white;
            text-decoration: none;
            padding: 10px 15px;
            border-radius: 8px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .portal-nav a:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .user-details {
            display: flex;
            flex-direction: column;
        }

        .user-name {
            font-weight: 600;
            font-size: 1rem;
        }

        .user-role {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 15px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 主要内容区域 */
        .portal-main {
            display: grid;
            grid-template-columns: 250px 1fr;
            min-height: calc(100vh - 70px);
        }

        .sidebar {
            background: white;
            border-right: 1px solid #e0e6ed;
            padding: 20px 0;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.05);
        }

        .sidebar-menu {
            list-style: none;
        }

        .sidebar-menu li {
            margin-bottom: 5px;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #666;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-menu a:hover,
        .sidebar-menu a.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border-left-color: #667eea;
        }

        .sidebar-menu i {
            width: 20px;
            text-align: center;
        }

        .content-area {
            padding: 30px;
            overflow-y: auto;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .welcome-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .welcome-content {
            position: relative;
            z-index: 1;
        }

        .welcome-title {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .welcome-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .welcome-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-box {
                width: 90%;
                padding: 30px 20px;
            }

            .portal-main {
                grid-template-columns: 1fr;
            }

            .sidebar {
                display: none;
            }

            .portal-nav {
                display: none;
            }

            .content-area {
                padding: 20px;
            }

            .welcome-stats {
                grid-template-columns: 1fr 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- 登录界面 -->
    <div class="login-container" id="loginContainer">
        <div class="login-box">
            <div class="login-logo">
                <i class="fas fa-building"></i>
                <h1>企业统一门户</h1>
                <p>Enterprise Unified Portal</p>
            </div>

            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <i class="fas fa-user"></i>
                    <input type="text" class="form-input" placeholder="用户名" id="username" required>
                </div>

                <div class="form-group">
                    <i class="fas fa-lock"></i>
                    <input type="password" class="form-input" placeholder="密码" id="password" required>
                </div>

                <div class="role-selector">
                    <div class="role-option" data-role="admin">
                        <i class="fas fa-crown"></i>
                        <div>系统管理员</div>
                    </div>
                    <div class="role-option" data-role="manager">
                        <i class="fas fa-user-tie"></i>
                        <div>部门经理</div>
                    </div>
                    <div class="role-option" data-role="employee">
                        <i class="fas fa-user"></i>
                        <div>普通员工</div>
                    </div>
                    <div class="role-option" data-role="finance">
                        <i class="fas fa-calculator"></i>
                        <div>财务人员</div>
                    </div>
                </div>

                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i> 登录系统
                </button>

                <div class="login-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span>记住我</span>
                    </label>
                    <a href="#" class="forgot-password">忘记密码？</a>
                </div>
            </form>
        </div>
    </div>

    <!-- 门户主界面 -->
    <div class="portal-container" id="portalContainer">
        <!-- 头部导航 -->
        <header class="portal-header">
            <div class="portal-logo">
                <i class="fas fa-building"></i>
                <span>企业统一门户</span>
            </div>

            <nav>
                <ul class="portal-nav">
                    <li><a href="#" data-section="dashboard"><i class="fas fa-tachometer-alt"></i>工作台</a></li>
                    <li><a href="#" data-section="systems"><i class="fas fa-th-large"></i>业务系统</a></li>
                    <li><a href="#" data-section="reports"><i class="fas fa-chart-bar"></i>数据报表</a></li>
                    <li><a href="#" data-section="notifications"><i class="fas fa-bell"></i>消息中心</a></li>
                </ul>
            </nav>

            <div class="user-info">
                <div class="user-avatar" id="userAvatar">A</div>
                <div class="user-details">
                    <div class="user-name" id="userName">管理员</div>
                    <div class="user-role" id="userRole">系统管理员</div>
                </div>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i> 退出
                </button>
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="portal-main">
            <!-- 侧边栏 -->
            <aside class="sidebar">
                <ul class="sidebar-menu" id="sidebarMenu">
                    <!-- 菜单项将通过JavaScript动态生成 -->
                </ul>
            </aside>

            <!-- 内容区域 -->
            <div class="content-area">
                <!-- 欢迎区域 -->
                <section class="welcome-section">
                    <div class="welcome-content">
                        <h2 class="welcome-title" id="welcomeTitle">欢迎回来，管理员</h2>
                        <p class="welcome-subtitle" id="welcomeSubtitle">今天是个美好的工作日，让我们开始高效的工作吧！</p>
                        
                        <div class="welcome-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="todayTasks">12</span>
                                <span class="stat-label">今日待办</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="unreadMessages">8</span>
                                <span class="stat-label">未读消息</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="activeProjects">5</span>
                                <span class="stat-label">进行项目</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="systemStatus">99.9%</span>
                                <span class="stat-label">系统可用率</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- 动态内容区域 -->
                <div id="dynamicContent">
                    <!-- 内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </main>
    </div>

    <script>
        // 全局变量
        let currentUser = null;
        let currentRole = null;
        let userPermissions = [];

        // 权限配置
        const PERMISSIONS = {
            // 系统管理权限
            'system.admin': '系统管理',
            'system.config': '系统配置',
            'system.monitor': '系统监控',

            // 用户管理权限
            'user.view': '查看用户',
            'user.create': '创建用户',
            'user.edit': '编辑用户',
            'user.delete': '删除用户',

            // 业务系统权限
            'business.oa': 'OA系统访问',
            'business.finance': '财务系统访问',
            'business.hr': '人事系统访问',
            'business.crm': 'CRM系统访问',
            'business.project': '项目管理访问',
            'business.inventory': '库存管理访问',
            'business.bi': '商业智能访问',

            // 数据权限
            'data.view': '查看数据',
            'data.export': '导出数据',
            'data.analysis': '数据分析',

            // 审批权限
            'approval.finance': '财务审批',
            'approval.hr': '人事审批',
            'approval.project': '项目审批'
        };

        // 角色权限映射
        const ROLE_PERMISSIONS = {
            'admin': [
                'system.admin', 'system.config', 'system.monitor',
                'user.view', 'user.create', 'user.edit', 'user.delete',
                'business.oa', 'business.finance', 'business.hr', 'business.crm',
                'business.project', 'business.inventory', 'business.bi',
                'data.view', 'data.export', 'data.analysis',
                'approval.finance', 'approval.hr', 'approval.project'
            ],
            'manager': [
                'user.view', 'user.edit',
                'business.oa', 'business.finance', 'business.hr', 'business.crm',
                'business.project', 'business.bi',
                'data.view', 'data.export', 'data.analysis',
                'approval.finance', 'approval.hr', 'approval.project'
            ],
            'finance': [
                'business.oa', 'business.finance', 'business.bi',
                'data.view', 'data.export',
                'approval.finance'
            ],
            'employee': [
                'business.oa', 'business.project', 'business.crm',
                'data.view'
            ]
        };

        // 权限检查函数
        function hasPermission(permission) {
            return userPermissions.includes(permission);
        }

        // 检查多个权限（需要全部满足）
        function hasAllPermissions(permissions) {
            return permissions.every(permission => hasPermission(permission));
        }

        // 检查多个权限（满足任一即可）
        function hasAnyPermission(permissions) {
            return permissions.some(permission => hasPermission(permission));
        }

        // 原有的页面初始化已移至文件末尾

        // 初始化登录功能
        function initializeLogin() {
            const roleOptions = document.querySelectorAll('.role-option');
            const loginForm = document.getElementById('loginForm');

            // 角色选择
            roleOptions.forEach(option => {
                option.addEventListener('click', () => {
                    roleOptions.forEach(opt => opt.classList.remove('selected'));
                    option.classList.add('selected');
                    currentRole = option.getAttribute('data-role');
                });
            });

            // 登录表单提交
            loginForm.addEventListener('submit', handleLogin);
        }

        // 处理登录
        function handleLogin(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                showNotification('请输入用户名和密码', 'error');
                return;
            }
            
            if (!currentRole) {
                showNotification('请选择用户角色', 'error');
                return;
            }

            // 模拟登录验证
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.disabled = true;
            loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

            setTimeout(() => {
                // 模拟登录成功
                currentUser = {
                    username: username,
                    role: currentRole,
                    name: getRoleName(currentRole),
                    avatar: username.charAt(0).toUpperCase()
                };

                // 初始化用户权限
                userPermissions = ROLE_PERMISSIONS[currentRole] || [];

                showPortal();
                showNotification(`登录成功！您拥有 ${userPermissions.length} 项权限`, 'success');

                // 显示权限信息（仅管理员可见）
                if (hasPermission('system.admin')) {
                    setTimeout(() => {
                        showNotification('管理员权限已激活', 'info');
                    }, 2000);
                }
            }, 1500);
        }

        // 获取角色名称
        function getRoleName(role) {
            const roleNames = {
                'admin': '系统管理员',
                'manager': '部门经理',
                'employee': '普通员工',
                'finance': '财务人员'
            };
            return roleNames[role] || '未知角色';
        }

        // 显示门户界面
        function showPortal() {
            document.getElementById('loginContainer').style.display = 'none';
            document.getElementById('portalContainer').style.display = 'block';
            
            // 更新用户信息
            document.getElementById('userAvatar').textContent = currentUser.avatar;
            document.getElementById('userName').textContent = currentUser.username;
            document.getElementById('userRole').textContent = currentUser.name;
            document.getElementById('welcomeTitle').textContent = `欢迎回来，${currentUser.username}`;
            
            // 根据角色生成菜单
            generateSidebarMenu();
            
            // 加载默认内容
            loadDashboard();
        }

        // 生成侧边栏菜单
        function generateSidebarMenu() {
            const menuContainer = document.getElementById('sidebarMenu');
            const menus = getMenusByRole(currentUser.role);
            
            menuContainer.innerHTML = menus.map(menu => `
                <li>
                    <a href="#" onclick="loadContent('${menu.id}')" class="${menu.id === 'dashboard' ? 'active' : ''}">
                        <i class="${menu.icon}"></i>
                        <span>${menu.name}</span>
                    </a>
                </li>
            `).join('');
        }

        // 根据角色获取菜单
        function getMenusByRole(role) {
            const allMenus = {
                dashboard: { id: 'dashboard', name: '工作台', icon: 'fas fa-tachometer-alt' },
                systems: { id: 'systems', name: '业务系统', icon: 'fas fa-th-large' },
                reports: { id: 'reports', name: '数据报表', icon: 'fas fa-chart-bar' },
                notifications: { id: 'notifications', name: '消息中心', icon: 'fas fa-bell' },
                users: { id: 'users', name: '用户管理', icon: 'fas fa-users' },
                settings: { id: 'settings', name: '系统设置', icon: 'fas fa-cog' },
                finance: { id: 'finance', name: '财务管理', icon: 'fas fa-calculator' },
                hr: { id: 'hr', name: '人事管理', icon: 'fas fa-user-friends' },
                projects: { id: 'projects', name: '项目管理', icon: 'fas fa-project-diagram' }
            };

            // 根据角色返回不同的菜单
            switch(role) {
                case 'admin':
                    return Object.values(allMenus);
                case 'manager':
                    return [allMenus.dashboard, allMenus.systems, allMenus.reports, allMenus.notifications, allMenus.projects, allMenus.hr];
                case 'finance':
                    return [allMenus.dashboard, allMenus.systems, allMenus.reports, allMenus.notifications, allMenus.finance];
                case 'employee':
                    return [allMenus.dashboard, allMenus.systems, allMenus.notifications, allMenus.projects];
                default:
                    return [allMenus.dashboard, allMenus.notifications];
            }
        }

        // 加载内容
        function loadContent(contentId) {
            // 更新菜单激活状态
            document.querySelectorAll('.sidebar-menu a').forEach(link => {
                link.classList.remove('active');
            });
            event.target.closest('a').classList.add('active');

            // 根据内容ID加载相应内容
            switch(contentId) {
                case 'dashboard':
                    loadDashboard();
                    break;
                case 'systems':
                    loadBusinessSystems();
                    break;
                case 'reports':
                    loadReports();
                    break;
                case 'notifications':
                    loadNotifications();
                    break;
                default:
                    loadDefaultContent(contentId);
            }
        }

        // 加载工作台
        function loadDashboard() {
            const content = `
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                    <!-- 快捷入口 -->
                    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 15px; color: #667eea;"><i class="fas fa-rocket"></i> 快捷入口</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                            <button onclick="loadContent('systems')" style="padding: 15px; border: none; background: #667eea; color: white; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-th-large"></i><br>业务系统
                            </button>
                            <button onclick="loadContent('reports')" style="padding: 15px; border: none; background: #764ba2; color: white; border-radius: 8px; cursor: pointer;">
                                <i class="fas fa-chart-bar"></i><br>数据报表
                            </button>
                        </div>
                    </div>

                    <!-- 待办事项 -->
                    <div style="background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 15px; color: #667eea;"><i class="fas fa-tasks"></i> 待办事项</h3>
                        <div style="space-y: 10px;">
                            <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; margin-bottom: 8px;">
                                <div style="font-weight: 600;">审批财务报销单</div>
                                <div style="font-size: 0.8rem; color: #666;">截止时间: 今天 18:00</div>
                            </div>
                            <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; margin-bottom: 8px;">
                                <div style="font-weight: 600;">项目进度汇报</div>
                                <div style="font-size: 0.8rem; color: #666;">截止时间: 明天 10:00</div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('dynamicContent').innerHTML = content;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                // 清理用户信息和权限
                currentUser = null;
                currentRole = null;
                userPermissions = [];

                // 重置界面
                document.getElementById('portalContainer').style.display = 'none';
                document.getElementById('loginContainer').style.display = 'flex';
                document.getElementById('loginForm').reset();
                document.querySelectorAll('.role-option').forEach(opt => opt.classList.remove('selected'));
                document.getElementById('loginBtn').disabled = false;
                document.getElementById('loginBtn').innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录系统';

                // 记录退出日志
                console.log('用户退出登录:', {
                    timestamp: new Date().toLocaleString(),
                    action: 'logout'
                });

                showNotification('已安全退出系统', 'success');
            }
        }

        // 加载业务系统
        function loadBusinessSystems() {
            const systems = getSystemsByRole(currentUser.role);
            const content = `
                <div style="margin-bottom: 20px;">
                    <h2 style="color: #667eea; margin-bottom: 10px;"><i class="fas fa-th-large"></i> 业务系统集成</h2>
                    <p style="color: #666; margin-bottom: 30px;">统一访问入口，实现单点登录各业务系统</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px;">
                    ${systems.map(system => `
                        <div class="system-card" style="background: white; border-radius: 12px; padding: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s ease; cursor: pointer;" onclick="accessSystem('${system.id}')">
                            <div style="display: flex; align-items: center; margin-bottom: 15px;">
                                <div style="width: 50px; height: 50px; background: ${system.color}; border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 15px;">
                                    <i class="${system.icon}" style="color: white; font-size: 1.5rem;"></i>
                                </div>
                                <div>
                                    <h3 style="margin: 0; color: #333; font-size: 1.2rem;">${system.name}</h3>
                                    <p style="margin: 5px 0 0 0; color: #666; font-size: 0.9rem;">${system.description}</p>
                                </div>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
                                <span style="background: ${system.status === 'online' ? '#e8f5e8' : '#ffeaa7'}; color: ${system.status === 'online' ? '#27ae60' : '#f39c12'}; padding: 4px 12px; border-radius: 15px; font-size: 0.8rem;">
                                    ${system.status === 'online' ? '在线' : '维护中'}
                                </span>
                                <span style="color: #999; font-size: 0.8rem;">最后访问: ${system.lastAccess}</span>
                            </div>
                        </div>
                    `).join('')}
                </div>
            `;
            document.getElementById('dynamicContent').innerHTML = content;
        }

        // 根据权限获取可访问的系统
        function getSystemsByRole(role) {
            const allSystems = [
                { id: 'oa', name: 'OA办公系统', description: '日常办公、流程审批', icon: 'fas fa-briefcase', color: '#667eea', status: 'online', lastAccess: '2小时前', permission: 'business.oa' },
                { id: 'finance', name: '财务管理系统', description: '财务核算、报表分析', icon: 'fas fa-calculator', color: '#27ae60', status: 'online', lastAccess: '1天前', permission: 'business.finance' },
                { id: 'hr', name: '人力资源系统', description: '人事管理、薪资考勤', icon: 'fas fa-user-friends', color: '#e74c3c', status: 'online', lastAccess: '3天前', permission: 'business.hr' },
                { id: 'crm', name: '客户关系管理', description: '客户信息、销售管理', icon: 'fas fa-handshake', color: '#f39c12', status: 'online', lastAccess: '1小时前', permission: 'business.crm' },
                { id: 'project', name: '项目管理系统', description: '项目进度、任务分配', icon: 'fas fa-project-diagram', color: '#9b59b6', status: 'online', lastAccess: '30分钟前', permission: 'business.project' },
                { id: 'inventory', name: '库存管理系统', description: '库存监控、采购管理', icon: 'fas fa-warehouse', color: '#34495e', status: 'maintenance', lastAccess: '1周前', permission: 'business.inventory' },
                { id: 'bi', name: '商业智能系统', description: '数据分析、决策支持', icon: 'fas fa-chart-line', color: '#16a085', status: 'online', lastAccess: '2小时前', permission: 'business.bi' },
                { id: 'admin', name: '系统管理平台', description: '用户权限、系统配置', icon: 'fas fa-cogs', color: '#2c3e50', status: 'online', lastAccess: '10分钟前', permission: 'system.admin' }
            ];

            // 根据权限过滤系统
            return allSystems.filter(system => hasPermission(system.permission));
        }

        // 访问系统
        function accessSystem(systemId) {
            // 权限映射
            const systemPermissions = {
                'oa': 'business.oa',
                'finance': 'business.finance',
                'hr': 'business.hr',
                'crm': 'business.crm',
                'project': 'business.project',
                'inventory': 'business.inventory',
                'bi': 'business.bi',
                'admin': 'system.admin'
            };

            const requiredPermission = systemPermissions[systemId];

            if (!hasPermission(requiredPermission)) {
                showNotification('权限不足，无法访问该系统', 'error');
                return;
            }

            showNotification(`正在跳转到${systemId}系统...`, 'info');

            // 模拟系统跳转
            setTimeout(() => {
                showNotification('系统访问成功！', 'success');

                // 记录访问日志
                logSystemAccess(systemId);
            }, 1500);
        }

        // 记录系统访问日志
        function logSystemAccess(systemId) {
            const accessLog = {
                user: currentUser.username,
                system: systemId,
                timestamp: new Date().toLocaleString(),
                ip: '*************' // 模拟IP
            };

            console.log('系统访问日志:', accessLog);

            // 在实际应用中，这里会发送到后端记录访问日志
        }

        // 加载数据报表
        function loadReports() {
            const content = `
                <div style="margin-bottom: 20px;">
                    <h2 style="color: #667eea; margin-bottom: 10px;"><i class="fas fa-chart-bar"></i> 数据统计报表</h2>
                    <p style="color: #666; margin-bottom: 30px;">综合业务数据统计分析与可视化展示</p>
                </div>

                <!-- KPI指标卡片 -->
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px;">
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                        <i class="fas fa-users" style="font-size: 2.5rem; margin-bottom: 15px; opacity: 0.8;"></i>
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">1,247</div>
                        <div style="opacity: 0.9;">总用户数</div>
                        <div style="font-size: 0.8rem; margin-top: 10px; opacity: 0.7;">
                            <i class="fas fa-arrow-up"></i> +12.5% 本月
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #27ae60, #2ecc71); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                        <i class="fas fa-chart-line" style="font-size: 2.5rem; margin-bottom: 15px; opacity: 0.8;"></i>
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">¥2.4M</div>
                        <div style="opacity: 0.9;">月度营收</div>
                        <div style="font-size: 0.8rem; margin-top: 10px; opacity: 0.7;">
                            <i class="fas fa-arrow-up"></i> +8.3% 环比
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                        <i class="fas fa-tasks" style="font-size: 2.5rem; margin-bottom: 15px; opacity: 0.8;"></i>
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">156</div>
                        <div style="opacity: 0.9;">进行项目</div>
                        <div style="font-size: 0.8rem; margin-top: 10px; opacity: 0.7;">
                            <i class="fas fa-arrow-down"></i> -2.1% 本周
                        </div>
                    </div>

                    <div style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; padding: 25px; border-radius: 12px; text-align: center;">
                        <i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; margin-bottom: 15px; opacity: 0.8;"></i>
                        <div style="font-size: 2rem; font-weight: bold; margin-bottom: 5px;">23</div>
                        <div style="opacity: 0.9;">待处理问题</div>
                        <div style="font-size: 0.8rem; margin-top: 10px; opacity: 0.7;">
                            <i class="fas fa-arrow-up"></i> +5 今日新增
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 20px;">
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 20px; color: #333;"><i class="fas fa-chart-area"></i> 业务趋势分析</h3>
                        <canvas id="trendChart" width="400" height="200"></canvas>
                    </div>

                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 20px; color: #333;"><i class="fas fa-chart-pie"></i> 部门分布</h3>
                        <canvas id="departmentChart" width="200" height="200"></canvas>
                    </div>
                </div>
            `;

            document.getElementById('dynamicContent').innerHTML = content;

            // 初始化图表
            setTimeout(() => {
                initializeCharts();
            }, 100);
        }

        // 初始化图表
        function initializeCharts() {
            // 趋势图表
            const trendCtx = document.getElementById('trendChart');
            if (trendCtx) {
                new Chart(trendCtx, {
                    type: 'line',
                    data: {
                        labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                        datasets: [{
                            label: '营收(万元)',
                            data: [120, 190, 300, 500, 200, 300],
                            borderColor: '#667eea',
                            backgroundColor: 'rgba(102, 126, 234, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: '用户增长',
                            data: [80, 120, 180, 220, 160, 240],
                            borderColor: '#27ae60',
                            backgroundColor: 'rgba(39, 174, 96, 0.1)',
                            tension: 0.4,
                            fill: true
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'top',
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            }

            // 部门分布饼图
            const deptCtx = document.getElementById('departmentChart');
            if (deptCtx) {
                new Chart(deptCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['技术部', '销售部', '财务部', '人事部', '运营部'],
                        datasets: [{
                            data: [30, 25, 15, 12, 18],
                            backgroundColor: [
                                '#667eea',
                                '#27ae60',
                                '#f39c12',
                                '#e74c3c',
                                '#9b59b6'
                            ]
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                            }
                        }
                    }
                });
            }
        }

        // 加载消息通知
        function loadNotifications() {
            const content = `
                <div style="margin-bottom: 20px;">
                    <h2 style="color: #667eea; margin-bottom: 10px;"><i class="fas fa-bell"></i> 消息通知中心</h2>
                    <p style="color: #666; margin-bottom: 30px;">系统通知、公告信息和待办事项统一管理</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <!-- 系统通知 -->
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 20px; color: #333; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-bullhorn" style="color: #667eea;"></i> 系统公告
                        </h3>
                        <div style="space-y: 15px;">
                            <div style="padding: 15px; border-left: 4px solid #667eea; background: #f8f9ff; margin-bottom: 15px;">
                                <div style="font-weight: 600; margin-bottom: 5px;">系统维护通知</div>
                                <div style="font-size: 0.9rem; color: #666; margin-bottom: 8px;">本周六凌晨2:00-6:00进行系统升级维护，期间系统将暂停服务。</div>
                                <div style="font-size: 0.8rem; color: #999;">发布时间: 2024-01-15 14:30</div>
                            </div>

                            <div style="padding: 15px; border-left: 4px solid #27ae60; background: #f8fff8; margin-bottom: 15px;">
                                <div style="font-weight: 600; margin-bottom: 5px;">新功能上线</div>
                                <div style="font-size: 0.9rem; color: #666; margin-bottom: 8px;">移动端APP已正式上线，支持移动办公和消息推送功能。</div>
                                <div style="font-size: 0.8rem; color: #999;">发布时间: 2024-01-14 09:15</div>
                            </div>

                            <div style="padding: 15px; border-left: 4px solid #f39c12; background: #fffbf0; margin-bottom: 15px;">
                                <div style="font-weight: 600; margin-bottom: 5px;">安全提醒</div>
                                <div style="font-size: 0.9rem; color: #666; margin-bottom: 8px;">请定期修改密码，不要在公共场所登录系统，保护账户安全。</div>
                                <div style="font-size: 0.8rem; color: #999;">发布时间: 2024-01-13 16:45</div>
                            </div>
                        </div>
                    </div>

                    <!-- 待办事项 -->
                    <div style="background: white; padding: 25px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1);">
                        <h3 style="margin-bottom: 20px; color: #333; display: flex; align-items: center; gap: 10px;">
                            <i class="fas fa-tasks" style="color: #e74c3c;"></i> 待办事项
                            <span style="background: #e74c3c; color: white; font-size: 0.7rem; padding: 2px 8px; border-radius: 10px;">8</span>
                        </h3>
                        <div style="space-y: 12px;">
                            <div style="display: flex; align-items: center; padding: 12px; background: #fff5f5; border-radius: 8px; margin-bottom: 12px;">
                                <div style="width: 8px; height: 8px; background: #e74c3c; border-radius: 50%; margin-right: 12px;"></div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; margin-bottom: 3px;">审批财务报销单</div>
                                    <div style="font-size: 0.8rem; color: #666;">紧急 • 截止: 今天 18:00</div>
                                </div>
                                <button style="background: #e74c3c; color: white; border: none; padding: 5px 12px; border-radius: 4px; font-size: 0.8rem; cursor: pointer;">处理</button>
                            </div>

                            <div style="display: flex; align-items: center; padding: 12px; background: #fff9e6; border-radius: 8px; margin-bottom: 12px;">
                                <div style="width: 8px; height: 8px; background: #f39c12; border-radius: 50%; margin-right: 12px;"></div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; margin-bottom: 3px;">项目进度汇报</div>
                                    <div style="font-size: 0.8rem; color: #666;">普通 • 截止: 明天 10:00</div>
                                </div>
                                <button style="background: #f39c12; color: white; border: none; padding: 5px 12px; border-radius: 4px; font-size: 0.8rem; cursor: pointer;">查看</button>
                            </div>

                            <div style="display: flex; align-items: center; padding: 12px; background: #f0f8ff; border-radius: 8px; margin-bottom: 12px;">
                                <div style="width: 8px; height: 8px; background: #667eea; border-radius: 50%; margin-right: 12px;"></div>
                                <div style="flex: 1;">
                                    <div style="font-weight: 600; margin-bottom: 3px;">月度会议安排</div>
                                    <div style="font-size: 0.8rem; color: #666;">一般 • 截止: 本周五</div>
                                </div>
                                <button style="background: #667eea; color: white; border: none; padding: 5px 12px; border-radius: 4px; font-size: 0.8rem; cursor: pointer;">安排</button>
                            </div>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <button style="background: transparent; border: 1px solid #667eea; color: #667eea; padding: 8px 20px; border-radius: 6px; cursor: pointer;">
                                查看全部待办 (8)
                            </button>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('dynamicContent').innerHTML = content;
        }

        function loadDefaultContent(contentId) {
            const contentMap = {
                'users': { title: '用户管理', icon: 'fas fa-users', desc: '用户账户管理、角色权限分配' },
                'settings': { title: '系统设置', icon: 'fas fa-cog', desc: '系统参数配置、功能开关管理' },
                'finance': { title: '财务管理', icon: 'fas fa-calculator', desc: '财务数据管理、报表生成' },
                'hr': { title: '人事管理', icon: 'fas fa-user-friends', desc: '员工信息管理、考勤薪资' },
                'projects': { title: '项目管理', icon: 'fas fa-project-diagram', desc: '项目进度跟踪、任务分配' }
            };

            const moduleInfo = contentMap[contentId] || { title: contentId, icon: 'fas fa-tools', desc: '功能模块' };

            document.getElementById('dynamicContent').innerHTML = `
                <div style="text-align: center; padding: 60px 20px; animation: fadeInUp 0.6s ease-out;">
                    <div style="width: 120px; height: 120px; background: linear-gradient(135deg, #667eea, #764ba2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 30px; box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);">
                        <i class="${moduleInfo.icon}" style="font-size: 3rem; color: white;"></i>
                    </div>
                    <h2 style="color: #667eea; margin-bottom: 15px; font-size: 2rem;">${moduleInfo.title}</h2>
                    <p style="color: #666; font-size: 1.1rem; margin-bottom: 30px;">${moduleInfo.desc}</p>
                    <div style="background: rgba(102, 126, 234, 0.1); border-radius: 10px; padding: 20px; max-width: 500px; margin: 0 auto;">
                        <p style="color: #667eea; font-weight: 600; margin-bottom: 10px;">
                            <i class="fas fa-info-circle"></i> 开发进度
                        </p>
                        <div style="background: #e0e6ed; border-radius: 10px; height: 8px; margin-bottom: 10px;">
                            <div style="background: linear-gradient(135deg, #667eea, #764ba2); height: 100%; border-radius: 10px; width: 75%; animation: progressBar 2s ease-out;"></div>
                        </div>
                        <p style="color: #666; font-size: 0.9rem;">功能开发中，预计完成度 75%</p>
                    </div>
                </div>

                <style>
                    @keyframes fadeInUp {
                        from { opacity: 0; transform: translateY(30px); }
                        to { opacity: 1; transform: translateY(0); }
                    }

                    @keyframes progressBar {
                        from { width: 0%; }
                        to { width: 75%; }
                    }
                </style>
            `;
        }

        // 添加页面加载动画
        function addLoadingAnimation() {
            const content = document.getElementById('dynamicContent');
            content.style.opacity = '0';
            content.style.transform = 'translateY(20px)';

            setTimeout(() => {
                content.style.transition = 'all 0.5s ease-out';
                content.style.opacity = '1';
                content.style.transform = 'translateY(0)';
            }, 100);
        }

        // 增强系统卡片交互效果
        function enhanceSystemCards() {
            const style = document.createElement('style');
            style.textContent = `
                .system-card:hover {
                    transform: translateY(-5px) !important;
                    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
                }

                .system-card:active {
                    transform: translateY(-2px) !important;
                }

                .notification-enter {
                    animation: notificationSlide 0.3s ease-out;
                }

                @keyframes notificationSlide {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }

                .fade-transition {
                    transition: all 0.3s ease-out;
                }

                .pulse-animation {
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0% { transform: scale(1); }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); }
                }
            `;
            document.head.appendChild(style);
        }

        // 实时时间显示
        function updateRealTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // 更新欢迎区域的时间信息
            const subtitle = document.getElementById('welcomeSubtitle');
            if (subtitle) {
                subtitle.textContent = `今天是 ${timeString}，让我们开始高效的工作吧！`;
            }
        }

        // 模拟实时数据更新
        function simulateRealTimeData() {
            // 更新统计数据
            const stats = {
                todayTasks: Math.floor(Math.random() * 5) + 10,
                unreadMessages: Math.floor(Math.random() * 3) + 6,
                activeProjects: Math.floor(Math.random() * 2) + 4,
                systemStatus: (99.5 + Math.random() * 0.4).toFixed(1)
            };

            Object.keys(stats).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.textContent = key === 'systemStatus' ? stats[key] + '%' : stats[key];
                    element.classList.add('pulse-animation');
                    setTimeout(() => {
                        element.classList.remove('pulse-animation');
                    }, 2000);
                }
            });
        }

        // 键盘快捷键支持
        function setupKeyboardShortcuts() {
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + 数字键快速切换菜单
                if ((e.ctrlKey || e.metaKey) && e.key >= '1' && e.key <= '9') {
                    e.preventDefault();
                    const menuItems = document.querySelectorAll('.sidebar-menu a');
                    const index = parseInt(e.key) - 1;
                    if (menuItems[index]) {
                        menuItems[index].click();
                    }
                }

                // ESC键返回工作台
                if (e.key === 'Escape') {
                    const dashboardLink = document.querySelector('.sidebar-menu a[onclick*="dashboard"]');
                    if (dashboardLink) {
                        dashboardLink.click();
                    }
                }

                // F5刷新当前页面内容
                if (e.key === 'F5') {
                    e.preventDefault();
                    const activeLink = document.querySelector('.sidebar-menu a.active');
                    if (activeLink) {
                        activeLink.click();
                    }
                }
            });
        }

        // 增强通知系统
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = 'notification-enter';
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#667eea'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                display: flex;
                align-items: center;
                gap: 10px;
                max-width: 300px;
                word-wrap: break-word;
            `;

            const icon = type === 'success' ? 'fas fa-check-circle' :
                        type === 'error' ? 'fas fa-exclamation-circle' :
                        'fas fa-info-circle';

            notification.innerHTML = `
                <i class="${icon}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" style="background: none; border: none; color: white; cursor: pointer; margin-left: auto; font-size: 1.2rem;">×</button>
            `;

            document.body.appendChild(notification);

            // 自动移除
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.style.animation = 'notificationSlide 0.3s ease-out reverse';
                    setTimeout(() => {
                        notification.remove();
                    }, 300);
                }
            }, 5000);
        }

        // 页面初始化增强
        document.addEventListener('DOMContentLoaded', function() {
            initializeLogin();
            enhanceSystemCards();
            setupKeyboardShortcuts();

            // 每30秒更新一次实时数据
            setInterval(simulateRealTimeData, 30000);

            // 每秒更新时间
            setInterval(updateRealTime, 1000);
        });
    </script>
</body>
</html>
