<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧水务驾驶舱</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.9.4/leaflet.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .dashboard-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .dashboard-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px 40px;
            margin-bottom: 25px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .dashboard-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.1), transparent);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3rem;
            color: #1e3c72;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .system-status {
            display: flex;
            justify-content: center;
            gap: 40px;
            font-size: 1rem;
            color: #888;
            position: relative;
            z-index: 1;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .status-online { color: #4caf50; }
        .status-warning { color: #ff9800; }
        .status-error { color: #f44336; }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 12px;
            margin-bottom: 25px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            gap: 10px;
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            min-width: 200px;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            white-space: nowrap;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
            transform: translateY(-3px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            transform: translateY(-2px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            min-height: 800px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* KPI卡片 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 6px solid;
        }

        .kpi-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .kpi-card.supply-demand { border-left-color: #2196f3; }
        .kpi-card.water-quality { border-left-color: #4caf50; }
        .kpi-card.energy-consumption { border-left-color: #ff9800; }
        .kpi-card.equipment-health { border-left-color: #9c27b0; }
        .kpi-card.pipeline-loss { border-left-color: #f44336; }
        .kpi-card.emergency { border-left-color: #e91e63; }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .kpi-title {
            font-size: 1.2rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .kpi-trend {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .kpi-value {
            font-size: 2.8rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
            gap: 10px;
        }

        .kpi-unit {
            font-size: 1.1rem;
            color: #888;
        }

        .kpi-comparison {
            display: flex;
            justify-content: space-between;
            font-size: 0.95rem;
            color: #666;
        }

        /* 图表容器 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 35px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e3c72;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chart-controls {
            display: flex;
            gap: 12px;
        }

        .chart-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #666;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .chart-btn.active {
            background: #1e3c72;
            color: white;
            border-color: #1e3c72;
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
            border-color: #1e3c72;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        /* 地图容器 */
        .map-container {
            height: 600px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 25px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(30, 60, 114, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 600;
        }

        .data-table .status-normal { color: #4caf50; font-weight: 600; }
        .data-table .status-warning { color: #ff9800; font-weight: 600; }
        .data-table .status-danger { color: #f44336; font-weight: 600; }

        /* 预警面板 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 6px solid #ffc107;
            border-radius: 15px;
            padding: 20px 25px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.danger {
            background: linear-gradient(135deg, #f8d7da, #fab1a0);
            border-left-color: #dc3545;
            box-shadow: 0 8px 32px rgba(220, 53, 69, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
        }

        .alert-icon {
            font-size: 2rem;
            color: #856404;
        }

        .alert-panel.danger .alert-icon { color: #721c24; }
        .alert-panel.success .alert-icon { color: #155724; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #856404;
        }

        .alert-panel.danger .alert-title { color: #721c24; }
        .alert-panel.success .alert-title { color: #155724; }

        .alert-message {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #856404;
        }

        .alert-panel.danger .alert-message { color: #721c24; }
        .alert-panel.success .alert-message { color: #155724; }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dashboard-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.5rem;
                flex-direction: column;
                gap: 15px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
                padding: 15px 20px;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .system-status {
                flex-direction: column;
                gap: 15px;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 工具提示 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip::after {
            content: attr(data-tooltip);
            position: absolute;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 0.8rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s;
            z-index: 1000;
        }

        .tooltip:hover::after {
            opacity: 1;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            color: white;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 系统头部 -->
        <div class="dashboard-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tachometer-alt"></i>
                智慧水务驾驶舱
                <i class="fas fa-water"></i>
            </h1>
            <p class="subtitle">Smart Water Management Dashboard - 数据融合 · 智能分析 · 精准决策</p>
            <div class="system-status">
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>供水系统：正常运行</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>水质监测：达标</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-warning"></i>
                    <span>设备状态：1台预警</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>管网运行：稳定</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-sync-alt"></i>
                    <span id="updateTime">更新时间：14:30:25</span>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="supply-demand">
                <i class="fas fa-balance-scale"></i>
                供需平衡分析
            </button>
            <button class="nav-tab" data-tab="water-quality">
                <i class="fas fa-flask"></i>
                水质追溯分析
            </button>
            <button class="nav-tab" data-tab="energy-consumption">
                <i class="fas fa-bolt"></i>
                能耗药耗优化
            </button>
            <button class="nav-tab" data-tab="equipment-health">
                <i class="fas fa-cogs"></i>
                设备健康分析
            </button>
            <button class="nav-tab" data-tab="pipeline-loss">
                <i class="fas fa-map-marked-alt"></i>
                管网漏损分析
            </button>
            <button class="nav-tab" data-tab="emergency">
                <i class="fas fa-exclamation-triangle"></i>
                应急事件处置
            </button>
            <button class="nav-tab" data-tab="gis-system">
                <i class="fas fa-map"></i>
                管网GIS系统
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 供需平衡分析 -->
            <div id="supply-demand" class="tab-content active">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-balance-scale"></i>
                    水量供需平衡分析
                </h2>

                <!-- 预警信息 -->
                <div class="alert-panel">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">供需平衡预警</div>
                        <div class="alert-message">预测明日14:00-16:00用水高峰期供需缺口约2,500m³，建议提前调整制水计划并启用备用水源</div>
                    </div>
                </div>

                <!-- 关键指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card supply-demand">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                当前供水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            1,850
                            <span class="kpi-unit">m³/h</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>昨日同期：1,790</span>
                            <span>设计能力：2,200</span>
                        </div>
                    </div>

                    <div class="kpi-card supply-demand">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-chart-line"></i>
                                预测用水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            1,920
                            <span class="kpi-unit">m³/h</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>峰值预测：2,350</span>
                            <span>置信度：94.2%</span>
                        </div>
                    </div>

                    <div class="kpi-card supply-demand">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-circle"></i>
                                供需缺口
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                风险
                            </div>
                        </div>
                        <div class="kpi-value">
                            -70
                            <span class="kpi-unit">m³/h</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>峰值缺口：-150</span>
                            <span>安全余量：10%</span>
                        </div>
                    </div>

                    <div class="kpi-card supply-demand">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-layer-group"></i>
                                清水池液位
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -2.1%
                            </div>
                        </div>
                        <div class="kpi-value">
                            3.2
                            <span class="kpi-unit">m</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>安全水位：2.5-4.5m</span>
                            <span>储量：68%</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                供需平衡趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="supplyDemandChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                用水结构分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="waterUsageChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 历史数据分析 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-table"></i>
                            历史用水数据分析
                        </h3>
                        <button class="btn btn-primary" onclick="exportData()">
                            <i class="fas fa-download"></i>
                            导出数据
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间段</th>
                                <th>平均用水量</th>
                                <th>峰值用水量</th>
                                <th>供水量</th>
                                <th>供需比</th>
                                <th>节假日影响</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>06:00-08:00</td>
                                <td class="number">1,650 m³/h</td>
                                <td class="number">1,890 m³/h</td>
                                <td class="number">1,850 m³/h</td>
                                <td class="number">112%</td>
                                <td>+15%</td>
                                <td class="status-normal">正常</td>
                            </tr>
                            <tr>
                                <td>08:00-10:00</td>
                                <td class="number">1,820 m³/h</td>
                                <td class="number">2,150 m³/h</td>
                                <td class="number">1,850 m³/h</td>
                                <td class="number">102%</td>
                                <td>+8%</td>
                                <td class="status-warning">紧张</td>
                            </tr>
                            <tr>
                                <td>10:00-12:00</td>
                                <td class="number">1,920 m³/h</td>
                                <td class="number">2,280 m³/h</td>
                                <td class="number">1,850 m³/h</td>
                                <td class="number">96%</td>
                                <td>+12%</td>
                                <td class="status-warning">紧张</td>
                            </tr>
                            <tr>
                                <td>12:00-14:00</td>
                                <td class="number">2,050 m³/h</td>
                                <td class="number">2,350 m³/h</td>
                                <td class="number">1,850 m³/h</td>
                                <td class="number">90%</td>
                                <td>+18%</td>
                                <td class="status-danger">缺口</td>
                            </tr>
                            <tr>
                                <td>14:00-16:00</td>
                                <td class="number">1,980 m³/h</td>
                                <td class="number">2,320 m³/h</td>
                                <td class="number">1,850 m³/h</td>
                                <td class="number">93%</td>
                                <td>+20%</td>
                                <td class="status-danger">缺口</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 水质全流程追溯分析 -->
            <div id="water-quality" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-flask"></i>
                    水质全流程追溯分析
                </h2>

                <!-- 水质预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">水质异常预警</div>
                        <div class="alert-message">出厂水浊度检测值0.85 NTU，接近标准限值1.0 NTU，已启动数据血缘追踪分析，初步定位为2号沉淀池絮凝效果不佳</div>
                    </div>
                </div>

                <!-- 水质指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card water-quality">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-eye-dropper"></i>
                                出厂水浊度
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.15
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.85
                            <span class="kpi-unit">NTU</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>标准限值：≤1.0</span>
                            <span>昨日均值：0.70</span>
                        </div>
                    </div>

                    <div class="kpi-card water-quality">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-atom"></i>
                                余氯含量
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.45
                            <span class="kpi-unit">mg/L</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>标准范围：0.3-4.0</span>
                            <span>目标值：0.5</span>
                        </div>
                    </div>

                    <div class="kpi-card water-quality">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-thermometer-half"></i>
                                pH值
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="kpi-value">
                            7.2
                            <span class="kpi-unit">pH</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>标准范围：6.5-8.5</span>
                            <span>最优范围：7.0-7.5</span>
                        </div>
                    </div>

                    <div class="kpi-card water-quality">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-microscope"></i>
                                细菌总数
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -5 CFU
                            </div>
                        </div>
                        <div class="kpi-value">
                            15
                            <span class="kpi-unit">CFU/mL</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>标准限值：≤100</span>
                            <span>优秀水平：≤20</span>
                        </div>
                    </div>
                </div>

                <!-- 水质追溯流程图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-project-diagram"></i>
                            水质全流程追溯链路
                        </h3>
                        <button class="btn btn-primary" onclick="startTraceAnalysis()">
                            <i class="fas fa-search"></i>
                            启动追溯分析
                        </button>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <div style="display: flex; align-items: center; justify-content: space-between; flex-wrap: wrap; gap: 20px;">
                            <div style="text-align: center; flex: 1; min-width: 120px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #4caf50, #81c784); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-water"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72;">原水进厂</div>
                                <div style="font-size: 0.9rem; color: #666;">浊度: 8.5 NTU</div>
                                <div style="font-size: 0.8rem; color: #4caf50;">✓ 正常</div>
                            </div>
                            <div style="color: #1e3c72; font-size: 1.5rem;">→</div>
                            <div style="text-align: center; flex: 1; min-width: 120px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #2196f3, #64b5f6); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-flask"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72;">混凝沉淀</div>
                                <div style="font-size: 0.9rem; color: #666;">PAC: 25 mg/L</div>
                                <div style="font-size: 0.8rem; color: #ff9800;">⚠ 效果不佳</div>
                            </div>
                            <div style="color: #1e3c72; font-size: 1.5rem;">→</div>
                            <div style="text-align: center; flex: 1; min-width: 120px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #ff9800, #ffb74d); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-filter"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72;">过滤处理</div>
                                <div style="font-size: 0.9rem; color: #666;">滤速: 8.5 m/h</div>
                                <div style="font-size: 0.8rem; color: #4caf50;">✓ 正常</div>
                            </div>
                            <div style="color: #1e3c72; font-size: 1.5rem;">→</div>
                            <div style="text-align: center; flex: 1; min-width: 120px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #9c27b0, #ba68c8); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72;">消毒处理</div>
                                <div style="font-size: 0.9rem; color: #666;">氯气: 2.5 mg/L</div>
                                <div style="font-size: 0.8rem; color: #4caf50;">✓ 正常</div>
                            </div>
                            <div style="color: #1e3c72; font-size: 1.5rem;">→</div>
                            <div style="text-align: center; flex: 1; min-width: 120px;">
                                <div style="width: 80px; height: 80px; background: linear-gradient(135deg, #f44336, #ef5350); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px; color: white; font-size: 1.5rem;">
                                    <i class="fas fa-tint"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72;">出厂供水</div>
                                <div style="font-size: 0.9rem; color: #666;">浊度: 0.85 NTU</div>
                                <div style="font-size: 0.8rem; color: #ff9800;">⚠ 接近限值</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                水质指标趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchQualityPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchQualityPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchQualityPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="waterQualityTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                各环节处理效果
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="treatmentEffectChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 历史对比分析 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-history"></i>
                            历史数据对比分析
                        </h3>
                        <button class="btn btn-warning" onclick="generateQualityReport()">
                            <i class="fas fa-file-alt"></i>
                            生成分析报告
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>检测时间</th>
                                <th>浊度 (NTU)</th>
                                <th>余氯 (mg/L)</th>
                                <th>pH值</th>
                                <th>细菌总数 (CFU/mL)</th>
                                <th>处理环节</th>
                                <th>异常原因</th>
                                <th>处理措施</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>14:30</td>
                                <td class="number status-warning">0.85</td>
                                <td class="number status-normal">0.45</td>
                                <td class="number status-normal">7.2</td>
                                <td class="number status-normal">15</td>
                                <td>混凝沉淀</td>
                                <td>絮凝效果不佳</td>
                                <td>调整PAC投加量</td>
                            </tr>
                            <tr>
                                <td>14:00</td>
                                <td class="number status-warning">0.82</td>
                                <td class="number status-normal">0.48</td>
                                <td class="number status-normal">7.1</td>
                                <td class="number status-normal">18</td>
                                <td>混凝沉淀</td>
                                <td>原水浊度偏高</td>
                                <td>增加预处理</td>
                            </tr>
                            <tr>
                                <td>13:30</td>
                                <td class="number status-normal">0.75</td>
                                <td class="number status-normal">0.52</td>
                                <td class="number status-normal">7.0</td>
                                <td class="number status-normal">12</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>13:00</td>
                                <td class="number status-normal">0.68</td>
                                <td class="number status-normal">0.46</td>
                                <td class="number status-normal">7.3</td>
                                <td class="number status-normal">10</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                            <tr>
                                <td>12:30</td>
                                <td class="number status-normal">0.72</td>
                                <td class="number status-normal">0.44</td>
                                <td class="number status-normal">7.2</td>
                                <td class="number status-normal">16</td>
                                <td>-</td>
                                <td>-</td>
                                <td>-</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 能耗与药耗优化分析 -->
            <div id="energy-consumption" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-bolt"></i>
                    能耗与药耗优化分析
                </h2>

                <!-- 优化建议 -->
                <div class="alert-panel success">
                    <i class="fas fa-lightbulb alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">优化建议</div>
                        <div class="alert-message">检测到12:00-14:00时段单吨水能耗偏高，建议调整水泵运行策略，预计可节能8.5%，月节约电费约12,000元</div>
                    </div>
                </div>

                <!-- 能耗药耗指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card energy-consumption">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bolt"></i>
                                单吨水电耗
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.05
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.85
                            <span class="kpi-unit">kWh/吨</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标值：0.80</span>
                            <span>行业均值：0.90</span>
                        </div>
                    </div>

                    <div class="kpi-card energy-consumption">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-flask"></i>
                                单吨水药耗
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -0.8元
                            </div>
                        </div>
                        <div class="kpi-value">
                            12.5
                            <span class="kpi-unit">元/吨</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标值：13.0</span>
                            <span>昨日：13.3</span>
                        </div>
                    </div>

                    <div class="kpi-card energy-consumption">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-coins"></i>
                                日运行成本
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +2.1%
                            </div>
                        </div>
                        <div class="kpi-value">
                            45,680
                            <span class="kpi-unit">元</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>预算：48,000</span>
                            <span>节约：4.8%</span>
                        </div>
                    </div>

                    <div class="kpi-card energy-consumption">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-leaf"></i>
                                节能效果
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            15.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>本月节约：28,500元</span>
                            <span>年度目标：12%</span>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                能耗药耗趋势对比
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchConsumptionPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchConsumptionPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchConsumptionPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="consumptionTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                成本构成分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="costStructureChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 优化分析表格 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-search"></i>
                            高消耗区间识别分析
                        </h3>
                        <button class="btn btn-success" onclick="optimizeConsumption()">
                            <i class="fas fa-magic"></i>
                            智能优化
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间段</th>
                                <th>处理水量 (m³)</th>
                                <th>电耗 (kWh/吨)</th>
                                <th>药耗 (元/吨)</th>
                                <th>水质状况</th>
                                <th>消耗等级</th>
                                <th>优化潜力</th>
                                <th>建议措施</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>12:00-14:00</td>
                                <td class="number">4,100</td>
                                <td class="number status-danger">0.95</td>
                                <td class="number status-warning">14.2</td>
                                <td>良好</td>
                                <td class="status-danger">高消耗</td>
                                <td class="number">8.5%</td>
                                <td>调整水泵频率</td>
                            </tr>
                            <tr>
                                <td>14:00-16:00</td>
                                <td class="number">3,960</td>
                                <td class="number status-warning">0.88</td>
                                <td class="number status-warning">13.8</td>
                                <td>良好</td>
                                <td class="status-warning">中等消耗</td>
                                <td class="number">5.2%</td>
                                <td>优化加药策略</td>
                            </tr>
                            <tr>
                                <td>10:00-12:00</td>
                                <td class="number">3,840</td>
                                <td class="number status-normal">0.82</td>
                                <td class="number status-normal">12.1</td>
                                <td>优秀</td>
                                <td class="status-normal">正常消耗</td>
                                <td class="number">2.1%</td>
                                <td>保持现状</td>
                            </tr>
                            <tr>
                                <td>08:00-10:00</td>
                                <td class="number">3,320</td>
                                <td class="number status-normal">0.79</td>
                                <td class="number status-normal">11.8</td>
                                <td>优秀</td>
                                <td class="status-normal">低消耗</td>
                                <td class="number">1.5%</td>
                                <td>推广经验</td>
                            </tr>
                            <tr>
                                <td>06:00-08:00</td>
                                <td class="number">2,640</td>
                                <td class="number status-normal">0.76</td>
                                <td class="number status-normal">11.2</td>
                                <td>优秀</td>
                                <td class="status-normal">低消耗</td>
                                <td class="number">0.8%</td>
                                <td>最佳实践</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 设备健康状态分析 -->
            <div id="equipment-health" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-cogs"></i>
                    设备健康状态分析
                </h2>

                <!-- 设备预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">设备故障预警</div>
                        <div class="alert-message">3号水泵振动值达到4.8mm/s，超出正常范围且呈上升趋势，预测剩余安全运行时间72小时，已推送维修工单</div>
                    </div>
                </div>

                <!-- 设备健康指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card equipment-health">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-heartbeat"></i>
                                设备健康指数
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -2.5
                            </div>
                        </div>
                        <div class="kpi-value">
                            87.5
                            <span class="kpi-unit">分</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>优秀：≥90</span>
                            <span>昨日：90.0</span>
                        </div>
                    </div>

                    <div class="kpi-card equipment-health">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-circle"></i>
                                故障风险设备
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1台
                            </div>
                        </div>
                        <div class="kpi-value">
                            3
                            <span class="kpi-unit">台</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>高风险：1台</span>
                            <span>中风险：2台</span>
                        </div>
                    </div>

                    <div class="kpi-card equipment-health">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tools"></i>
                                维修工单
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                +2单
                            </div>
                        </div>
                        <div class="kpi-value">
                            8
                            <span class="kpi-unit">单</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>待处理：3单</span>
                            <span>处理中：5单</span>
                        </div>
                    </div>

                    <div class="kpi-card equipment-health">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-clock"></i>
                                平均修复时间
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -0.5h
                            </div>
                        </div>
                        <div class="kpi-value">
                            4.2
                            <span class="kpi-unit">小时</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：≤6小时</span>
                            <span>最快：2.1小时</span>
                        </div>
                    </div>
                </div>

                <!-- 设备监控面板 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-desktop"></i>
                            关键设备实时监控
                        </h3>
                        <button class="btn btn-primary" onclick="refreshEquipmentStatus()">
                            <i class="fas fa-sync-alt"></i>
                            刷新状态
                        </button>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; padding: 20px;">
                        <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; border-left: 5px solid #f44336;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="color: #1e3c72; margin: 0;">3号水泵</h4>
                                <span style="background: #ffebee; color: #c62828; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">高风险</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>振动值:</span>
                                    <span style="color: #f44336; font-weight: 600;">4.8 mm/s</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>温度:</span>
                                    <span style="color: #ff9800;">78°C</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>运行时间:</span>
                                    <span>8,450小时</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>健康指数:</span>
                                    <span style="color: #f44336; font-weight: 600;">65分</span>
                                </div>
                            </div>
                            <div style="font-size: 0.9rem; color: #666;">
                                预测剩余安全运行时间：72小时
                            </div>
                        </div>

                        <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; border-left: 5px solid #ff9800;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="color: #1e3c72; margin: 0;">1号加药泵</h4>
                                <span style="background: #fff3e0; color: #ef6c00; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">中风险</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>流量:</span>
                                    <span style="color: #ff9800;">85 L/h</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>压力:</span>
                                    <span style="color: #4caf50;">0.6 MPa</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>运行时间:</span>
                                    <span>6,280小时</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>健康指数:</span>
                                    <span style="color: #ff9800; font-weight: 600;">78分</span>
                                </div>
                            </div>
                            <div style="font-size: 0.9rem; color: #666;">
                                建议下次保养时间：15天后
                            </div>
                        </div>

                        <div style="background: #f8f9fa; border-radius: 10px; padding: 20px; border-left: 5px solid #4caf50;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                <h4 style="color: #1e3c72; margin: 0;">过滤设备A</h4>
                                <span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; font-weight: 600;">正常</span>
                            </div>
                            <div style="margin-bottom: 10px;">
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>滤速:</span>
                                    <span style="color: #4caf50;">8.2 m/h</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>反洗周期:</span>
                                    <span style="color: #4caf50;">24小时</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                                    <span>运行时间:</span>
                                    <span>3,150小时</span>
                                </div>
                                <div style="display: flex; justify-content: space-between;">
                                    <span>健康指数:</span>
                                    <span style="color: #4caf50; font-weight: 600;">92分</span>
                                </div>
                            </div>
                            <div style="font-size: 0.9rem; color: #666;">
                                设备状态良好，继续监控
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                设备健康趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchHealthPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchHealthPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchHealthPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="equipmentHealthChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                故障类型分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="faultTypeChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 维修工单管理 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-clipboard-list"></i>
                            维修工单管理
                        </h3>
                        <button class="btn btn-success" onclick="createWorkOrder()">
                            <i class="fas fa-plus"></i>
                            创建工单
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>工单编号</th>
                                <th>设备名称</th>
                                <th>故障类型</th>
                                <th>紧急程度</th>
                                <th>创建时间</th>
                                <th>负责人员</th>
                                <th>预计完成</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>WO-2024-001</td>
                                <td>3号水泵</td>
                                <td>振动异常</td>
                                <td class="status-danger">紧急</td>
                                <td>14:25</td>
                                <td>张工</td>
                                <td>16:00</td>
                                <td class="status-warning">处理中</td>
                                <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8rem;">查看</button></td>
                            </tr>
                            <tr>
                                <td>WO-2024-002</td>
                                <td>1号加药泵</td>
                                <td>流量偏低</td>
                                <td class="status-warning">中等</td>
                                <td>13:45</td>
                                <td>李工</td>
                                <td>17:00</td>
                                <td class="status-warning">处理中</td>
                                <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8rem;">查看</button></td>
                            </tr>
                            <tr>
                                <td>WO-2024-003</td>
                                <td>过滤设备B</td>
                                <td>定期保养</td>
                                <td class="status-normal">低</td>
                                <td>12:30</td>
                                <td>王工</td>
                                <td>明日</td>
                                <td class="status-normal">待处理</td>
                                <td><button class="btn btn-primary" style="padding: 4px 8px; font-size: 0.8rem;">查看</button></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 管网漏损分析与定位 -->
            <div id="pipeline-loss" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-map-marked-alt"></i>
                    管网漏损分析与定位
                </h2>

                <!-- 漏损预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">管网漏损预警</div>
                        <div class="alert-message">DMA-05区域夜间最小流量异常偏高，漏损率从3.2%上升至8.5%，疑似春华路段存在暗漏，已标注重点排查区域</div>
                    </div>
                </div>

                <!-- 漏损指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card pipeline-loss">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-percentage"></i>
                                总体漏损率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            6.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标值：≤5%</span>
                            <span>昨日：5.6%</span>
                        </div>
                    </div>

                    <div class="kpi-card pipeline-loss">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-circle"></i>
                                异常区域数
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2区域
                            </div>
                        </div>
                        <div class="kpi-value">
                            5
                            <span class="kpi-unit">个</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>高风险：2个</span>
                            <span>中风险：3个</span>
                        </div>
                    </div>

                    <div class="kpi-card pipeline-loss">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                日漏失水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +580m³
                            </div>
                        </div>
                        <div class="kpi-value">
                            2,850
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>经济损失：4,275元</span>
                            <span>月累计：78,500m³</span>
                        </div>
                    </div>

                    <div class="kpi-card pipeline-loss">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-search"></i>
                                定位精度
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            85.2
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：≥80%</span>
                            <span>平均定位时间：2.5h</span>
                        </div>
                    </div>
                </div>

                <!-- 地图展示区域 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-map"></i>
                            管网漏损地图分析
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchMapView('leakage')">漏损分布</button>
                            <button class="chart-btn" onclick="switchMapView('pressure')">压力分布</button>
                            <button class="chart-btn" onclick="switchMapView('flow')">流量分布</button>
                            <button class="chart-btn" onclick="switchMapView('age')">管龄分布</button>
                        </div>
                    </div>
                    <div class="map-container" id="pipelineMap">
                        <div style="height: 100%; display: flex; align-items: center; justify-content: center; background: #f8f9fa; color: #666; font-size: 1.2rem;">
                            <div style="text-align: center;">
                                <i class="fas fa-map" style="font-size: 4rem; margin-bottom: 20px; color: #1e3c72;"></i>
                                <div>管网GIS地图加载中...</div>
                                <div style="font-size: 1rem; margin-top: 10px;">显示DMA分区、漏损点位、压力监测点</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                DMA分区漏损趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchLossPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchLossPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchLossPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="dmaLossChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                管材漏损统计
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="materialLossChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- DMA分区详细分析 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-table"></i>
                            DMA分区漏损详细分析
                        </h3>
                        <button class="btn btn-danger" onclick="generateLeakageReport()">
                            <i class="fas fa-exclamation-triangle"></i>
                            生成漏损报告
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>DMA分区</th>
                                <th>供水量 (m³/日)</th>
                                <th>夜间最小流量</th>
                                <th>漏损率</th>
                                <th>压力状况</th>
                                <th>主要管材</th>
                                <th>管龄</th>
                                <th>风险等级</th>
                                <th>建议措施</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>DMA-05</td>
                                <td class="number">3,200</td>
                                <td class="number status-danger">85 m³/h</td>
                                <td class="number status-danger">8.5%</td>
                                <td class="status-warning">偏低</td>
                                <td>铸铁管</td>
                                <td class="number">35年</td>
                                <td class="status-danger">高风险</td>
                                <td>重点排查春华路段</td>
                            </tr>
                            <tr>
                                <td>DMA-03</td>
                                <td class="number">2,850</td>
                                <td class="number status-warning">45 m³/h</td>
                                <td class="number status-warning">6.2%</td>
                                <td class="status-normal">正常</td>
                                <td>球墨铸铁</td>
                                <td class="number">28年</td>
                                <td class="status-warning">中风险</td>
                                <td>加强巡检频次</td>
                            </tr>
                            <tr>
                                <td>DMA-01</td>
                                <td class="number">4,100</td>
                                <td class="number status-normal">32 m³/h</td>
                                <td class="number status-normal">3.8%</td>
                                <td class="status-normal">正常</td>
                                <td>PE管</td>
                                <td class="number">15年</td>
                                <td class="status-normal">低风险</td>
                                <td>保持现状</td>
                            </tr>
                            <tr>
                                <td>DMA-07</td>
                                <td class="number">2,650</td>
                                <td class="number status-warning">38 m³/h</td>
                                <td class="number status-warning">5.8%</td>
                                <td class="status-normal">正常</td>
                                <td>钢管</td>
                                <td class="number">42年</td>
                                <td class="status-warning">中风险</td>
                                <td>计划更新改造</td>
                            </tr>
                            <tr>
                                <td>DMA-02</td>
                                <td class="number">3,750</td>
                                <td class="number status-normal">28 m³/h</td>
                                <td class="number status-normal">2.9%</td>
                                <td class="status-normal">正常</td>
                                <td>PE管</td>
                                <td class="number">8年</td>
                                <td class="status-normal">低风险</td>
                                <td>优秀示范区域</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 应急事件处置分析 -->
            <div id="emergency" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-exclamation-triangle"></i>
                    应急事件处置分析
                </h2>

                <!-- 应急事件预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">应急事件进行中</div>
                        <div class="alert-message">建设路DN400主管爆管事件，影响用户约2,500户，预计停水时长4-6小时，抢修队伍已到位，备件充足</div>
                    </div>
                </div>

                <!-- 应急指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card emergency">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-circle"></i>
                                当前事件数
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1事件
                            </div>
                        </div>
                        <div class="kpi-value">
                            3
                            <span class="kpi-unit">起</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>紧急：1起</span>
                            <span>一般：2起</span>
                        </div>
                    </div>

                    <div class="kpi-card emergency">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-users"></i>
                                影响用户数
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2,500户
                            </div>
                        </div>
                        <div class="kpi-value">
                            3,200
                            <span class="kpi-unit">户</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>停水：2,500户</span>
                            <span>降压：700户</span>
                        </div>
                    </div>

                    <div class="kpi-card emergency">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -5分钟
                            </div>
                        </div>
                        <div class="kpi-value">
                            18
                            <span class="kpi-unit">分钟</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：≤30分钟</span>
                            <span>最快：12分钟</span>
                        </div>
                    </div>

                    <div class="kpi-card emergency">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tools"></i>
                                抢修队伍
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                就位
                            </div>
                        </div>
                        <div class="kpi-value">
                            8
                            <span class="kpi-unit">队</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>出动：3队</span>
                            <span>待命：5队</span>
                        </div>
                    </div>
                </div>

                <!-- 应急处置流程 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-sitemap"></i>
                            应急处置流程监控
                        </h3>
                        <button class="btn btn-danger" onclick="activateEmergencyPlan()">
                            <i class="fas fa-play"></i>
                            启动应急预案
                        </button>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                            <div style="text-align: center; position: relative;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #4caf50, #81c784); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 2rem;">
                                    <i class="fas fa-bell"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72; margin-bottom: 5px;">事件上报</div>
                                <div style="font-size: 0.9rem; color: #666;">14:15 完成</div>
                                <div style="font-size: 0.8rem; color: #4caf50; margin-top: 5px;">✓ 已完成</div>
                            </div>

                            <div style="text-align: center; position: relative;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #4caf50, #81c784); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 2rem;">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72; margin-bottom: 5px;">现场勘查</div>
                                <div style="font-size: 0.9rem; color: #666;">14:25 完成</div>
                                <div style="font-size: 0.8rem; color: #4caf50; margin-top: 5px;">✓ 已完成</div>
                            </div>

                            <div style="text-align: center; position: relative;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #2196f3, #64b5f6); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 2rem;">
                                    <i class="fas fa-cogs"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72; margin-bottom: 5px;">关阀停水</div>
                                <div style="font-size: 0.9rem; color: #666;">14:35 进行中</div>
                                <div style="font-size: 0.8rem; color: #2196f3; margin-top: 5px;">⏳ 进行中</div>
                            </div>

                            <div style="text-align: center; position: relative;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #9e9e9e, #bdbdbd); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 2rem;">
                                    <i class="fas fa-tools"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72; margin-bottom: 5px;">抢修作业</div>
                                <div style="font-size: 0.9rem; color: #666;">预计15:00开始</div>
                                <div style="font-size: 0.8rem; color: #9e9e9e; margin-top: 5px;">⏸ 待执行</div>
                            </div>

                            <div style="text-align: center; position: relative;">
                                <div style="width: 100px; height: 100px; background: linear-gradient(135deg, #9e9e9e, #bdbdbd); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px; color: white; font-size: 2rem;">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div style="font-weight: 600; color: #1e3c72; margin-bottom: 5px;">恢复供水</div>
                                <div style="font-size: 0.9rem; color: #666;">预计18:00完成</div>
                                <div style="font-size: 0.8rem; color: #9e9e9e; margin-top: 5px;">⏸ 待执行</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 资源调配 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-truck"></i>
                                抢修资源调配
                            </h3>
                            <button class="btn btn-primary" onclick="optimizeResourceAllocation()">
                                <i class="fas fa-route"></i>
                                优化调配
                            </button>
                        </div>
                        <div style="padding: 20px;">
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                                <div style="background: #e3f2fd; padding: 15px; border-radius: 10px;">
                                    <h4 style="color: #1e3c72; margin-bottom: 10px;">
                                        <i class="fas fa-users"></i> 抢修队伍
                                    </h4>
                                    <div style="margin-bottom: 8px;">A队：建设路现场 (3人)</div>
                                    <div style="margin-bottom: 8px;">B队：待命中 (4人)</div>
                                    <div style="margin-bottom: 8px;">C队：前往现场 (3人)</div>
                                    <div style="color: #4caf50; font-weight: 600;">总计：10人可调配</div>
                                </div>

                                <div style="background: #e8f5e8; padding: 15px; border-radius: 10px;">
                                    <h4 style="color: #1e3c72; margin-bottom: 10px;">
                                        <i class="fas fa-cogs"></i> 备件库存
                                    </h4>
                                    <div style="margin-bottom: 8px;">DN400管道：5根</div>
                                    <div style="margin-bottom: 8px;">法兰接头：12套</div>
                                    <div style="margin-bottom: 8px;">阀门配件：8个</div>
                                    <div style="color: #4caf50; font-weight: 600;">库存充足</div>
                                </div>

                                <div style="background: #fff3e0; padding: 15px; border-radius: 10px;">
                                    <h4 style="color: #1e3c72; margin-bottom: 10px;">
                                        <i class="fas fa-truck"></i> 设备车辆
                                    </h4>
                                    <div style="margin-bottom: 8px;">抢修车：3辆在用</div>
                                    <div style="margin-bottom: 8px;">挖掘机：1台到位</div>
                                    <div style="margin-bottom: 8px;">应急供水车：2辆待命</div>
                                    <div style="color: #ff9800; font-weight: 600;">设备就位</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                历史事件统计
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="emergencyStatsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 事件处置记录 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-clipboard-list"></i>
                            应急事件处置记录
                        </h3>
                        <button class="btn btn-success" onclick="exportEmergencyReport()">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>事件编号</th>
                                <th>事件类型</th>
                                <th>发生位置</th>
                                <th>影响用户</th>
                                <th>发生时间</th>
                                <th>响应时间</th>
                                <th>处置时长</th>
                                <th>状态</th>
                                <th>负责人</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>EM-2024-001</td>
                                <td class="status-danger">管网爆管</td>
                                <td>建设路DN400</td>
                                <td class="number">2,500户</td>
                                <td>14:15</td>
                                <td class="number">18分钟</td>
                                <td>进行中</td>
                                <td class="status-warning">处置中</td>
                                <td>张队长</td>
                            </tr>
                            <tr>
                                <td>EM-2024-002</td>
                                <td class="status-warning">水质异常</td>
                                <td>东区水厂</td>
                                <td class="number">1,200户</td>
                                <td>13:45</td>
                                <td class="number">15分钟</td>
                                <td>2小时</td>
                                <td class="status-normal">已完成</td>
                                <td>李工程师</td>
                            </tr>
                            <tr>
                                <td>EM-2024-003</td>
                                <td class="status-warning">设备故障</td>
                                <td>3号水泵</td>
                                <td class="number">800户</td>
                                <td>12:30</td>
                                <td class="number">22分钟</td>
                                <td>1.5小时</td>
                                <td class="status-normal">已完成</td>
                                <td>王技师</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 管网GIS系统 -->
            <div id="gis-system" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-map"></i>
                    管网GIS系统
                </h2>

                <!-- GIS功能面板 -->
                <div class="chart-card" style="margin-bottom: 25px;">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-tools"></i>
                            GIS功能工具栏
                        </h3>
                        <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                            <button class="btn btn-primary" onclick="gisFunction('search')">
                                <i class="fas fa-search"></i>
                                搜索定位
                            </button>
                            <button class="btn btn-primary" onclick="gisFunction('measure')">
                                <i class="fas fa-ruler"></i>
                                测量工具
                            </button>
                            <button class="btn btn-primary" onclick="gisFunction('analysis')">
                                <i class="fas fa-chart-line"></i>
                                分析工具
                            </button>
                            <button class="btn btn-success" onclick="gisFunction('print')">
                                <i class="fas fa-print"></i>
                                打印地图
                            </button>
                        </div>
                    </div>
                    <div style="display: flex; gap: 20px; padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <div style="flex: 1;">
                            <h4 style="color: #1e3c72; margin-bottom: 15px;">图层控制</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px;">
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked onchange="toggleLayer('pipeline')">
                                    <span>管道网络</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked onchange="toggleLayer('valve')">
                                    <span>阀门设施</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" checked onchange="toggleLayer('pump')">
                                    <span>泵站设备</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" onchange="toggleLayer('pressure')">
                                    <span>压力监测</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" onchange="toggleLayer('flow')">
                                    <span>流量监测</span>
                                </label>
                                <label style="display: flex; align-items: center; gap: 8px;">
                                    <input type="checkbox" onchange="toggleLayer('dma')">
                                    <span>DMA分区</span>
                                </label>
                            </div>
                        </div>
                        <div style="flex: 1;">
                            <h4 style="color: #1e3c72; margin-bottom: 15px;">专题图</h4>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                                <button class="chart-btn" onclick="showThematicMap('diameter')">管径分布</button>
                                <button class="chart-btn" onclick="showThematicMap('age')">管龄分析</button>
                                <button class="chart-btn" onclick="showThematicMap('material')">管材分布</button>
                                <button class="chart-btn" onclick="showThematicMap('pressure')">压力分析</button>
                                <button class="chart-btn" onclick="showThematicMap('flow')">流量分析</button>
                                <button class="chart-btn" onclick="showThematicMap('leakage')">漏损分析</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 主地图区域 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-globe"></i>
                            管网地理信息系统
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchMapMode('normal')">标准地图</button>
                            <button class="chart-btn" onclick="switchMapMode('satellite')">卫星地图</button>
                            <button class="chart-btn" onclick="switchMapMode('terrain')">地形地图</button>
                        </div>
                    </div>
                    <div class="map-container" id="mainGISMap">
                        <div style="height: 100%; display: flex; align-items: center; justify-content: center; background: linear-gradient(135deg, #e3f2fd, #bbdefb); color: #1e3c72; font-size: 1.2rem;">
                            <div style="text-align: center;">
                                <i class="fas fa-map" style="font-size: 5rem; margin-bottom: 25px;"></i>
                                <div style="font-size: 1.5rem; font-weight: 600; margin-bottom: 15px;">管网GIS地图系统</div>
                                <div style="font-size: 1rem; margin-bottom: 10px;">集成管网数据、设施信息、监测数据</div>
                                <div style="font-size: 0.9rem; color: #666;">支持搜索、查询、测量、分析、打印等功能</div>
                                <div style="margin-top: 20px;">
                                    <button class="btn btn-primary" onclick="initGISMap()">
                                        <i class="fas fa-play"></i>
                                        启动地图系统
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 数据管理面板 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-database"></i>
                                数据管理
                            </h3>
                            <button class="btn btn-success" onclick="importGISData()">
                                <i class="fas fa-upload"></i>
                                导入数据
                            </button>
                        </div>
                        <div style="padding: 20px;">
                            <div style="margin-bottom: 20px;">
                                <h4 style="color: #1e3c72; margin-bottom: 10px;">数据统计</h4>
                                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 15px;">
                                    <div style="text-align: center; padding: 10px; background: #e3f2fd; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #1e3c72;">1,258</div>
                                        <div style="font-size: 0.9rem; color: #666;">管道段</div>
                                    </div>
                                    <div style="text-align: center; padding: 10px; background: #e8f5e8; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #1e3c72;">456</div>
                                        <div style="font-size: 0.9rem; color: #666;">阀门</div>
                                    </div>
                                    <div style="text-align: center; padding: 10px; background: #fff3e0; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #1e3c72;">89</div>
                                        <div style="font-size: 0.9rem; color: #666;">泵站</div>
                                    </div>
                                    <div style="text-align: center; padding: 10px; background: #fce4ec; border-radius: 8px;">
                                        <div style="font-size: 1.5rem; font-weight: bold; color: #1e3c72;">234</div>
                                        <div style="font-size: 0.9rem; color: #666;">监测点</div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <h4 style="color: #1e3c72; margin-bottom: 10px;">数据质量</h4>
                                <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span>数据完整性：</span>
                                        <span style="color: #4caf50; font-weight: 600;">98.5%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span>数据准确性：</span>
                                        <span style="color: #4caf50; font-weight: 600;">96.8%</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                                        <span>最后更新：</span>
                                        <span>2024-01-15 14:30</span>
                                    </div>
                                    <div style="display: flex; justify-content: space-between;">
                                        <span>数据版本：</span>
                                        <span>v2.1.3</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                管网资产统计
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="assetStatsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 分析工具结果 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-search"></i>
                            分析工具结果
                        </h3>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary" onclick="runConnectivityAnalysis()">
                                <i class="fas fa-project-diagram"></i>
                                连通性分析
                            </button>
                            <button class="btn btn-primary" onclick="runCrossSectionAnalysis()">
                                <i class="fas fa-cut"></i>
                                横断面分析
                            </button>
                            <button class="btn btn-warning" onclick="runBurstAnalysis()">
                                <i class="fas fa-exclamation-triangle"></i>
                                爆管分析
                            </button>
                        </div>
                    </div>
                    <div style="padding: 20px; background: #f8f9fa; border-radius: 10px;">
                        <div id="analysisResults" style="min-height: 200px; display: flex; align-items: center; justify-content: center; color: #666;">
                            <div style="text-align: center;">
                                <i class="fas fa-chart-line" style="font-size: 3rem; margin-bottom: 15px; color: #1e3c72;"></i>
                                <div>选择分析工具查看结果</div>
                                <div style="font-size: 0.9rem; margin-top: 5px;">支持连通性分析、横断面分析、纵剖面分析、爆管影响分析</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'supply-demand';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('智慧水务驾驶舱初始化...');
            
            // 初始化标签页事件
            initTabEvents();
            
            // 初始化图表
            initCharts();
            
            // 启动实时更新
            startRealTimeUpdate();
            
            console.log('系统初始化完成');
        });

        // 初始化标签页事件
        function initTabEvents() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活选中标签
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
            
            // 加载对应内容
            setTimeout(() => {
                loadTabContent(tabId);
            }, 100);
        }

        // 加载标签页内容
        function loadTabContent(tabId) {
            switch(tabId) {
                case 'supply-demand':
                    // 供需平衡分析已经在HTML中
                    break;
                case 'water-quality':
                    loadWaterQualityContent();
                    break;
                case 'energy-consumption':
                    loadEnergyConsumptionContent();
                    break;
                case 'equipment-health':
                    loadEquipmentHealthContent();
                    break;
                case 'pipeline-loss':
                    loadPipelineLossContent();
                    break;
                case 'emergency':
                    loadEmergencyContent();
                    break;
                case 'gis-system':
                    loadGISContent();
                    break;
            }
        }

        // 初始化图表
        function initCharts() {
            initSupplyDemandChart();
            initWaterUsageChart();
        }

        // 供需平衡趋势图
        function initSupplyDemandChart() {
            const ctx = document.getElementById('supplyDemandChart');
            if (!ctx) return;
            
            charts.supplyDemand = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '供水量',
                        data: [1200, 1100, 1650, 1850, 1850, 1750, 1400],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '用水量',
                        data: [800, 600, 1580, 2050, 1980, 1650, 1200],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '预测用水量',
                        data: [850, 650, 1620, 2120, 2050, 1720, 1250],
                        borderColor: '#ff9800',
                        backgroundColor: 'transparent',
                        borderDash: [5, 5],
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '水量 (m³/h)'
                            }
                        }
                    }
                }
            });
        }

        // 用水结构饼图
        function initWaterUsageChart() {
            const ctx = document.getElementById('waterUsageChart');
            if (!ctx) return;
            
            charts.waterUsage = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['居民用水', '工业用水', '商业用水', '公共用水', '其他用水'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            '#1e3c72',
                            '#2a5298',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 切换时间周期
        function switchPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            console.log('切换到时间周期:', period);
            showNotification(`已切换到${period}视图`, 'info');
        }

        // 导出数据
        function exportData() {
            showNotification('正在导出数据...', 'info');
            
            setTimeout(() => {
                showNotification('数据导出成功！', 'success');
            }, 2000);
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            setInterval(() => {
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.getElementById('updateTime').textContent = `更新时间：${timeStr}`;
                
                // 模拟数据更新
                updateRandomData();
            }, 30000);
        }

        // 更新随机数据
        function updateRandomData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const text = value.textContent;
                const number = parseFloat(text.replace(/[^\d.-]/g, ''));
                
                if (isNaN(number)) return;
                
                const variation = (Math.random() - 0.5) * 0.02;
                const newNumber = number * (1 + variation);
                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';
                
                if (Math.abs(number) < 100) {
                    value.innerHTML = newNumber.toFixed(1) + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newNumber).toLocaleString() + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 300px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 加载水质追溯分析内容
        function loadWaterQualityContent() {
            console.log('加载水质追溯分析内容');
            initWaterQualityCharts();
        }

        // 加载能耗药耗优化内容
        function loadEnergyConsumptionContent() {
            console.log('加载能耗药耗优化内容');
            initConsumptionCharts();
        }

        // 加载设备健康分析内容
        function loadEquipmentHealthContent() {
            console.log('加载设备健康分析内容');
            initEquipmentCharts();
        }

        // 加载管网漏损分析内容
        function loadPipelineLossContent() {
            console.log('加载管网漏损分析内容');
            initPipelineCharts();
            initPipelineMap();
        }

        // 加载应急事件处置内容
        function loadEmergencyContent() {
            console.log('加载应急事件处置内容');
            initEmergencyCharts();
        }

        // 加载管网GIS系统内容
        function loadGISContent() {
            console.log('加载管网GIS系统内容');
            initGISCharts();
        }

        // 初始化水质图表
        function initWaterQualityCharts() {
            initWaterQualityTrendChart();
            initTreatmentEffectChart();
        }

        // 水质趋势图
        function initWaterQualityTrendChart() {
            const ctx = document.getElementById('waterQualityTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.waterQualityTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['10:00', '11:00', '12:00', '13:00', '14:00', '15:00'],
                    datasets: [{
                        label: '浊度 (NTU)',
                        data: [0.65, 0.68, 0.72, 0.78, 0.82, 0.85],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '余氯 (mg/L)',
                        data: [0.52, 0.50, 0.48, 0.46, 0.45, 0.45],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: 'pH值',
                        data: [7.0, 7.1, 7.2, 7.2, 7.2, 7.2],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '浊度 (NTU)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '余氯 (mg/L)' },
                            grid: { drawOnChartArea: false }
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            position: 'right'
                        }
                    }
                }
            });
        }

        // 处理效果图
        function initTreatmentEffectChart() {
            const ctx = document.getElementById('treatmentEffectChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.treatmentEffect = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['原水', '混凝沉淀', '过滤', '消毒', '出厂'],
                    datasets: [{
                        label: '浊度去除率 (%)',
                        data: [0, 85, 95, 95, 95],
                        backgroundColor: '#2196f3',
                        borderColor: '#1976d2',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: { display: true, text: '去除率 (%)' }
                        }
                    }
                }
            });
        }

        // 初始化能耗图表
        function initConsumptionCharts() {
            initConsumptionTrendChart();
            initCostStructureChart();
        }

        // 能耗趋势图
        function initConsumptionTrendChart() {
            const ctx = document.getElementById('consumptionTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.consumptionTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00'],
                    datasets: [{
                        label: '电耗 (kWh/吨)',
                        data: [0.76, 0.79, 0.82, 0.95, 0.88, 0.85],
                        borderColor: '#ff9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '药耗 (元/吨)',
                        data: [11.2, 11.8, 12.1, 14.2, 13.8, 12.5],
                        borderColor: '#9c27b0',
                        backgroundColor: 'rgba(156, 39, 176, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '电耗 (kWh/吨)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '药耗 (元/吨)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // 成本构成图
        function initCostStructureChart() {
            const ctx = document.getElementById('costStructureChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.costStructure = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['电费', '药剂费', '人工费', '维护费', '其他'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            '#ff9800',
                            '#9c27b0',
                            '#4caf50',
                            '#2196f3',
                            '#f44336'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 初始化设备图表
        function initEquipmentCharts() {
            initEquipmentHealthChart();
            initFaultTypeChart();
        }

        // 设备健康趋势图
        function initEquipmentHealthChart() {
            const ctx = document.getElementById('equipmentHealthChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.equipmentHealth = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
                    datasets: [{
                        label: '3号水泵健康指数',
                        data: [85, 82, 78, 72, 68, 65, 65],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4
                    }, {
                        label: '1号加药泵健康指数',
                        data: [88, 86, 84, 82, 80, 78, 78],
                        borderColor: '#ff9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4
                    }, {
                        label: '过滤设备A健康指数',
                        data: [95, 94, 93, 92, 92, 92, 92],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: { display: true, text: '健康指数' }
                        }
                    }
                }
            });
        }

        // 故障类型图
        function initFaultTypeChart() {
            const ctx = document.getElementById('faultTypeChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.faultType = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['振动异常', '温度过高', '流量异常', '压力异常', '电气故障', '其他'],
                    datasets: [{
                        label: '故障次数',
                        data: [8, 5, 6, 4, 3, 2],
                        backgroundColor: [
                            '#f44336',
                            '#ff9800',
                            '#2196f3',
                            '#9c27b0',
                            '#4caf50',
                            '#607d8b'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '故障次数' }
                        }
                    }
                }
            });
        }

        // 初始化管网图表
        function initPipelineCharts() {
            initDMALossChart();
            initMaterialLossChart();
        }

        // DMA漏损图
        function initDMALossChart() {
            const ctx = document.getElementById('dmaLossChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.dmaLoss = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月9日', '1月10日', '1月11日', '1月12日', '1月13日', '1月14日', '1月15日'],
                    datasets: [{
                        label: 'DMA-05漏损率',
                        data: [3.2, 3.5, 4.1, 5.2, 6.8, 7.5, 8.5],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'DMA-03漏损率',
                        data: [5.8, 5.9, 6.0, 6.1, 6.2, 6.2, 6.2],
                        borderColor: '#ff9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'DMA-01漏损率',
                        data: [3.5, 3.6, 3.7, 3.8, 3.8, 3.8, 3.8],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '漏损率 (%)' }
                        }
                    }
                }
            });
        }

        // 管材漏损图
        function initMaterialLossChart() {
            const ctx = document.getElementById('materialLossChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.materialLoss = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['铸铁管', '球墨铸铁', '钢管', 'PE管', 'PVC管'],
                    datasets: [{
                        label: '漏损次数',
                        data: [15, 8, 12, 3, 2],
                        backgroundColor: [
                            '#f44336',
                            '#ff9800',
                            '#9c27b0',
                            '#4caf50',
                            '#2196f3'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '漏损次数' }
                        }
                    }
                }
            });
        }

        // 初始化应急图表
        function initEmergencyCharts() {
            initEmergencyStatsChart();
        }

        // 应急统计图
        function initEmergencyStatsChart() {
            const ctx = document.getElementById('emergencyStatsChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.emergencyStats = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
                    datasets: [{
                        label: '管网爆管',
                        data: [3, 2, 4, 1, 2, 1],
                        backgroundColor: '#f44336'
                    }, {
                        label: '水质异常',
                        data: [1, 2, 1, 3, 1, 2],
                        backgroundColor: '#ff9800'
                    }, {
                        label: '设备故障',
                        data: [2, 3, 2, 2, 3, 1],
                        backgroundColor: '#2196f3'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '事件数量' }
                        }
                    }
                }
            });
        }

        // 初始化GIS图表
        function initGISCharts() {
            initAssetStatsChart();
        }

        // 资产统计图
        function initAssetStatsChart() {
            const ctx = document.getElementById('assetStatsChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.assetStats = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['管道', '阀门', '泵站', '监测点', '其他设施'],
                    datasets: [{
                        data: [1258, 456, 89, 234, 156],
                        backgroundColor: [
                            '#1e3c72',
                            '#2a5298',
                            '#4facfe',
                            '#00f2fe',
                            '#43e97b'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 水质分析功能
        function startTraceAnalysis() {
            showNotification('正在启动水质追溯分析...', 'info');
            setTimeout(() => {
                showNotification('追溯分析完成！已定位问题环节', 'success');
            }, 2000);
        }

        function switchQualityPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到${period}水质分析视图`, 'info');
        }

        function generateQualityReport() {
            showNotification('正在生成水质分析报告...', 'info');
            setTimeout(() => {
                showNotification('水质分析报告生成成功！', 'success');
            }, 2000);
        }

        // 能耗优化功能
        function switchConsumptionPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到${period}能耗分析视图`, 'info');
        }

        function optimizeConsumption() {
            showNotification('正在执行智能优化...', 'info');
            setTimeout(() => {
                showNotification('优化完成！预计节能8.5%', 'success');
            }, 3000);
        }

        // 设备健康功能
        function refreshEquipmentStatus() {
            showNotification('正在刷新设备状态...', 'info');
            setTimeout(() => {
                showNotification('设备状态已更新！', 'success');
            }, 1500);
        }

        function switchHealthPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到${period}健康分析视图`, 'info');
        }

        function createWorkOrder() {
            showNotification('正在创建维修工单...', 'info');
            setTimeout(() => {
                showNotification('维修工单创建成功！', 'success');
            }, 1500);
        }

        // 管网漏损功能
        function switchMapView(view) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到${view}视图`, 'info');
        }

        function switchLossPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            showNotification(`已切换到${period}漏损分析视图`, 'info');
        }

        function generateLeakageReport() {
            showNotification('正在生成漏损分析报告...', 'info');
            setTimeout(() => {
                showNotification('漏损分析报告生成成功！', 'success');
            }, 2000);
        }

        function initPipelineMap() {
            console.log('初始化管网地图');
        }

        // 应急事件功能
        function activateEmergencyPlan() {
            showNotification('正在启动应急预案...', 'warning');
            setTimeout(() => {
                showNotification('应急预案已启动！', 'success');
            }, 2000);
        }

        function optimizeResourceAllocation() {
            showNotification('正在优化资源调配...', 'info');
            setTimeout(() => {
                showNotification('资源调配优化完成！', 'success');
            }, 2500);
        }

        function exportEmergencyReport() {
            showNotification('正在导出应急报告...', 'info');
            setTimeout(() => {
                showNotification('应急报告导出成功！', 'success');
            }, 2000);
        }

        // GIS系统功能
        function gisFunction(func) {
            const funcNames = {
                'search': '搜索定位',
                'measure': '测量工具',
                'analysis': '分析工具',
                'print': '打印地图'
            };
            showNotification(`正在启动${funcNames[func]}...`, 'info');
        }

        function toggleLayer(layer) {
            const layerNames = {
                'pipeline': '管道网络',
                'valve': '阀门设施',
                'pump': '泵站设备',
                'pressure': '压力监测',
                'flow': '流量监测',
                'dma': 'DMA分区'
            };
            const isChecked = event.target.checked;
            showNotification(`${layerNames[layer]}图层已${isChecked ? '显示' : '隐藏'}`, 'info');
        }

        function showThematicMap(theme) {
            const themeNames = {
                'diameter': '管径分布',
                'age': '管龄分析',
                'material': '管材分布',
                'pressure': '压力分析',
                'flow': '流量分析',
                'leakage': '漏损分析'
            };
            showNotification(`正在加载${themeNames[theme]}专题图...`, 'info');
        }

        function switchMapMode(mode) {
            document.querySelectorAll('.chart-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
            const modeNames = {
                'normal': '标准地图',
                'satellite': '卫星地图',
                'terrain': '地形地图'
            };
            showNotification(`已切换到${modeNames[mode]}模式`, 'info');
        }

        function importGISData() {
            showNotification('正在导入GIS数据...', 'info');
            setTimeout(() => {
                showNotification('GIS数据导入成功！', 'success');
            }, 3000);
        }

        function initGISMap() {
            showNotification('正在启动GIS地图系统...', 'info');
            setTimeout(() => {
                showNotification('GIS地图系统启动成功！', 'success');
            }, 2000);
        }

        function runConnectivityAnalysis() {
            showNotification('正在执行连通性分析...', 'info');
            setTimeout(() => {
                document.getElementById('analysisResults').innerHTML = `
                    <div style="text-align: left;">
                        <h4 style="color: #1e3c72; margin-bottom: 15px;">连通性分析结果</h4>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <strong>分析区域：</strong>建设路DN400主管段<br>
                            <strong>连通节点：</strong>156个<br>
                            <strong>关键路径：</strong>3条<br>
                            <strong>冗余度：</strong>85%<br>
                            <strong>建议：</strong>增加1条备用管道提高冗余度
                        </div>
                    </div>
                `;
                showNotification('连通性分析完成！', 'success');
            }, 2000);
        }

        function runCrossSectionAnalysis() {
            showNotification('正在执行横断面分析...', 'info');
            setTimeout(() => {
                document.getElementById('analysisResults').innerHTML = `
                    <div style="text-align: left;">
                        <h4 style="color: #1e3c72; margin-bottom: 15px;">横断面分析结果</h4>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <strong>分析位置：</strong>春华路与建设路交叉口<br>
                            <strong>管道数量：</strong>5条<br>
                            <strong>最大管径：</strong>DN600<br>
                            <strong>埋深范围：</strong>1.2-2.8米<br>
                            <strong>冲突检测：</strong>无冲突
                        </div>
                    </div>
                `;
                showNotification('横断面分析完成！', 'success');
            }, 2000);
        }

        function runBurstAnalysis() {
            showNotification('正在执行爆管影响分析...', 'info');
            setTimeout(() => {
                document.getElementById('analysisResults').innerHTML = `
                    <div style="text-align: left;">
                        <h4 style="color: #1e3c72; margin-bottom: 15px;">爆管影响分析结果</h4>
                        <div style="background: white; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <strong>爆管位置：</strong>建设路DN400主管<br>
                            <strong>影响用户：</strong>2,500户<br>
                            <strong>停水时长：</strong>4-6小时<br>
                            <strong>关阀方案：</strong>关闭V-001、V-003阀门<br>
                            <strong>应急供水：</strong>需要3辆供水车
                        </div>
                    </div>
                `;
                showNotification('爆管影响分析完成！', 'success');
            }, 2000);
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
