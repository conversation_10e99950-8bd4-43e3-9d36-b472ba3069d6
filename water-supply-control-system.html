<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>供水管网全局管控系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .control-container {
            max-width: 1800px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .control-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 30px 40px;
            margin-bottom: 25px;
            text-align: center;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .control-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.1), transparent);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3rem;
            color: #1e3c72;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 25px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
        }

        .system-status {
            display: flex;
            justify-content: center;
            gap: 40px;
            font-size: 1rem;
            color: #888;
            position: relative;
            z-index: 1;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .status-online { color: #4caf50; }
        .status-warning { color: #ff9800; }
        .status-error { color: #f44336; }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 12px;
            margin-bottom: 25px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            gap: 10px;
            overflow-x: auto;
        }

        .nav-tab {
            flex: 1;
            min-width: 200px;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            white-space: nowrap;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
            transform: translateY(-3px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            transform: translateY(-2px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            min-height: 800px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.6s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* KPI指标卡片 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 35px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border-left: 6px solid;
            cursor: pointer;
        }

        .kpi-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .kpi-card.supply { border-left-color: #2196f3; }
        .kpi-card.leakage { border-left-color: #f44336; }
        .kpi-card.equipment { border-left-color: #4caf50; }
        .kpi-card.quality { border-left-color: #ff9800; }
        .kpi-card.pressure { border-left-color: #9c27b0; }
        .kpi-card.flow { border-left-color: #00bcd4; }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .kpi-title {
            font-size: 1.2rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .kpi-trend {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.85rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .kpi-value {
            font-size: 2.8rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 15px;
            display: flex;
            align-items: baseline;
            gap: 10px;
        }

        .kpi-unit {
            font-size: 1.1rem;
            color: #888;
        }

        .kpi-comparison {
            display: flex;
            justify-content: space-between;
            font-size: 0.95rem;
            color: #666;
        }

        /* 图表区域 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            margin-bottom: 35px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.4rem;
            font-weight: 700;
            color: #1e3c72;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chart-controls {
            display: flex;
            gap: 12px;
        }

        .chart-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: white;
            color: #666;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .chart-btn.active {
            background: #1e3c72;
            color: white;
            border-color: #1e3c72;
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
            border-color: #1e3c72;
        }

        .chart-container {
            position: relative;
            height: 400px;
        }

        /* 地图容器 */
        .map-container {
            height: 600px;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1e3c72;
            font-size: 1.2rem;
        }

        /* 设备监控面板 */
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .equipment-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .equipment-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .equipment-card.normal { border-left-color: #4caf50; }
        .equipment-card.warning { border-left-color: #ff9800; }
        .equipment-card.error { border-left-color: #f44336; }

        .equipment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .equipment-name {
            font-size: 1.1rem;
            font-weight: 600;
            color: #1e3c72;
        }

        .equipment-status {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-normal { background: #e8f5e8; color: #2e7d32; }
        .status-warning { background: #fff3e0; color: #ef6c00; }
        .status-error { background: #ffebee; color: #c62828; }

        .equipment-params {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .param-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            font-size: 0.9rem;
        }

        .param-label {
            color: #666;
        }

        .param-value {
            font-weight: 600;
            color: #333;
        }

        /* 监控数据表格 */
        .monitor-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 25px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .monitor-table th,
        .monitor-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .monitor-table th {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .monitor-table tr:hover {
            background: rgba(30, 60, 114, 0.05);
        }

        .monitor-table .number {
            text-align: right;
            font-weight: 600;
        }

        /* 调度控制面板 */
        .dispatch-panel {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #1e3c72;
        }

        .panel-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #1e3c72;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .control-group {
            display: flex;
            flex-direction: column;
        }

        .control-label {
            font-weight: 600;
            color: #555;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .control-input {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .control-input:focus {
            outline: none;
            border-color: #1e3c72;
            box-shadow: 0 0 0 3px rgba(30, 60, 114, 0.1);
        }

        .control-select {
            padding: 12px 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            background: white;
            cursor: pointer;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        /* 预警信息 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 6px solid #ffc107;
            border-radius: 15px;
            padding: 20px 25px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 8px 32px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.danger {
            background: linear-gradient(135deg, #f8d7da, #fab1a0);
            border-left-color: #dc3545;
            box-shadow: 0 8px 32px rgba(220, 53, 69, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 8px 32px rgba(40, 167, 69, 0.2);
        }

        .alert-icon {
            font-size: 2rem;
            color: #856404;
        }

        .alert-panel.danger .alert-icon { color: #721c24; }
        .alert-panel.success .alert-icon { color: #155724; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.1rem;
            font-weight: 700;
            margin-bottom: 5px;
            color: #856404;
        }

        .alert-panel.danger .alert-title { color: #721c24; }
        .alert-panel.success .alert-title { color: #155724; }

        .alert-message {
            font-size: 0.95rem;
            line-height: 1.5;
            color: #856404;
        }

        .alert-panel.danger .alert-message { color: #721c24; }
        .alert-panel.success .alert-message { color: #155724; }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .control-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.5rem;
                flex-direction: column;
                gap: 15px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
                padding: 15px 20px;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .system-status {
                flex-direction: column;
                gap: 15px;
            }

            .chart-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .equipment-grid {
                grid-template-columns: 1fr;
            }

            .control-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            color: #1e3c72;
            font-size: 1.1rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="control-container">
        <!-- 系统头部 -->
        <div class="control-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tachometer-alt"></i>
                供水管网全局管控系统
                <i class="fas fa-water"></i>
            </h1>
            <p class="subtitle">Water Supply Network Global Control System - 全局管控 · 实时监控 · 智能调度</p>
            <div class="system-status">
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>系统状态：正常运行</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>设备在线：98.5%</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-warning"></i>
                    <span>预警信息：3条</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-circle status-online"></i>
                    <span>供水正常：99.2%</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-sync-alt"></i>
                    <span id="updateTime">更新时间：14:30:25</span>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="global-overview">
                <i class="fas fa-globe"></i>
                全局管控
            </button>
            <button class="nav-tab" data-tab="real-time-monitor">
                <i class="fas fa-desktop"></i>
                实时监控
            </button>
            <button class="nav-tab" data-tab="dispatch-management">
                <i class="fas fa-cogs"></i>
                调度管理
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 全局管控模块 -->
            <div id="global-overview" class="tab-content active">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-globe"></i>
                    全局管控 - 业务总览
                </h2>

                <!-- 关键指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card supply" onclick="jumpToDetail('supply')">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                日供水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            42,580
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>昨日：41,250 m³</span>
                            <span>计划：45,000 m³</span>
                        </div>
                    </div>

                    <div class="kpi-card leakage" onclick="jumpToDetail('leakage')">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                管网漏损率
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -0.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            6.2
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：≤5%</span>
                            <span>上月：7.0%</span>
                        </div>
                    </div>

                    <div class="kpi-card equipment" onclick="jumpToDetail('equipment')">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-cogs"></i>
                                设备完好率
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            98.5
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>目标：≥95%</span>
                            <span>故障设备：3台</span>
                        </div>
                    </div>

                    <div class="kpi-card quality" onclick="jumpToDetail('quality')">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-flask"></i>
                                水质达标率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.3%
                            </div>
                        </div>
                        <div class="kpi-value">
                            99.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-comparison">
                            <span>国标要求：≥95%</span>
                            <span>检测点：156个</span>
                        </div>
                    </div>
                </div>

                <!-- 管网拓扑展示 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-project-diagram"></i>
                            管网拓扑结构
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchTopologyView('overview')">总览</button>
                            <button class="chart-btn" onclick="switchTopologyView('detail')">详细</button>
                            <button class="chart-btn" onclick="switchTopologyView('3d')">3D视图</button>
                        </div>
                    </div>
                    <div class="map-container" id="topologyMap">
                        <div style="text-align: center;">
                            <i class="fas fa-project-diagram" style="font-size: 5rem; margin-bottom: 25px;"></i>
                            <div style="font-size: 1.5rem; font-weight: 600; margin-bottom: 15px;">管网拓扑结构图</div>
                            <div style="font-size: 1rem; margin-bottom: 10px;">基于GIS地图展示供水管网整体拓扑结构</div>
                            <div style="font-size: 0.9rem; color: #666;">包括管道走向、节点分布、设备位置等信息</div>
                            <div style="margin-top: 20px;">
                                <button class="btn btn-primary" onclick="loadTopologyMap()">
                                    <i class="fas fa-play"></i>
                                    加载拓扑图
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 业务数据汇总 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                供水量趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchSupplyPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchSupplyPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchSupplyPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="supplyTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                关键指标分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="kpiDistributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 实时监控模块 -->
            <div id="real-time-monitor" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-desktop"></i>
                    实时监控 - 设备与参数监控
                </h2>

                <!-- 监控预警 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">设备异常预警</div>
                        <div class="alert-message">3号水泵运行异常，出口压力偏低0.15MPa，建议立即检查；DMA-05区域压力监测点离线，需要现场维护</div>
                    </div>
                </div>

                <!-- 设备监控 -->
                <div class="dispatch-panel">
                    <div class="panel-title">
                        <i class="fas fa-cogs"></i>
                        关键设备实时监控
                    </div>

                    <div class="equipment-grid">
                        <div class="equipment-card error">
                            <div class="equipment-header">
                                <div class="equipment-name">3号水泵</div>
                                <div class="equipment-status status-error">故障</div>
                            </div>
                            <div class="equipment-params">
                                <div class="param-item">
                                    <span class="param-label">转速:</span>
                                    <span class="param-value" style="color: #f44336;">1420 rpm</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">出口压力:</span>
                                    <span class="param-value" style="color: #f44336;">0.45 MPa</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">电流:</span>
                                    <span class="param-value">85.2 A</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">电压:</span>
                                    <span class="param-value">380 V</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">运行时长:</span>
                                    <span class="param-value">8,450 h</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">启停次数:</span>
                                    <span class="param-value">1,256 次</span>
                                </div>
                            </div>
                        </div>

                        <div class="equipment-card normal">
                            <div class="equipment-header">
                                <div class="equipment-name">1号水泵</div>
                                <div class="equipment-status status-normal">正常</div>
                            </div>
                            <div class="equipment-params">
                                <div class="param-item">
                                    <span class="param-label">转速:</span>
                                    <span class="param-value" style="color: #4caf50;">1480 rpm</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">出口压力:</span>
                                    <span class="param-value" style="color: #4caf50;">0.62 MPa</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">电流:</span>
                                    <span class="param-value">92.8 A</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">电压:</span>
                                    <span class="param-value">385 V</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">运行时长:</span>
                                    <span class="param-value">6,280 h</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">启停次数:</span>
                                    <span class="param-value">892 次</span>
                                </div>
                            </div>
                        </div>

                        <div class="equipment-card warning">
                            <div class="equipment-header">
                                <div class="equipment-name">主控阀门V-001</div>
                                <div class="equipment-status status-warning">预警</div>
                            </div>
                            <div class="equipment-params">
                                <div class="param-item">
                                    <span class="param-label">开度:</span>
                                    <span class="param-value" style="color: #ff9800;">75%</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">上游压力:</span>
                                    <span class="param-value">0.68 MPa</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">下游压力:</span>
                                    <span class="param-value">0.52 MPa</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">流量:</span>
                                    <span class="param-value">1,850 m³/h</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">操作次数:</span>
                                    <span class="param-value">2,156 次</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">状态:</span>
                                    <span class="param-value" style="color: #ff9800;">开启</span>
                                </div>
                            </div>
                        </div>

                        <div class="equipment-card normal">
                            <div class="equipment-header">
                                <div class="equipment-name">加药设备A</div>
                                <div class="equipment-status status-normal">正常</div>
                            </div>
                            <div class="equipment-params">
                                <div class="param-item">
                                    <span class="param-label">投加量:</span>
                                    <span class="param-value" style="color: #4caf50;">25 mg/L</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">药剂余量:</span>
                                    <span class="param-value">85%</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">泵频率:</span>
                                    <span class="param-value">45 Hz</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">流量:</span>
                                    <span class="param-value">120 L/h</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">运行时长:</span>
                                    <span class="param-value">3,650 h</span>
                                </div>
                                <div class="param-item">
                                    <span class="param-label">状态:</span>
                                    <span class="param-value" style="color: #4caf50;">运行</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 压力监控 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-tachometer-alt"></i>
                                管网压力分布监控
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchPressureView('realtime')">实时</button>
                                <button class="chart-btn" onclick="switchPressureView('trend')">趋势</button>
                                <button class="chart-btn" onclick="switchPressureView('alarm')">预警</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="pressureMonitorChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-water"></i>
                                流量监控统计
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="flowMonitorChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 水质监控 -->
                <div class="dispatch-panel">
                    <div class="panel-title">
                        <i class="fas fa-flask"></i>
                        水质实时监控
                    </div>

                    <table class="monitor-table">
                        <thead>
                            <tr>
                                <th>监测点</th>
                                <th>浊度 (NTU)</th>
                                <th>余氯 (mg/L)</th>
                                <th>pH值</th>
                                <th>微生物 (CFU/mL)</th>
                                <th>检测时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>原水进厂</td>
                                <td class="number">8.5</td>
                                <td class="number">0.0</td>
                                <td class="number">7.8</td>
                                <td class="number">850</td>
                                <td>14:25:30</td>
                                <td class="status-normal">正常</td>
                            </tr>
                            <tr>
                                <td>出厂水</td>
                                <td class="number status-warning">0.85</td>
                                <td class="number">0.45</td>
                                <td class="number">7.2</td>
                                <td class="number">15</td>
                                <td>14:30:15</td>
                                <td class="status-warning">接近限值</td>
                            </tr>
                            <tr>
                                <td>管网末梢-东区</td>
                                <td class="number">0.72</td>
                                <td class="number">0.38</td>
                                <td class="number">7.1</td>
                                <td class="number">8</td>
                                <td>14:28:45</td>
                                <td class="status-normal">优秀</td>
                            </tr>
                            <tr>
                                <td>管网末梢-西区</td>
                                <td class="number">0.68</td>
                                <td class="number">0.42</td>
                                <td class="number">7.3</td>
                                <td class="number">12</td>
                                <td>14:29:20</td>
                                <td class="status-normal">良好</td>
                            </tr>
                            <tr>
                                <td>管网末梢-南区</td>
                                <td class="number">0.75</td>
                                <td class="number">0.35</td>
                                <td class="number">7.0</td>
                                <td class="number">18</td>
                                <td>14:27:10</td>
                                <td class="status-normal">良好</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- 蓄水池监控 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-layer-group"></i>
                                蓄水池水位监控
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="refreshReservoirData()">刷新数据</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="reservoirLevelChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-thermometer-half"></i>
                                蓄水池参数监控
                            </h3>
                        </div>
                        <div style="padding: 20px;">
                            <div class="equipment-grid" style="grid-template-columns: 1fr;">
                                <div class="equipment-card normal">
                                    <div class="equipment-header">
                                        <div class="equipment-name">1号蓄水池</div>
                                        <div class="equipment-status status-normal">正常</div>
                                    </div>
                                    <div class="equipment-params">
                                        <div class="param-item">
                                            <span class="param-label">当前水位:</span>
                                            <span class="param-value" style="color: #4caf50;">3.25 m</span>
                                        </div>
                                        <div class="param-item">
                                            <span class="param-label">容量:</span>
                                            <span class="param-value">65%</span>
                                        </div>
                                        <div class="param-item">
                                            <span class="param-label">水温:</span>
                                            <span class="param-value">18.5°C</span>
                                        </div>
                                        <div class="param-item">
                                            <span class="param-label">进水流量:</span>
                                            <span class="param-value">850 m³/h</span>
                                        </div>
                                        <div class="param-item">
                                            <span class="param-label">出水流量:</span>
                                            <span class="param-value">920 m³/h</span>
                                        </div>
                                        <div class="param-item">
                                            <span class="param-label">可用时长:</span>
                                            <span class="param-value">12.5 小时</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调度管理模块 -->
            <div id="dispatch-management" class="tab-content">
                <h2 style="color: #1e3c72; margin-bottom: 30px; display: flex; align-items: center; gap: 15px;">
                    <i class="fas fa-cogs"></i>
                    调度管理 - 水量调度与应急调度
                </h2>

                <!-- 调度状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">调度系统运行正常</div>
                        <div class="alert-message">当前供水调度方案运行稳定，管网压力均衡，预计今日供水量可达45,200m³，满足用户需求</div>
                    </div>
                </div>

                <!-- 水量调度 -->
                <div class="dispatch-panel">
                    <div class="panel-title">
                        <i class="fas fa-tint"></i>
                        水量调度控制
                    </div>

                    <div class="control-grid">
                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-tachometer-alt"></i>
                                目标供水量
                            </label>
                            <input type="number" class="control-input" id="targetSupply" value="45000" step="100">
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-cogs"></i>
                                运行水泵台数
                            </label>
                            <select class="control-select" id="pumpCount">
                                <option value="2">2台</option>
                                <option value="3" selected>3台</option>
                                <option value="4">4台</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-gauge"></i>
                                目标管网压力
                            </label>
                            <input type="number" class="control-input" id="targetPressure" value="0.6" step="0.05">
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-layer-group"></i>
                                蓄水池水位控制
                            </label>
                            <select class="control-select" id="reservoirControl">
                                <option value="auto" selected>自动控制</option>
                                <option value="manual">手动控制</option>
                                <option value="emergency">应急模式</option>
                            </select>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="applyDispatchPlan()">
                            <i class="fas fa-play"></i>
                            执行调度方案
                        </button>
                        <button class="btn btn-success" onclick="optimizeDispatch()">
                            <i class="fas fa-magic"></i>
                            智能优化
                        </button>
                        <button class="btn btn-warning" onclick="saveDispatchPlan()">
                            <i class="fas fa-save"></i>
                            保存方案
                        </button>
                    </div>
                </div>

                <!-- 应急调度 -->
                <div class="dispatch-panel">
                    <div class="panel-title">
                        <i class="fas fa-exclamation-triangle"></i>
                        应急调度预案
                    </div>

                    <div class="control-grid">
                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-list"></i>
                                应急事件类型
                            </label>
                            <select class="control-select" id="emergencyType">
                                <option value="">选择事件类型</option>
                                <option value="burst">管网爆管</option>
                                <option value="pollution">水质污染</option>
                                <option value="power">停电事故</option>
                                <option value="equipment">设备故障</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-map-marker-alt"></i>
                                事件位置
                            </label>
                            <input type="text" class="control-input" id="emergencyLocation" placeholder="输入事件发生位置">
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-exclamation-circle"></i>
                                紧急程度
                            </label>
                            <select class="control-select" id="emergencyLevel">
                                <option value="low">一般</option>
                                <option value="medium">重要</option>
                                <option value="high">紧急</option>
                                <option value="critical">特急</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label class="control-label">
                                <i class="fas fa-users"></i>
                                预计影响用户
                            </label>
                            <input type="number" class="control-input" id="affectedUsers" placeholder="输入影响用户数量">
                        </div>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-danger" onclick="activateEmergencyPlan()">
                            <i class="fas fa-exclamation-triangle"></i>
                            启动应急预案
                        </button>
                        <button class="btn btn-warning" onclick="generateEmergencyPlan()">
                            <i class="fas fa-file-alt"></i>
                            生成预案
                        </button>
                        <button class="btn btn-primary" onclick="viewEmergencyHistory()">
                            <i class="fas fa-history"></i>
                            历史记录
                        </button>
                    </div>
                </div>

                <!-- 调度效果分析 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                调度效果分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchDispatchPeriod('today')">今日</button>
                                <button class="chart-btn" onclick="switchDispatchPeriod('week')">本周</button>
                                <button class="chart-btn" onclick="switchDispatchPeriod('month')">本月</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="dispatchEffectChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                应急响应统计
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="emergencyStatsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 调度记录表 -->
                <table class="monitor-table">
                    <thead>
                        <tr>
                            <th>调度时间</th>
                            <th>调度类型</th>
                            <th>调度内容</th>
                            <th>执行状态</th>
                            <th>效果评估</th>
                            <th>操作人员</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>14:30:00</td>
                            <td>水量调度</td>
                            <td>增开1台水泵</td>
                            <td class="status-normal">已执行</td>
                            <td class="status-normal">良好</td>
                            <td>张工程师</td>
                            <td>应对用水高峰</td>
                        </tr>
                        <tr>
                            <td>13:45:00</td>
                            <td>压力调节</td>
                            <td>调整V-001阀门开度至80%</td>
                            <td class="status-normal">已执行</td>
                            <td class="status-normal">优秀</td>
                            <td>李技师</td>
                            <td>平衡管网压力</td>
                        </tr>
                        <tr>
                            <td>12:15:00</td>
                            <td>应急调度</td>
                            <td>启动备用水源</td>
                            <td class="status-normal">已完成</td>
                            <td class="status-normal">有效</td>
                            <td>王主管</td>
                            <td>应对设备故障</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'global-overview';
        let charts = {};
        let updateInterval;

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('供水管网全局管控系统初始化...');
            
            // 初始化标签页事件
            initTabEvents();
            
            // 初始化图表
            initCharts();
            
            // 启动实时更新
            startRealTimeUpdate();
            
            console.log('系统初始化完成');
        });

        // 初始化标签页事件
        function initTabEvents() {
            const tabs = document.querySelectorAll('.nav-tab');
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            }
            
            // 激活选中标签
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
            
            // 加载对应内容
            setTimeout(() => {
                loadTabContent(tabId);
            }, 100);
        }

        // 加载标签页内容
        function loadTabContent(tabId) {
            switch(tabId) {
                case 'global-overview':
                    // 全局管控已经在HTML中
                    break;
                case 'real-time-monitor':
                    loadRealTimeMonitorContent();
                    break;
                case 'dispatch-management':
                    loadDispatchManagementContent();
                    break;
            }
        }

        // 初始化图表
        function initCharts() {
            initSupplyTrendChart();
            initKPIDistributionChart();
        }

        // 供水量趋势图
        function initSupplyTrendChart() {
            const ctx = document.getElementById('supplyTrendChart');
            if (!ctx) return;
            
            charts.supplyTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '实际供水量',
                        data: [28000, 25000, 35000, 42000, 45000, 38000, 32000],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '计划供水量',
                        data: [30000, 28000, 38000, 45000, 47000, 40000, 35000],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '供水量 (m³)'
                            }
                        }
                    }
                }
            });
        }

        // KPI分布图
        function initKPIDistributionChart() {
            const ctx = document.getElementById('kpiDistributionChart');
            if (!ctx) return;
            
            charts.kpiDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['供水达标', '设备正常', '水质合格', '压力稳定', '其他'],
                    datasets: [{
                        data: [98.5, 98.5, 99.8, 96.2, 97.8],
                        backgroundColor: [
                            '#2196f3',
                            '#4caf50',
                            '#ff9800',
                            '#9c27b0',
                            '#00bcd4'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 跳转到详情页面
        function jumpToDetail(type) {
            const typeNames = {
                'supply': '供水量详情',
                'leakage': '漏损分析详情',
                'equipment': '设备监控详情',
                'quality': '水质监测详情'
            };
            
            showNotification(`正在跳转到${typeNames[type]}页面...`, 'info');
            
            // 模拟跳转延迟
            setTimeout(() => {
                showNotification(`${typeNames[type]}页面加载完成`, 'success');
            }, 1500);
        }

        // 切换拓扑视图
        function switchTopologyView(view) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const viewNames = {
                'overview': '总览视图',
                'detail': '详细视图',
                '3d': '3D视图'
            };
            
            showNotification(`已切换到${viewNames[view]}`, 'info');
        }

        // 加载拓扑地图
        function loadTopologyMap() {
            showNotification('正在加载管网拓扑图...', 'info');
            
            setTimeout(() => {
                showNotification('拓扑图加载完成！', 'success');
            }, 2000);
        }

        // 切换供水量周期
        function switchSupplyPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            showNotification(`已切换到${period}供水量视图`, 'info');
        }

        // 实时数据更新
        function startRealTimeUpdate() {
            updateInterval = setInterval(() => {
                const now = new Date();
                const timeStr = now.toLocaleTimeString();
                document.getElementById('updateTime').textContent = `更新时间：${timeStr}`;
                
                // 模拟数据更新
                updateRandomData();
            }, 30000);
        }

        // 更新随机数据
        function updateRandomData() {
            const kpiValues = document.querySelectorAll('.kpi-value');
            kpiValues.forEach(value => {
                const text = value.textContent;
                const number = parseFloat(text.replace(/[^\d.]/g, ''));
                
                if (isNaN(number)) return;
                
                const variation = (Math.random() - 0.5) * 0.02;
                const newNumber = number * (1 + variation);
                const unit = value.querySelector('.kpi-unit');
                const unitText = unit ? unit.textContent : '';
                
                if (number < 100) {
                    value.innerHTML = newNumber.toFixed(1) + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                } else {
                    value.innerHTML = Math.round(newNumber).toLocaleString() + 
                        (unitText ? `<span class="kpi-unit">${unitText}</span>` : '');
                }
            });
        }

        // 加载实时监控内容
        function loadRealTimeMonitorContent() {
            console.log('加载实时监控内容');
            initMonitorCharts();
        }

        // 加载调度管理内容
        function loadDispatchManagementContent() {
            console.log('加载调度管理内容');
            initDispatchCharts();
        }

        // 初始化监控图表
        function initMonitorCharts() {
            initPressureMonitorChart();
            initFlowMonitorChart();
            initReservoirLevelChart();
        }

        // 压力监控图表
        function initPressureMonitorChart() {
            const ctx = document.getElementById('pressureMonitorChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.pressureMonitor = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['监测点1', '监测点2', '监测点3', '监测点4', '监测点5', '监测点6'],
                    datasets: [{
                        label: '当前压力 (MPa)',
                        data: [0.65, 0.58, 0.62, 0.45, 0.68, 0.55],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '压力上限',
                        data: [0.8, 0.8, 0.8, 0.8, 0.8, 0.8],
                        borderColor: '#f44336',
                        borderDash: [5, 5],
                        fill: false
                    }, {
                        label: '压力下限',
                        data: [0.3, 0.3, 0.3, 0.3, 0.3, 0.3],
                        borderColor: '#ff9800',
                        borderDash: [5, 5],
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '压力 (MPa)' }
                        }
                    }
                }
            });
        }

        // 流量监控图表
        function initFlowMonitorChart() {
            const ctx = document.getElementById('flowMonitorChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.flowMonitor = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['泵站1', '泵站2', '泵站3', '主干管A', '主干管B', '支管网'],
                    datasets: [{
                        label: '瞬时流量 (m³/h)',
                        data: [1850, 1650, 1420, 2800, 2200, 1500],
                        backgroundColor: [
                            '#2196f3',
                            '#4caf50',
                            '#ff9800',
                            '#9c27b0',
                            '#00bcd4',
                            '#795548'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '流量 (m³/h)' }
                        }
                    }
                }
            });
        }

        // 蓄水池水位图表
        function initReservoirLevelChart() {
            const ctx = document.getElementById('reservoirLevelChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.reservoirLevel = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '1号池水位 (m)',
                        data: [3.8, 3.6, 3.2, 2.8, 3.0, 3.4, 3.25],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '2号池水位 (m)',
                        data: [4.2, 4.0, 3.8, 3.5, 3.8, 4.1, 4.0],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '安全上限',
                        data: [4.5, 4.5, 4.5, 4.5, 4.5, 4.5, 4.5],
                        borderColor: '#f44336',
                        borderDash: [5, 5],
                        fill: false
                    }, {
                        label: '安全下限',
                        data: [2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0],
                        borderColor: '#ff9800',
                        borderDash: [5, 5],
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '水位 (m)' }
                        }
                    }
                }
            });
        }

        // 初始化调度图表
        function initDispatchCharts() {
            initDispatchEffectChart();
            initEmergencyStatsChart();
        }

        // 调度效果图表
        function initDispatchEffectChart() {
            const ctx = document.getElementById('dispatchEffectChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.dispatchEffect = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['06:00', '08:00', '10:00', '12:00', '14:00', '16:00', '18:00'],
                    datasets: [{
                        label: '供水量 (m³/h)',
                        data: [1800, 2200, 2800, 3200, 3500, 3000, 2600],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '管网压力 (MPa)',
                        data: [0.58, 0.62, 0.65, 0.68, 0.66, 0.63, 0.60],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '供水量 (m³/h)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '压力 (MPa)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // 应急响应统计图表
        function initEmergencyStatsChart() {
            const ctx = document.getElementById('emergencyStatsChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.emergencyStats = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['管网爆管', '水质污染', '设备故障', '停电事故', '其他'],
                    datasets: [{
                        data: [8, 3, 12, 5, 4],
                        backgroundColor: [
                            '#f44336',
                            '#ff9800',
                            '#2196f3',
                            '#9c27b0',
                            '#4caf50'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 监控功能函数
        function switchPressureView(view) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const viewNames = {
                'realtime': '实时压力',
                'trend': '压力趋势',
                'alarm': '压力预警'
            };

            showNotification(`已切换到${viewNames[view]}视图`, 'info');
        }

        function refreshReservoirData() {
            showNotification('正在刷新蓄水池数据...', 'info');

            setTimeout(() => {
                showNotification('蓄水池数据已更新！', 'success');
            }, 1500);
        }

        // 调度管理功能函数
        function applyDispatchPlan() {
            const targetSupply = document.getElementById('targetSupply').value;
            const pumpCount = document.getElementById('pumpCount').value;
            const targetPressure = document.getElementById('targetPressure').value;

            showNotification('正在执行调度方案...', 'info');

            setTimeout(() => {
                showNotification(`调度方案已执行：目标供水量${targetSupply}m³，运行${pumpCount}台水泵`, 'success');
            }, 2000);
        }

        function optimizeDispatch() {
            showNotification('正在进行智能优化...', 'info');

            setTimeout(() => {
                showNotification('智能优化完成！预计节能12.5%，提升效率8.3%', 'success');
            }, 3000);
        }

        function saveDispatchPlan() {
            showNotification('正在保存调度方案...', 'info');

            setTimeout(() => {
                showNotification('调度方案已保存！', 'success');
            }, 1000);
        }

        function activateEmergencyPlan() {
            const emergencyType = document.getElementById('emergencyType').value;
            const emergencyLocation = document.getElementById('emergencyLocation').value;
            const emergencyLevel = document.getElementById('emergencyLevel').value;

            if (!emergencyType || !emergencyLocation) {
                showNotification('请填写完整的应急事件信息', 'warning');
                return;
            }

            showNotification('正在启动应急预案...', 'warning');

            setTimeout(() => {
                showNotification(`${emergencyType}应急预案已启动！紧急程度：${emergencyLevel}`, 'success');
            }, 2000);
        }

        function generateEmergencyPlan() {
            showNotification('正在生成应急预案...', 'info');

            setTimeout(() => {
                showNotification('应急预案已生成！', 'success');
            }, 1500);
        }

        function viewEmergencyHistory() {
            showNotification('正在加载应急历史记录...', 'info');

            setTimeout(() => {
                showNotification('历史记录加载完成！', 'success');
            }, 1000);
        }

        function switchDispatchPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                'today': '今日',
                'week': '本周',
                'month': '本月'
            };

            showNotification(`已切换到${periodNames[period]}调度分析`, 'info');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 350px;
                animation: slideInRight 0.3s ease-out;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
