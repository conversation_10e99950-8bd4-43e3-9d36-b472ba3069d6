<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .main-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .feature-tags {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .feature-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-1px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .stat-content {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-label .required {
            color: #e74c3c;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #e83e8c);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #cce5ff;
            color: #004085;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-urgent {
            background: #f8d7da;
            color: #721c24;
        }

        .status-normal {
            background: #e3f2fd;
            color: #1565c0;
        }

        .status-critical {
            background: #ffebee;
            color: #c62828;
        }

        /* 事件列表 */
        .event-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .event-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .event-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .event-meta {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            color: #6c757d;
            flex-wrap: wrap;
        }

        .event-content {
            line-height: 1.6;
            color: #495057;
            margin-bottom: 15px;
        }

        .event-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                padding: 15px 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .btn-group {
                flex-direction: column;
            }

            .event-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .event-meta {
                flex-direction: column;
                gap: 5px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .chart-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1 class="main-title">
                <i class="fas fa-exclamation-triangle"></i>
                事件管理系统
                <i class="fas fa-cogs"></i>
            </h1>
            <p class="subtitle">Event Management System</p>
            <div class="feature-tags">
                <div class="feature-tag">
                    <i class="fas fa-phone-alt"></i>
                    多渠道报修
                </div>
                <div class="feature-tag">
                    <i class="fas fa-bell"></i>
                    报警汇总
                </div>
                <div class="feature-tag">
                    <i class="fas fa-route"></i>
                    智能调度
                </div>
                <div class="feature-tag">
                    <i class="fas fa-users"></i>
                    响应中心
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" onclick="showTab('event-source')">
                <i class="fas fa-plus-circle"></i>
                事件来源管理
            </button>
            <button class="nav-tab" onclick="showTab('event-dispatch')">
                <i class="fas fa-route"></i>
                事件调度管理
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 事件来源管理标签页 -->
            <div id="event-source" class="tab-content active">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-plus-circle"></i>
                    事件来源管理
                </h2>

                <!-- 统计概览 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="stat-number">156</div>
                            <div class="stat-label">今日事件总数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="stat-number">89</div>
                            <div class="stat-label">移动端报修</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-bell"></i>
                            </div>
                            <div class="stat-number">45</div>
                            <div class="stat-label">系统报警</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div class="stat-number">22</div>
                            <div class="stat-label">电话报修</div>
                        </div>
                    </div>
                </div>

                <!-- 多渠道报修表单 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-edit"></i>
                            在线报修提交
                        </h3>
                        <div style="display: flex; gap: 10px;">
                            <span class="status-badge status-normal">
                                <i class="fas fa-globe"></i>
                                在线表单
                            </span>
                            <span class="status-badge status-processing">
                                <i class="fas fa-mobile-alt"></i>
                                移动端APP
                            </span>
                            <span class="status-badge status-pending">
                                <i class="fas fa-phone"></i>
                                电话集成
                            </span>
                        </div>
                    </div>

                    <form id="repairForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-tag"></i>
                                    事件类型 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="eventType" required>
                                    <option value="">请选择事件类型</option>
                                    <option value="equipment">设备故障</option>
                                    <option value="facility">设施维修</option>
                                    <option value="safety">安全事故</option>
                                    <option value="environment">环境异常</option>
                                    <option value="system">系统故障</option>
                                    <option value="other">其他问题</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    紧急程度 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="urgencyLevel" required>
                                    <option value="">请选择紧急程度</option>
                                    <option value="critical">紧急 - 立即处理</option>
                                    <option value="high">重要 - 4小时内</option>
                                    <option value="normal">一般 - 24小时内</option>
                                    <option value="low">较低 - 72小时内</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-map-marker-alt"></i>
                                    发生地点 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="location" required>
                                    <option value="">请选择发生地点</option>
                                    <option value="workshop-a">A车间</option>
                                    <option value="workshop-b">B车间</option>
                                    <option value="office">办公楼</option>
                                    <option value="warehouse">仓库</option>
                                    <option value="outdoor">室外区域</option>
                                    <option value="parking">停车场</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user"></i>
                                    报修人员 <span class="required">*</span>
                                </label>
                                <input type="text" class="form-input" id="reporter" required placeholder="请输入报修人姓名">
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-phone"></i>
                                    联系电话 <span class="required">*</span>
                                </label>
                                <input type="tel" class="form-input" id="phone" required placeholder="请输入联系电话">
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-building"></i>
                                    所属部门
                                </label>
                                <select class="form-select" id="department">
                                    <option value="">请选择所属部门</option>
                                    <option value="production">生产部</option>
                                    <option value="maintenance">维修部</option>
                                    <option value="quality">质量部</option>
                                    <option value="safety">安全部</option>
                                    <option value="logistics">物流部</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-alt"></i>
                                问题描述 <span class="required">*</span>
                            </label>
                            <textarea class="form-textarea" id="description" required placeholder="请详细描述发现的问题、故障现象、影响范围等..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-images"></i>
                                相关图片/视频
                            </label>
                            <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #667eea; margin-bottom: 15px;"></i>
                                <p style="color: #666; margin-bottom: 10px;">点击上传或拖拽文件到此区域</p>
                                <p style="color: #999; font-size: 0.9rem;">支持图片格式：JPG、PNG、GIF，视频格式：MP4、AVI</p>
                                <input type="file" id="fileInput" multiple accept="image/*,video/*" style="display: none;">
                            </div>
                            <div id="fileList" style="margin-top: 10px;"></div>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="submitRepair()">
                                <i class="fas fa-paper-plane"></i>
                                提交报修
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="saveDraft()">
                                <i class="fas fa-save"></i>
                                保存草稿
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                重置表单
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 事件调度管理标签页 -->
            <div id="event-dispatch" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-route"></i>
                    事件调度管理
                </h2>

                <!-- 响应中心统计 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">23</div>
                            <div class="stat-label">待处理事件</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-cogs"></i>
                            </div>
                            <div class="stat-number">45</div>
                            <div class="stat-label">处理中事件</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number">88</div>
                            <div class="stat-label">已完成事件</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-tachometer-alt"></i>
                            </div>
                            <div class="stat-number">2.3h</div>
                            <div class="stat-label">平均响应时间</div>
                        </div>
                    </div>
                </div>

                <!-- 响应中心控制台 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-desktop"></i>
                            响应中心控制台
                        </h3>
                        <div style="display: flex; gap: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部部门</option>
                                <option value="maintenance">维修部</option>
                                <option value="safety">安全部</option>
                                <option value="production">生产部</option>
                                <option value="quality">质量部</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部状态</option>
                                <option value="pending">待分配</option>
                                <option value="assigned">已分配</option>
                                <option value="processing">处理中</option>
                                <option value="completed">已完成</option>
                            </select>
                            <button class="btn btn-primary" onclick="refreshEvents()">
                                <i class="fas fa-sync-alt"></i>
                                刷新
                            </button>
                        </div>
                    </div>

                    <!-- 事件列表 -->
                    <div id="eventList">
                        <div class="event-item" data-event-id="EVT20240115001">
                            <div class="event-header">
                                <div>
                                    <strong style="color: #2c3e50;">EVT20240115001 - 设备故障</strong>
                                    <span class="status-badge status-urgent">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        紧急
                                    </span>
                                </div>
                                <div class="event-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> A车间</span>
                                    <span><i class="fas fa-user"></i> 张三</span>
                                    <span><i class="fas fa-clock"></i> 10:30</span>
                                    <span><i class="fas fa-phone"></i> 138****5678</span>
                                </div>
                            </div>
                            <div class="event-content">
                                <p><strong>问题描述：</strong>生产线主电机出现异响，温度过高，已紧急停机。影响整条生产线正常运行，需要立即处理。</p>
                                <p><strong>初步评估：</strong>可能是轴承损坏或润滑不足，需要专业维修人员现场检查。</p>
                            </div>
                            <div class="event-actions">
                                <button class="btn btn-success" onclick="assignEvent('EVT20240115001')">
                                    <i class="fas fa-user-plus"></i>
                                    分配处理
                                </button>
                                <button class="btn btn-warning" onclick="escalateEvent('EVT20240115001')">
                                    <i class="fas fa-level-up-alt"></i>
                                    升级处理
                                </button>
                                <button class="btn btn-primary" onclick="viewEventDetail('EVT20240115001')">
                                    <i class="fas fa-eye"></i>
                                    查看详情
                                </button>
                                <button class="btn btn-secondary" onclick="addComment('EVT20240115001')">
                                    <i class="fas fa-comment"></i>
                                    添加备注
                                </button>
                            </div>
                        </div>

                        <div class="event-item" data-event-id="EVT20240115002">
                            <div class="event-header">
                                <div>
                                    <strong style="color: #2c3e50;">EVT20240115002 - 设施维修</strong>
                                    <span class="status-badge status-processing">
                                        <i class="fas fa-cogs"></i>
                                        处理中
                                    </span>
                                </div>
                                <div class="event-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> 办公楼</span>
                                    <span><i class="fas fa-user"></i> 李四</span>
                                    <span><i class="fas fa-clock"></i> 09:15</span>
                                    <span><i class="fas fa-user-cog"></i> 王维修</span>
                                </div>
                            </div>
                            <div class="event-content">
                                <p><strong>问题描述：</strong>3楼会议室空调不制冷，室内温度过高，影响会议正常进行。</p>
                                <p><strong>处理进展：</strong>维修人员已到现场，正在检查空调系统，预计1小时内完成维修。</p>
                            </div>
                            <div class="event-actions">
                                <button class="btn btn-warning" onclick="transferEvent('EVT20240115002')">
                                    <i class="fas fa-exchange-alt"></i>
                                    转交处理
                                </button>
                                <button class="btn btn-primary" onclick="viewEventDetail('EVT20240115002')">
                                    <i class="fas fa-eye"></i>
                                    查看详情
                                </button>
                                <button class="btn btn-success" onclick="updateProgress('EVT20240115002')">
                                    <i class="fas fa-tasks"></i>
                                    更新进度
                                </button>
                            </div>
                        </div>

                        <div class="event-item" data-event-id="EVT20240115003">
                            <div class="event-header">
                                <div>
                                    <strong style="color: #2c3e50;">EVT20240115003 - 安全事故</strong>
                                    <span class="status-badge status-critical">
                                        <i class="fas fa-exclamation-circle"></i>
                                        重大
                                    </span>
                                </div>
                                <div class="event-meta">
                                    <span><i class="fas fa-map-marker-alt"></i> B车间</span>
                                    <span><i class="fas fa-user"></i> 赵六</span>
                                    <span><i class="fas fa-clock"></i> 08:45</span>
                                    <span><i class="fas fa-phone"></i> 139****1234</span>
                                </div>
                            </div>
                            <div class="event-content">
                                <p><strong>问题描述：</strong>员工在操作过程中手部被机器夹伤，已送医院处理，现场需要安全检查。</p>
                                <p><strong>紧急措施：</strong>已停止相关设备运行，安全部门正在调查事故原因，需要上级部门介入。</p>
                            </div>
                            <div class="event-actions">
                                <button class="btn btn-danger" onclick="emergencyResponse('EVT20240115003')">
                                    <i class="fas fa-ambulance"></i>
                                    紧急响应
                                </button>
                                <button class="btn btn-warning" onclick="escalateEvent('EVT20240115003')">
                                    <i class="fas fa-level-up-alt"></i>
                                    上报领导
                                </button>
                                <button class="btn btn-primary" onclick="viewEventDetail('EVT20240115003')">
                                    <i class="fas fa-eye"></i>
                                    查看详情
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 事件分配面板 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-users-cog"></i>
                            事件分配与调度
                        </h3>
                    </div>

                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">选择事件</label>
                            <select class="form-select" id="selectedEvent">
                                <option value="">请选择要分配的事件</option>
                                <option value="EVT20240115001">EVT20240115001 - 设备故障 (紧急)</option>
                                <option value="EVT20240115004">EVT20240115004 - 设施维修 (一般)</option>
                                <option value="EVT20240115005">EVT20240115005 - 系统故障 (重要)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">处理部门</label>
                            <select class="form-select" id="assignDepartment">
                                <option value="">请选择处理部门</option>
                                <option value="maintenance">维修部</option>
                                <option value="safety">安全部</option>
                                <option value="it">信息技术部</option>
                                <option value="facility">设施管理部</option>
                                <option value="production">生产部</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">指定处理人</label>
                            <select class="form-select" id="assignPerson">
                                <option value="">请选择处理人员</option>
                                <option value="wang-repair">王维修 - 维修部</option>
                                <option value="li-safety">李安全 - 安全部</option>
                                <option value="zhang-it">张技术 - IT部</option>
                                <option value="zhao-facility">赵设施 - 设施部</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">预计完成时间</label>
                            <input type="datetime-local" class="form-input" id="expectedTime">
                        </div>

                        <div class="form-group">
                            <label class="form-label">优先级调整</label>
                            <select class="form-select" id="priorityLevel">
                                <option value="critical">紧急 - 立即处理</option>
                                <option value="high">重要 - 4小时内</option>
                                <option value="normal">一般 - 24小时内</option>
                                <option value="low">较低 - 72小时内</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">调度备注</label>
                            <textarea class="form-textarea" id="dispatchNotes" placeholder="请输入调度说明、特殊要求或注意事项..."></textarea>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button class="btn btn-success" onclick="confirmAssignment()">
                            <i class="fas fa-check"></i>
                            确认分配
                        </button>
                        <button class="btn btn-warning" onclick="batchAssign()">
                            <i class="fas fa-layer-group"></i>
                            批量分配
                        </button>
                        <button class="btn btn-primary" onclick="autoAssign()">
                            <i class="fas fa-magic"></i>
                            智能分配
                        </button>
                    </div>
                </div>

                <!-- 流转历史 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-history"></i>
                            事件流转历史
                        </h3>
                        <button class="btn btn-secondary" onclick="exportFlowHistory()">
                            <i class="fas fa-download"></i>
                            导出记录
                        </button>
                    </div>

                    <div style="overflow-x: auto;">
                        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
                            <thead>
                                <tr style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                                    <th style="padding: 12px; text-align: left;">事件编号</th>
                                    <th style="padding: 12px; text-align: left;">流转时间</th>
                                    <th style="padding: 12px; text-align: left;">操作人</th>
                                    <th style="padding: 12px; text-align: left;">流转类型</th>
                                    <th style="padding: 12px; text-align: left;">从</th>
                                    <th style="padding: 12px; text-align: left;">到</th>
                                    <th style="padding: 12px; text-align: left;">流转原因</th>
                                    <th style="padding: 12px; text-align: left;">状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr style="border-bottom: 1px solid #ecf0f1;">
                                    <td style="padding: 12px;">EVT20240115001</td>
                                    <td style="padding: 12px;">2024-01-15 10:35</td>
                                    <td style="padding: 12px;">调度员A</td>
                                    <td style="padding: 12px;">分配</td>
                                    <td style="padding: 12px;">响应中心</td>
                                    <td style="padding: 12px;">维修部-王维修</td>
                                    <td style="padding: 12px;">设备专业故障</td>
                                    <td style="padding: 12px;"><span class="status-badge status-processing">已分配</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #ecf0f1;">
                                    <td style="padding: 12px;">EVT20240115002</td>
                                    <td style="padding: 12px;">2024-01-15 09:20</td>
                                    <td style="padding: 12px;">王维修</td>
                                    <td style="padding: 12px;">转交</td>
                                    <td style="padding: 12px;">维修部-王维修</td>
                                    <td style="padding: 12px;">设施部-赵设施</td>
                                    <td style="padding: 12px;">空调专业问题</td>
                                    <td style="padding: 12px;"><span class="status-badge status-processing">处理中</span></td>
                                </tr>
                                <tr style="border-bottom: 1px solid #ecf0f1;">
                                    <td style="padding: 12px;">EVT20240115003</td>
                                    <td style="padding: 12px;">2024-01-15 08:50</td>
                                    <td style="padding: 12px;">调度员B</td>
                                    <td style="padding: 12px;">升级</td>
                                    <td style="padding: 12px;">安全部-李安全</td>
                                    <td style="padding: 12px;">安全总监</td>
                                    <td style="padding: 12px;">重大安全事故</td>
                                    <td style="padding: 12px;"><span class="status-badge status-urgent">升级处理</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 统计图表 -->
                <div class="chart-grid">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                事件类型分布
                            </h4>
                        </div>
                        <div class="chart-container">
                            <canvas id="eventTypeChart"></canvas>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">
                                <i class="fas fa-chart-line"></i>
                                处理时效统计
                            </h4>
                        </div>
                        <div class="chart-container">
                            <canvas id="responseTimeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-8px)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 文件上传拖拽功能
            setupFileUpload();
        });

        // 标签页切换功能
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活选中的标签
            event.target.classList.add('active');

            // 根据标签页加载相应内容
            if (tabId === 'event-dispatch') {
                loadEventDispatch();
            }
        }

        // 提交报修
        function submitRepair() {
            const formData = collectRepairData();
            if (validateRepairData(formData)) {
                // 模拟提交到服务器
                showNotification('正在提交报修信息...', 'info');
                
                setTimeout(() => {
                    const eventId = generateEventId();
                    showNotification(`报修提交成功！事件编号：${eventId}`, 'success');
                    document.getElementById('repairForm').reset();
                    document.getElementById('fileList').innerHTML = '';
                }, 2000);
            }
        }

        // 保存草稿
        function saveDraft() {
            const formData = collectRepairData();
            localStorage.setItem('repairDraft', JSON.stringify(formData));
            showNotification('草稿保存成功！', 'success');
        }

        // 收集表单数据
        function collectRepairData() {
            return {
                eventType: document.getElementById('eventType').value,
                urgencyLevel: document.getElementById('urgencyLevel').value,
                location: document.getElementById('location').value,
                reporter: document.getElementById('reporter').value,
                phone: document.getElementById('phone').value,
                department: document.getElementById('department').value,
                description: document.getElementById('description').value,
                timestamp: new Date().toISOString()
            };
        }

        // 验证表单数据
        function validateRepairData(data) {
            if (!data.eventType || !data.urgencyLevel || !data.location || !data.reporter || !data.phone || !data.description) {
                showNotification('请填写所有必填项！', 'error');
                return false;
            }
            
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(data.phone)) {
                showNotification('请输入正确的手机号码！', 'error');
                return false;
            }
            
            return true;
        }

        // 生成事件编号
        function generateEventId() {
            const now = new Date();
            const dateStr = now.getFullYear() + 
                String(now.getMonth() + 1).padStart(2, '0') + 
                String(now.getDate()).padStart(2, '0');
            const timeStr = String(now.getHours()).padStart(2, '0') + 
                String(now.getMinutes()).padStart(2, '0');
            const randomNum = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
            return `EVT${dateStr}${timeStr}${randomNum}`;
        }

        // 设置文件上传功能
        function setupFileUpload() {
            const uploadArea = document.querySelector('.upload-area');
            const fileInput = document.getElementById('fileInput');
            const fileList = document.getElementById('fileList');

            // 拖拽上传
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files);
            });

            // 文件选择
            fileInput.addEventListener('change', function(e) {
                handleFiles(e.target.files);
            });

            function handleFiles(files) {
                fileList.innerHTML = '';
                Array.from(files).forEach(file => {
                    const fileItem = document.createElement('div');
                    fileItem.style.cssText = `
                        display: flex;
                        align-items: center;
                        gap: 10px;
                        padding: 10px;
                        background: #f8f9fa;
                        border-radius: 6px;
                        margin-bottom: 5px;
                    `;
                    
                    const fileIcon = file.type.startsWith('image/') ? 'fa-image' : 'fa-video';
                    fileItem.innerHTML = `
                        <i class="fas ${fileIcon}" style="color: #667eea;"></i>
                        <span style="flex: 1;">${file.name}</span>
                        <span style="color: #666; font-size: 0.9rem;">${formatFileSize(file.size)}</span>
                        <button type="button" onclick="this.parentElement.remove()" style="background: none; border: none; color: #dc3545; cursor: pointer;">
                            <i class="fas fa-times"></i>
                        </button>
                    `;
                    fileList.appendChild(fileItem);
                });
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 400px;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 加载事件调度管理功能
        function loadEventDispatch() {
            // 设置预计完成时间默认值（当前时间+4小时）
            const now = new Date();
            now.setHours(now.getHours() + 4);
            const defaultTime = now.toISOString().slice(0, 16);
            document.getElementById('expectedTime').value = defaultTime;

            // 初始化图表
            initializeDispatchCharts();
        }

        // 初始化调度管理图表
        function initializeDispatchCharts() {
            // 事件类型分布饼图
            const eventTypeCtx = document.getElementById('eventTypeChart');
            if (eventTypeCtx) {
                new Chart(eventTypeCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['设备故障', '设施维修', '安全事故', '系统故障', '环境异常', '其他'],
                        datasets: [{
                            data: [35, 28, 12, 18, 15, 8],
                            backgroundColor: [
                                '#667eea',
                                '#764ba2',
                                '#f093fb',
                                '#f5576c',
                                '#4facfe',
                                '#43e97b'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 处理时效统计柱状图
            const responseTimeCtx = document.getElementById('responseTimeChart');
            if (responseTimeCtx) {
                new Chart(responseTimeCtx, {
                    type: 'bar',
                    data: {
                        labels: ['< 1小时', '1-4小时', '4-24小时', '1-3天', '> 3天'],
                        datasets: [{
                            label: '事件数量',
                            data: [45, 32, 28, 15, 8],
                            backgroundColor: [
                                '#28a745',
                                '#17a2b8',
                                '#ffc107',
                                '#fd7e14',
                                '#dc3545'
                            ],
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '事件数量'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }

        // 刷新事件列表
        function refreshEvents() {
            showNotification('正在刷新事件列表...', 'info');

            setTimeout(() => {
                showNotification('事件列表已更新！', 'success');
                // 这里可以重新加载事件数据
            }, 1000);
        }

        // 分配事件
        function assignEvent(eventId) {
            showAssignModal(eventId);
        }

        // 显示分配模态框
        function showAssignModal(eventId) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 600px; padding: 30px; box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding-bottom: 15px; border-bottom: 2px solid #f8f9fa;">
                        <h3 style="color: #2c3e50; margin: 0;">分配事件 - ${eventId}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">&times;</button>
                    </div>

                    <div style="display: grid; gap: 20px;">
                        <div>
                            <label style="display: block; font-weight: 600; margin-bottom: 8px;">处理部门</label>
                            <select id="modalDepartment" style="width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px;">
                                <option value="">请选择处理部门</option>
                                <option value="maintenance">维修部</option>
                                <option value="safety">安全部</option>
                                <option value="it">信息技术部</option>
                                <option value="facility">设施管理部</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; font-weight: 600; margin-bottom: 8px;">指定处理人</label>
                            <select id="modalPerson" style="width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px;">
                                <option value="">请选择处理人员</option>
                                <option value="wang-repair">王维修 - 维修部</option>
                                <option value="li-safety">李安全 - 安全部</option>
                                <option value="zhang-it">张技术 - IT部</option>
                            </select>
                        </div>

                        <div>
                            <label style="display: block; font-weight: 600; margin-bottom: 8px;">分配说明</label>
                            <textarea id="modalNotes" style="width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px; min-height: 80px;" placeholder="请输入分配说明..."></textarea>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; margin-top: 25px; justify-content: flex-end;">
                        <button onclick="this.closest('.modal').remove()" style="padding: 12px 25px; border: none; border-radius: 8px; background: #6c757d; color: white; cursor: pointer;">取消</button>
                        <button onclick="confirmEventAssignment('${eventId}')" style="padding: 12px 25px; border: none; border-radius: 8px; background: linear-gradient(135deg, #28a745, #20c997); color: white; cursor: pointer;">确认分配</button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // 确认事件分配
        function confirmEventAssignment(eventId) {
            const department = document.getElementById('modalDepartment').value;
            const person = document.getElementById('modalPerson').value;
            const notes = document.getElementById('modalNotes').value;

            if (!department || !person) {
                showNotification('请选择处理部门和人员！', 'error');
                return;
            }

            showNotification(`正在分配事件 ${eventId}...`, 'info');

            setTimeout(() => {
                // 更新事件状态
                const eventItem = document.querySelector(`[data-event-id="${eventId}"]`);
                if (eventItem) {
                    const statusBadge = eventItem.querySelector('.status-badge');
                    statusBadge.className = 'status-badge status-processing';
                    statusBadge.innerHTML = '<i class="fas fa-cogs"></i> 已分配';
                }

                document.querySelector('.modal').remove();
                showNotification(`事件 ${eventId} 分配成功！`, 'success');
            }, 1500);
        }

        // 升级事件
        function escalateEvent(eventId) {
            if (confirm(`确定要将事件 ${eventId} 升级到上级处理吗？`)) {
                showNotification(`正在升级事件 ${eventId}...`, 'info');

                setTimeout(() => {
                    showNotification(`事件 ${eventId} 已升级到上级部门处理！`, 'success');
                }, 1500);
            }
        }

        // 转交事件
        function transferEvent(eventId) {
            showTransferModal(eventId);
        }

        // 显示转交模态框
        function showTransferModal(eventId) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 500px; padding: 30px;">
                    <h3 style="color: #2c3e50; margin-bottom: 20px;">转交事件 - ${eventId}</h3>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; font-weight: 600; margin-bottom: 8px;">转交给</label>
                        <select id="transferTo" style="width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px;">
                            <option value="">请选择转交对象</option>
                            <option value="zhang-it">张技术 - IT部</option>
                            <option value="zhao-facility">赵设施 - 设施部</option>
                            <option value="liu-electric">刘电工 - 维修部</option>
                        </select>
                    </div>

                    <div style="margin-bottom: 20px;">
                        <label style="display: block; font-weight: 600; margin-bottom: 8px;">转交原因</label>
                        <textarea id="transferReason" style="width: 100%; padding: 12px; border: 2px solid #ecf0f1; border-radius: 8px; min-height: 80px;" placeholder="请说明转交原因..."></textarea>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="this.closest('.modal').remove()" style="padding: 12px 25px; border: none; border-radius: 8px; background: #6c757d; color: white; cursor: pointer;">取消</button>
                        <button onclick="confirmTransfer('${eventId}')" style="padding: 12px 25px; border: none; border-radius: 8px; background: linear-gradient(135deg, #ffc107, #fd7e14); color: white; cursor: pointer;">确认转交</button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // 确认转交
        function confirmTransfer(eventId) {
            const transferTo = document.getElementById('transferTo').value;
            const reason = document.getElementById('transferReason').value;

            if (!transferTo || !reason.trim()) {
                showNotification('请选择转交对象并填写转交原因！', 'error');
                return;
            }

            showNotification(`正在转交事件 ${eventId}...`, 'info');

            setTimeout(() => {
                document.querySelector('.modal').remove();
                showNotification(`事件 ${eventId} 转交成功！`, 'success');
            }, 1500);
        }

        // 查看事件详情
        function viewEventDetail(eventId) {
            showEventDetailModal(eventId);
        }

        // 显示事件详情模态框
        function showEventDetailModal(eventId) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(5px);
                z-index: 1000;
                display: flex;
                align-items: center;
                justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 15px; width: 90%; max-width: 800px; max-height: 80vh; overflow-y: auto; padding: 30px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding-bottom: 15px; border-bottom: 2px solid #f8f9fa;">
                        <h3 style="color: #2c3e50; margin: 0;">事件详情 - ${eventId}</h3>
                        <button onclick="this.closest('.modal').remove()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666;">&times;</button>
                    </div>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px; margin-bottom: 25px;">
                        <div>
                            <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">基本信息</h4>
                            <div style="space-y: 10px;">
                                <div style="margin-bottom: 10px;"><strong>事件编号：</strong>${eventId}</div>
                                <div style="margin-bottom: 10px;"><strong>事件类型：</strong>设备故障</div>
                                <div style="margin-bottom: 10px;"><strong>紧急程度：</strong><span class="status-badge status-urgent">紧急</span></div>
                                <div style="margin-bottom: 10px;"><strong>发生地点：</strong>A车间</div>
                                <div style="margin-bottom: 10px;"><strong>报修人员：</strong>张三</div>
                                <div style="margin-bottom: 10px;"><strong>联系电话：</strong>138****5678</div>
                                <div style="margin-bottom: 10px;"><strong>报修时间：</strong>2024-01-15 10:30</div>
                            </div>
                        </div>
                        <div>
                            <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #e74c3c; padding-bottom: 5px;">处理信息</h4>
                            <div style="space-y: 10px;">
                                <div style="margin-bottom: 10px;"><strong>当前状态：</strong><span class="status-badge status-processing">处理中</span></div>
                                <div style="margin-bottom: 10px;"><strong>处理部门：</strong>维修部</div>
                                <div style="margin-bottom: 10px;"><strong>处理人员：</strong>王维修</div>
                                <div style="margin-bottom: 10px;"><strong>分配时间：</strong>2024-01-15 10:35</div>
                                <div style="margin-bottom: 10px;"><strong>预计完成：</strong>2024-01-15 14:30</div>
                                <div style="margin-bottom: 10px;"><strong>响应时间：</strong>5分钟</div>
                            </div>
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">问题描述</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #667eea;">
                            生产线主电机出现异响，温度过高，已紧急停机。影响整条生产线正常运行，需要立即处理。初步判断可能是轴承损坏或润滑不足。
                        </div>
                    </div>

                    <div style="margin-bottom: 25px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">处理进展</h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                            <div style="margin-bottom: 10px;"><strong>10:35</strong> - 事件已分配给维修部王维修</div>
                            <div style="margin-bottom: 10px;"><strong>10:45</strong> - 维修人员已到达现场开始检查</div>
                            <div style="margin-bottom: 10px;"><strong>11:20</strong> - 确认轴承损坏，正在准备更换配件</div>
                            <div style="margin-bottom: 10px;"><strong>12:30</strong> - 配件已到位，开始更换作业</div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; justify-content: flex-end;">
                        <button onclick="this.closest('.modal').remove()" style="padding: 12px 25px; border: none; border-radius: 8px; background: #6c757d; color: white; cursor: pointer;">关闭</button>
                        <button onclick="printEventDetail('${eventId}')" style="padding: 12px 25px; border: none; border-radius: 8px; background: linear-gradient(135deg, #667eea, #764ba2); color: white; cursor: pointer;">打印详情</button>
                    </div>
                </div>
            `;

            modal.className = 'modal';
            document.body.appendChild(modal);
        }

        // 添加备注
        function addComment(eventId) {
            const comment = prompt(`请输入对事件 ${eventId} 的备注：`);
            if (comment && comment.trim()) {
                showNotification(`备注已添加到事件 ${eventId}`, 'success');
            }
        }

        // 更新进度
        function updateProgress(eventId) {
            const progress = prompt(`请输入事件 ${eventId} 的最新进展：`);
            if (progress && progress.trim()) {
                showNotification(`事件 ${eventId} 进度已更新`, 'success');
            }
        }

        // 紧急响应
        function emergencyResponse(eventId) {
            if (confirm(`确定启动事件 ${eventId} 的紧急响应程序吗？`)) {
                showNotification(`正在启动紧急响应程序...`, 'info');

                setTimeout(() => {
                    showNotification(`事件 ${eventId} 紧急响应程序已启动！`, 'success');
                }, 2000);
            }
        }

        // 确认分配
        function confirmAssignment() {
            const eventId = document.getElementById('selectedEvent').value;
            const department = document.getElementById('assignDepartment').value;
            const person = document.getElementById('assignPerson').value;

            if (!eventId || !department || !person) {
                showNotification('请完整填写分配信息！', 'error');
                return;
            }

            showNotification(`正在分配事件 ${eventId}...`, 'info');

            setTimeout(() => {
                showNotification('事件分配成功！', 'success');
                // 清空表单
                document.getElementById('selectedEvent').value = '';
                document.getElementById('assignDepartment').value = '';
                document.getElementById('assignPerson').value = '';
                document.getElementById('dispatchNotes').value = '';
            }, 1500);
        }

        // 批量分配
        function batchAssign() {
            showNotification('批量分配功能开发中...', 'info');
        }

        // 智能分配
        function autoAssign() {
            showNotification('正在进行智能分配...', 'info');

            setTimeout(() => {
                showNotification('智能分配完成！已根据专业领域和工作负荷自动分配事件', 'success');
            }, 3000);
        }

        // 导出流转历史
        function exportFlowHistory() {
            showNotification('正在导出流转历史...', 'info');

            setTimeout(() => {
                showNotification('流转历史导出成功！', 'success');
            }, 2000);
        }

        // 打印事件详情
        function printEventDetail(eventId) {
            window.print();
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
