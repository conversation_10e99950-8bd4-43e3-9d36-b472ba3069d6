<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水泵优化组合调度系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #4facfe 50%, #00f2fe 75%, #43e97b 100%);
            min-height: 100vh;
            color: #333;
        }

        .dispatch-container {
            max-width: 1920px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .dispatch-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(30px);
            border-radius: 35px;
            padding: 45px 55px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }

        .dispatch-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.08), transparent);
            animation: shimmer 5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3.8rem;
            background: linear-gradient(135deg, #1e3c72, #2a5298, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 35px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.5rem;
            margin-bottom: 35px;
            position: relative;
            z-index: 1;
        }

        .system-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            gap: 30px;
            margin-top: 40px;
            position: relative;
            z-index: 1;
        }

        .overview-item {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 25px;
            text-align: center;
            backdrop-filter: blur(20px);
            transition: all 0.5s ease;
            border: 3px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .overview-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #1e3c72, #4facfe, #00f2fe);
            transform: scaleX(0);
            transition: transform 0.5s ease;
        }

        .overview-item:hover {
            transform: translateY(-10px);
            border-color: #4facfe;
            box-shadow: 0 20px 50px rgba(79, 172, 254, 0.3);
        }

        .overview-item:hover::before {
            transform: scaleX(1);
        }

        .overview-icon {
            font-size: 3.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .overview-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 10px;
        }

        .overview-value {
            font-size: 2.2rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 8px;
        }

        .overview-desc {
            font-size: 1rem;
            color: #666;
        }

        /* 导航标签 */
        .nav-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 20px;
            margin-bottom: 30px;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
            display: flex;
            gap: 15px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            flex: 1;
            min-width: 220px;
            padding: 25px 35px;
            background: transparent;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 1.2rem;
            font-weight: 700;
            color: #7f8c8d;
            transition: all 0.5s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            transition: left 0.5s ease;
            z-index: -1;
        }

        .nav-tab.active::before {
            left: 0;
        }

        .nav-tab.active {
            color: white;
            box-shadow: 0 15px 40px rgba(30, 60, 114, 0.4);
            transform: translateY(-5px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            transform: translateY(-4px);
        }

        .nav-icon {
            font-size: 2rem;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 35px;
            padding: 50px;
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
            min-height: 1000px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 1s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 模块标题 */
        .module-title {
            font-size: 2.5rem;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 40px;
            display: flex;
            align-items: center;
            gap: 20px;
            padding-bottom: 25px;
            border-bottom: 4px solid #f0f0f0;
            position: relative;
        }

        .module-title::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 120px;
            height: 4px;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
        }

        /* 优化卡片 */
        .optimization-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 35px;
            margin-bottom: 45px;
        }

        .optimization-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.5s ease;
            border-left: 10px solid;
            cursor: pointer;
        }

        .optimization-card:hover {
            transform: translateY(-12px);
            box-shadow: 0 30px 80px rgba(0, 0, 0, 0.2);
        }

        .optimization-card.efficiency { border-left-color: #2196f3; }
        .optimization-card.combination { border-left-color: #4caf50; }
        .optimization-card.schedule { border-left-color: #ff9800; }
        .optimization-card.energy { border-left-color: #9c27b0; }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .card-title {
            font-size: 1.4rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 700;
        }

        .card-status {
            padding: 10px 18px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-optimal { background: #e8f5e8; color: #2e7d32; }
        .status-good { background: #e3f2fd; color: #1565c0; }
        .status-normal { background: #fff3e0; color: #ef6c00; }

        .card-value {
            font-size: 3.5rem;
            font-weight: bold;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            display: flex;
            align-items: baseline;
            gap: 15px;
        }

        .card-unit {
            font-size: 1.3rem;
            color: #888;
        }

        .card-description {
            font-size: 1.1rem;
            color: #666;
            line-height: 1.7;
            margin-bottom: 25px;
        }

        .card-progress {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 15px;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
            text-align: right;
        }

        /* 图表区域 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-bottom: 45px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            transition: all 0.4s ease;
        }

        .chart-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 35px;
            padding-bottom: 25px;
            border-bottom: 3px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.6rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .chart-controls {
            display: flex;
            gap: 15px;
        }

        .chart-btn {
            padding: 12px 22px;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            background: white;
            color: #666;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.4s ease;
            font-weight: 700;
        }

        .chart-btn.active {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
            border-color: transparent;
            box-shadow: 0 10px 30px rgba(30, 60, 114, 0.3);
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
            border-color: #1e3c72;
        }

        .chart-container {
            position: relative;
            height: 500px;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 35px;
            background: white;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 20px 30px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
            font-weight: 700;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(30, 60, 114, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 700;
        }

        .status-running { color: #4caf50; font-weight: 700; }
        .status-stopped { color: #f44336; font-weight: 700; }
        .status-standby { color: #ff9800; font-weight: 700; }
        .status-maintenance { color: #9e9e9e; font-weight: 700; }

        /* 按钮样式 */
        .btn {
            padding: 16px 32px;
            border: none;
            border-radius: 15px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            transition: all 0.4s ease;
            display: inline-flex;
            align-items: center;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 35px rgba(30, 60, 114, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin-top: 35px;
            flex-wrap: wrap;
        }

        /* 预警面板 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 10px solid #ffc107;
            border-radius: 25px;
            padding: 30px 35px;
            margin-bottom: 35px;
            display: flex;
            align-items: center;
            gap: 25px;
            box-shadow: 0 15px 50px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 15px 50px rgba(40, 167, 69, 0.2);
        }

        .alert-panel.info {
            background: linear-gradient(135deg, #d1ecf1, #bee5eb);
            border-left-color: #17a2b8;
            box-shadow: 0 15px 50px rgba(23, 162, 184, 0.2);
        }

        .alert-icon {
            font-size: 3rem;
            color: #856404;
        }

        .alert-panel.success .alert-icon { color: #155724; }
        .alert-panel.info .alert-icon { color: #0c5460; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 10px;
            color: #856404;
        }

        .alert-panel.success .alert-title { color: #155724; }
        .alert-panel.info .alert-title { color: #0c5460; }

        .alert-message {
            font-size: 1.1rem;
            line-height: 1.7;
            color: #856404;
        }

        .alert-panel.success .alert-message { color: #155724; }
        .alert-panel.info .alert-message { color: #0c5460; }

        /* 响应式设计 */
        @media (max-width: 1600px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .dispatch-container {
                padding: 15px;
            }

            .main-title {
                font-size: 3rem;
                flex-direction: column;
                gap: 25px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
                padding: 20px 30px;
                flex-direction: row;
                justify-content: center;
            }

            .optimization-grid {
                grid-template-columns: 1fr;
            }

            .system-overview {
                grid-template-columns: repeat(2, 1fr);
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 1.2s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(50px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 1s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-50px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 300px;
            color: #1e3c72;
            font-size: 1.3rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 15px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="dispatch-container">
        <!-- 系统头部 -->
        <div class="dispatch-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-cogs"></i>
                水泵优化组合调度系统
                <i class="fas fa-chart-line"></i>
            </h1>
            <p class="subtitle">Pump Optimization & Dispatch System - 智能优化 · 节能降耗 · 精准调度 · 高效运行</p>
            
            <div class="system-overview">
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-water"></i>
                    </div>
                    <div class="overview-title">运行水泵</div>
                    <div class="overview-value">8</div>
                    <div class="overview-desc">台运行中</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="overview-title">节能效率</div>
                    <div class="overview-value">92.5%</div>
                    <div class="overview-desc">优化效果</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="overview-title">调度指令</div>
                    <div class="overview-value">12</div>
                    <div class="overview-desc">条待执行</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-bolt"></i>
                    </div>
                    <div class="overview-title">能耗降低</div>
                    <div class="overview-value">15.8%</div>
                    <div class="overview-desc">较上月</div>
                </div>
                <div class="overview-item">
                    <div class="overview-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="overview-title">供水效率</div>
                    <div class="overview-value">96.2%</div>
                    <div class="overview-desc">满足需求</div>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="optimization-analysis">
                <i class="nav-icon fas fa-chart-line"></i>
                <span>优化分析</span>
            </button>
            <button class="nav-tab" data-tab="pump-combination">
                <i class="nav-icon fas fa-layer-group"></i>
                <span>组合方案</span>
            </button>
            <button class="nav-tab" data-tab="dispatch-instruction">
                <i class="nav-icon fas fa-clipboard-list"></i>
                <span>调度指令</span>
            </button>
            <button class="nav-tab" data-tab="energy-analysis">
                <i class="nav-icon fas fa-leaf"></i>
                <span>节能分析</span>
            </button>
            <button class="nav-tab" data-tab="schedule-plan">
                <i class="nav-icon fas fa-calendar-alt"></i>
                <span>调度计划</span>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 优化分析 -->
            <div id="optimization-analysis" class="tab-content active">
                <h2 class="module-title">
                    <i class="fas fa-chart-line"></i>
                    水泵优化分析
                </h2>

                <!-- 优化状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">优化运行状态良好</div>
                        <div class="alert-message">当前水泵组合运行效率96.2%，节能效果显著，较传统调度方式节能15.8%，满足供水需求的同时实现了能源效率最大化</div>
                    </div>
                </div>

                <!-- 优化指标 -->
                <div class="optimization-grid">
                    <div class="optimization-card efficiency">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-tachometer-alt"></i>
                                运行效率
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-check-circle"></i>
                                最优
                            </div>
                        </div>
                        <div class="card-value">
                            96.2
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            当前水泵组合运行效率达到96.2%，处于最优状态
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 96.2%;"></div>
                        </div>
                        <div class="progress-text">目标: 95% | 当前: 96.2%</div>
                    </div>

                    <div class="optimization-card combination">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-layer-group"></i>
                                组合优化度
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-star"></i>
                                优秀
                            </div>
                        </div>
                        <div class="card-value">
                            92.5
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            水泵组合方案优化度92.5%，匹配度高效合理
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 92.5%;"></div>
                        </div>
                        <div class="progress-text">目标: 90% | 当前: 92.5%</div>
                    </div>

                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-clock"></i>
                                调度频次
                            </div>
                            <div class="card-status status-good">
                                <i class="fas fa-thumbs-up"></i>
                                良好
                            </div>
                        </div>
                        <div class="card-value">
                            8.5
                            <span class="card-unit">次/日</span>
                        </div>
                        <div class="card-description">
                            日均调度频次8.5次，较上月减少2.3次，调度更加稳定
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 75%;"></div>
                        </div>
                        <div class="progress-text">目标: ≤10次 | 当前: 8.5次</div>
                    </div>

                    <div class="optimization-card energy">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-leaf"></i>
                                节能效果
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-award"></i>
                                卓越
                            </div>
                        </div>
                        <div class="card-value">
                            15.8
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            较传统调度方式节能15.8%，月节约电费3.2万元
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 88%;"></div>
                        </div>
                        <div class="progress-text">目标: 12% | 当前: 15.8%</div>
                    </div>
                </div>

                <!-- 优化趋势图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                优化效果趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchOptimizationPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchOptimizationPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchOptimizationPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="optimizationTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                能耗分布分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="energyDistributionChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 水泵运行状态表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>水泵编号</th>
                            <th>水泵位置</th>
                            <th>运行状态</th>
                            <th>当前流量</th>
                            <th>出口压力</th>
                            <th>功率消耗</th>
                            <th>运行效率</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>PUMP-001</td>
                            <td>东区水厂1号泵房</td>
                            <td class="status-running">运行中</td>
                            <td class="number">1,850 m³/h</td>
                            <td class="number">0.62 MPa</td>
                            <td class="number">185 kW</td>
                            <td class="number">96.5%</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>PUMP-002</td>
                            <td>东区水厂2号泵房</td>
                            <td class="status-running">运行中</td>
                            <td class="number">1,920 m³/h</td>
                            <td class="number">0.65 MPa</td>
                            <td class="number">192 kW</td>
                            <td class="number">95.8%</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>PUMP-003</td>
                            <td>西区水厂1号泵房</td>
                            <td class="status-standby">待机</td>
                            <td class="number">0 m³/h</td>
                            <td class="number">0.00 MPa</td>
                            <td class="number">5 kW</td>
                            <td class="number">-</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>PUMP-004</td>
                            <td>西区水厂2号泵房</td>
                            <td class="status-running">运行中</td>
                            <td class="number">1,680 m³/h</td>
                            <td class="number">0.58 MPa</td>
                            <td class="number">168 kW</td>
                            <td class="number">97.2%</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>PUMP-005</td>
                            <td>南区水厂1号泵房</td>
                            <td class="status-maintenance">维护中</td>
                            <td class="number">0 m³/h</td>
                            <td class="number">0.00 MPa</td>
                            <td class="number">0 kW</td>
                            <td class="number">-</td>
                            <td><button class="btn btn-primary" style="padding: 10px 20px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 组合方案 -->
            <div id="pump-combination" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-layer-group"></i>
                    水泵组合方案
                </h2>

                <!-- 组合状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">最优组合方案已生成</div>
                        <div class="alert-message">系统已为当前供水需求生成最优水泵组合方案，预计节能15.8%，满足96.2%的供水效率要求</div>
                    </div>
                </div>

                <!-- 组合方案卡片 -->
                <div class="optimization-grid">
                    <div class="optimization-card combination">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-star"></i>
                                推荐方案A
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-crown"></i>
                                最优
                            </div>
                        </div>
                        <div class="card-value">
                            8
                            <span class="card-unit">台运行</span>
                        </div>
                        <div class="card-description">
                            运行水泵：PUMP-001,002,004,006,007,008,010,012<br>
                            预计节能：15.8% | 供水效率：96.2%
                        </div>
                        <div class="btn-group" style="margin-top: 20px;">
                            <button class="btn btn-success" onclick="applyPumpCombination('A')">
                                <i class="fas fa-play"></i>
                                应用方案
                            </button>
                        </div>
                    </div>

                    <div class="optimization-card combination">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-medal"></i>
                                备选方案B
                            </div>
                            <div class="card-status status-good">
                                <i class="fas fa-thumbs-up"></i>
                                良好
                            </div>
                        </div>
                        <div class="card-value">
                            9
                            <span class="card-unit">台运行</span>
                        </div>
                        <div class="card-description">
                            运行水泵：PUMP-001,002,003,004,006,007,008,010,012<br>
                            预计节能：12.5% | 供水效率：97.8%
                        </div>
                        <div class="btn-group" style="margin-top: 20px;">
                            <button class="btn btn-primary" onclick="applyPumpCombination('B')">
                                <i class="fas fa-play"></i>
                                应用方案
                            </button>
                        </div>
                    </div>

                    <div class="optimization-card combination">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-shield-alt"></i>
                                安全方案C
                            </div>
                            <div class="card-status status-normal">
                                <i class="fas fa-check"></i>
                                稳定
                            </div>
                        </div>
                        <div class="card-value">
                            10
                            <span class="card-unit">台运行</span>
                        </div>
                        <div class="card-description">
                            运行水泵：全部水泵低负荷运行<br>
                            预计节能：8.2% | 供水效率：99.5%
                        </div>
                        <div class="btn-group" style="margin-top: 20px;">
                            <button class="btn btn-warning" onclick="applyPumpCombination('C')">
                                <i class="fas fa-play"></i>
                                应用方案
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 组合对比表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>方案名称</th>
                            <th>运行水泵数量</th>
                            <th>总功率消耗</th>
                            <th>供水效率</th>
                            <th>节能效果</th>
                            <th>稳定性评分</th>
                            <th>推荐指数</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr style="background: rgba(76, 175, 80, 0.1);">
                            <td><strong>推荐方案A</strong></td>
                            <td class="number">8台</td>
                            <td class="number">1,485 kW</td>
                            <td class="number">96.2%</td>
                            <td class="number status-running">15.8%</td>
                            <td class="number">9.2分</td>
                            <td class="number">★★★★★</td>
                            <td><button class="btn btn-success" style="padding: 8px 16px; font-size: 0.9rem;">应用</button></td>
                        </tr>
                        <tr>
                            <td>备选方案B</td>
                            <td class="number">9台</td>
                            <td class="number">1,620 kW</td>
                            <td class="number">97.8%</td>
                            <td class="number status-running">12.5%</td>
                            <td class="number">8.8分</td>
                            <td class="number">★★★★☆</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">应用</button></td>
                        </tr>
                        <tr>
                            <td>安全方案C</td>
                            <td class="number">10台</td>
                            <td class="number">1,750 kW</td>
                            <td class="number">99.5%</td>
                            <td class="number status-standby">8.2%</td>
                            <td class="number">9.8分</td>
                            <td class="number">★★★☆☆</td>
                            <td><button class="btn btn-warning" style="padding: 8px 16px; font-size: 0.9rem;">应用</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 调度指令 -->
            <div id="dispatch-instruction" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-clipboard-list"></i>
                    调度指令管理
                </h2>

                <!-- 指令状态 -->
                <div class="alert-panel info">
                    <i class="fas fa-info-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">调度指令已生成</div>
                        <div class="alert-message">系统已生成12条调度指令，包括开停泵指令和调度时间方案，调度人员可根据指令执行相应操作</div>
                    </div>
                </div>

                <!-- 指令统计 -->
                <div class="optimization-grid">
                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-list"></i>
                                待执行指令
                            </div>
                            <div class="card-status status-normal">
                                <i class="fas fa-clock"></i>
                                待处理
                            </div>
                        </div>
                        <div class="card-value">
                            12
                            <span class="card-unit">条</span>
                        </div>
                        <div class="card-description">
                            开泵指令5条，停泵指令4条，调节指令3条
                        </div>
                    </div>

                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-check-circle"></i>
                                已执行指令
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-check"></i>
                                完成
                            </div>
                        </div>
                        <div class="card-value">
                            28
                            <span class="card-unit">条</span>
                        </div>
                        <div class="card-description">
                            今日已执行28条指令，执行成功率100%
                        </div>
                    </div>

                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-clock"></i>
                                平均执行时间
                            </div>
                            <div class="card-status status-good">
                                <i class="fas fa-thumbs-up"></i>
                                良好
                            </div>
                        </div>
                        <div class="card-value">
                            3.5
                            <span class="card-unit">分钟</span>
                        </div>
                        <div class="card-description">
                            指令平均执行时间3.5分钟，响应迅速高效
                        </div>
                    </div>
                </div>

                <!-- 调度指令表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>指令编号</th>
                            <th>指令类型</th>
                            <th>目标设备</th>
                            <th>执行时间</th>
                            <th>指令内容</th>
                            <th>优先级</th>
                            <th>执行状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>CMD-2024-001</td>
                            <td class="status-running">开泵指令</td>
                            <td>PUMP-003</td>
                            <td>16:00:00</td>
                            <td>启动西区水厂1号泵</td>
                            <td class="status-running">高</td>
                            <td class="status-standby">待执行</td>
                            <td><button class="btn btn-success" style="padding: 8px 16px; font-size: 0.9rem;">立即执行</button></td>
                        </tr>
                        <tr>
                            <td>CMD-2024-002</td>
                            <td class="status-stopped">停泵指令</td>
                            <td>PUMP-009</td>
                            <td>16:30:00</td>
                            <td>停止北区水厂2号泵</td>
                            <td class="status-standby">中</td>
                            <td class="status-standby">待执行</td>
                            <td><button class="btn btn-danger" style="padding: 8px 16px; font-size: 0.9rem;">立即执行</button></td>
                        </tr>
                        <tr>
                            <td>CMD-2024-003</td>
                            <td class="status-standby">调节指令</td>
                            <td>PUMP-001</td>
                            <td>17:00:00</td>
                            <td>调节转速至1450rpm</td>
                            <td class="status-standby">中</td>
                            <td class="status-standby">待执行</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">立即执行</button></td>
                        </tr>
                        <tr>
                            <td>CMD-2024-004</td>
                            <td class="status-running">开泵指令</td>
                            <td>PUMP-011</td>
                            <td>18:00:00</td>
                            <td>启动南区水厂3号泵</td>
                            <td class="status-running">高</td>
                            <td class="status-standby">待执行</td>
                            <td><button class="btn btn-success" style="padding: 8px 16px; font-size: 0.9rem;">立即执行</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 节能分析 -->
            <div id="energy-analysis" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-leaf"></i>
                    节能降耗分析
                </h2>

                <!-- 节能状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-leaf alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">节能效果显著</div>
                        <div class="alert-message">通过优化调度，本月累计节能15.8%，节约电费3.2万元，减少碳排放8.5吨，节能降耗效果显著</div>
                    </div>
                </div>

                <!-- 节能指标 -->
                <div class="optimization-grid">
                    <div class="optimization-card energy">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-bolt"></i>
                                节电量
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-award"></i>
                                优秀
                            </div>
                        </div>
                        <div class="card-value">
                            15.8
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            较传统调度方式节电15.8%，月节约用电12.5万kWh
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 88%;"></div>
                        </div>
                        <div class="progress-text">目标: 12% | 当前: 15.8%</div>
                    </div>

                    <div class="optimization-card energy">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-dollar-sign"></i>
                                节约费用
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-coins"></i>
                                显著
                            </div>
                        </div>
                        <div class="card-value">
                            3.2
                            <span class="card-unit">万元</span>
                        </div>
                        <div class="card-description">
                            月节约电费3.2万元，年预计节约38.4万元
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 85%;"></div>
                        </div>
                        <div class="progress-text">目标: 2.5万 | 当前: 3.2万</div>
                    </div>

                    <div class="optimization-card energy">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-seedling"></i>
                                碳减排量
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-leaf"></i>
                                环保
                            </div>
                        </div>
                        <div class="card-value">
                            8.5
                            <span class="card-unit">吨CO₂</span>
                        </div>
                        <div class="card-description">
                            月减少碳排放8.5吨，年预计减排102吨CO₂
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 92%;"></div>
                        </div>
                        <div class="progress-text">目标: 7吨 | 当前: 8.5吨</div>
                    </div>

                    <div class="optimization-card energy">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i>
                                效率提升
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-rocket"></i>
                                卓越
                            </div>
                        </div>
                        <div class="card-value">
                            12.3
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            系统整体效率提升12.3%，运行更加稳定高效
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 95%;"></div>
                        </div>
                        <div class="progress-text">目标: 10% | 当前: 12.3%</div>
                    </div>
                </div>

                <!-- 节能趋势图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                节能效果趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchEnergyPeriod('month')">月度</button>
                                <button class="chart-btn" onclick="switchEnergyPeriod('quarter')">季度</button>
                                <button class="chart-btn" onclick="switchEnergyPeriod('year')">年度</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="energyTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-bar"></i>
                                节能贡献分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="energyContributionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 调度计划 -->
            <div id="schedule-plan" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-calendar-alt"></i>
                    调度计划管理
                </h2>

                <!-- 计划状态 -->
                <div class="alert-panel info">
                    <i class="fas fa-calendar-check alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">调度计划已制定</div>
                        <div class="alert-message">系统已制定未来24小时的水泵调度计划，包含18个调度节点，确保供水需求的同时实现最优节能效果</div>
                    </div>
                </div>

                <!-- 计划统计 -->
                <div class="optimization-grid">
                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-calendar-day"></i>
                                今日计划
                            </div>
                            <div class="card-status status-good">
                                <i class="fas fa-check-circle"></i>
                                正常
                            </div>
                        </div>
                        <div class="card-value">
                            18
                            <span class="card-unit">个节点</span>
                        </div>
                        <div class="card-description">
                            24小时调度计划，18个调度节点，平均1.3小时一次调度
                        </div>
                    </div>

                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-clock"></i>
                                执行进度
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-thumbs-up"></i>
                                良好
                            </div>
                        </div>
                        <div class="card-value">
                            67
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            已执行12个节点，剩余6个节点，执行进度良好
                        </div>
                        <div class="card-progress">
                            <div class="progress-bar" style="width: 67%;"></div>
                        </div>
                        <div class="progress-text">已完成: 12/18 节点</div>
                    </div>

                    <div class="optimization-card schedule">
                        <div class="card-header">
                            <div class="card-title">
                                <i class="fas fa-chart-line"></i>
                                预期效果
                            </div>
                            <div class="card-status status-optimal">
                                <i class="fas fa-star"></i>
                                优秀
                            </div>
                        </div>
                        <div class="card-value">
                            14.5
                            <span class="card-unit">%</span>
                        </div>
                        <div class="card-description">
                            预期节能效果14.5%，供水效率保持在96%以上
                        </div>
                    </div>
                </div>

                <!-- 调度计划时间轴 -->
                <div style="background: white; border-radius: 20px; padding: 30px; margin-bottom: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                    <h3 style="margin-bottom: 25px; color: #1e3c72;">
                        <i class="fas fa-timeline"></i>
                        24小时调度时间轴
                    </h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px;">
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                            <div style="font-weight: 700; color: #2e7d32;">06:00 - 高峰启动</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">启动PUMP-003,005,011</div>
                            <div style="font-size: 0.8rem; color: #4caf50; margin-top: 5px;">✓ 已执行</div>
                        </div>
                        <div style="background: #e8f5e8; padding: 15px; border-radius: 10px; border-left: 4px solid #4caf50;">
                            <div style="font-weight: 700; color: #2e7d32;">08:00 - 负荷调节</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">调节PUMP-001转速</div>
                            <div style="font-size: 0.8rem; color: #4caf50; margin-top: 5px;">✓ 已执行</div>
                        </div>
                        <div style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                            <div style="font-weight: 700; color: #ef6c00;">16:00 - 即将执行</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">启动PUMP-003</div>
                            <div style="font-size: 0.8rem; color: #ff9800; margin-top: 5px;">⏳ 待执行</div>
                        </div>
                        <div style="background: #f5f5f5; padding: 15px; border-radius: 10px; border-left: 4px solid #9e9e9e;">
                            <div style="font-weight: 700; color: #666;">22:00 - 夜间优化</div>
                            <div style="font-size: 0.9rem; color: #666; margin-top: 5px;">停止PUMP-009,010</div>
                            <div style="font-size: 0.8rem; color: #9e9e9e; margin-top: 5px;">⏰ 计划中</div>
                        </div>
                    </div>
                </div>

                <!-- 调度计划表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>时间节点</th>
                            <th>调度类型</th>
                            <th>涉及设备</th>
                            <th>调度内容</th>
                            <th>预期效果</th>
                            <th>执行状态</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>16:00</td>
                            <td class="status-running">开泵调度</td>
                            <td>PUMP-003</td>
                            <td>启动西区水厂1号泵</td>
                            <td>提升供水能力15%</td>
                            <td class="status-standby">待执行</td>
                            <td>应对晚高峰用水</td>
                        </tr>
                        <tr>
                            <td>18:00</td>
                            <td class="status-standby">调节调度</td>
                            <td>PUMP-001,002</td>
                            <td>调节转速至1500rpm</td>
                            <td>优化运行效率</td>
                            <td class="status-standby">待执行</td>
                            <td>高峰期优化</td>
                        </tr>
                        <tr>
                            <td>22:00</td>
                            <td class="status-stopped">停泵调度</td>
                            <td>PUMP-009,010</td>
                            <td>停止北区备用泵</td>
                            <td>节能8%</td>
                            <td class="status-maintenance">计划中</td>
                            <td>夜间节能运行</td>
                        </tr>
                        <tr>
                            <td>02:00</td>
                            <td class="status-standby">维护调度</td>
                            <td>PUMP-005</td>
                            <td>切换至维护模式</td>
                            <td>设备保养</td>
                            <td class="status-maintenance">计划中</td>
                            <td>定期维护保养</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'optimization-analysis';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('水泵优化组合调度系统初始化...');
            
            // 初始化导航事件
            initNavEvents();
            
            // 初始化优化分析图表
            initOptimizationCharts();
            
            console.log('系统初始化完成');
        });

        // 初始化导航事件
        function initNavEvents() {
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            } else {
                // 如果内容不存在，动态加载
                loadTabContent(tabId);
            }
            
            // 激活选中导航
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
        }

        // 动态加载标签页内容
        function loadTabContent(tabId) {
            const contentArea = document.querySelector('.content-area');
            
            // 显示加载动画
            contentArea.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    正在加载${getTabName(tabId)}模块...
                </div>
            `;
            
            // 模拟加载延迟
            setTimeout(() => {
                generateTabContent(tabId);
            }, 1000);
        }

        // 获取标签页名称
        function getTabName(tabId) {
            const names = {
                'optimization-analysis': '优化分析',
                'pump-combination': '组合方案',
                'dispatch-instruction': '调度指令',
                'energy-analysis': '节能分析',
                'schedule-plan': '调度计划'
            };
            return names[tabId] || '未知模块';
        }

        // 生成标签页内容
        function generateTabContent(tabId) {
            // 内容将在后续添加，这里先显示占位内容
            const contentArea = document.querySelector('.content-area');
            contentArea.innerHTML = `
                <div id="${tabId}" class="tab-content active">
                    <h2 class="module-title">
                        <i class="fas fa-cogs"></i>
                        ${getTabName(tabId)}模块
                    </h2>
                    <div class="alert-panel info">
                        <i class="fas fa-info-circle alert-icon"></i>
                        <div class="alert-content">
                            <div class="alert-title">模块加载成功</div>
                            <div class="alert-message">${getTabName(tabId)}模块已成功加载，详细功能正在开发中！</div>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="showNotification('功能开发中...', 'info')">
                            <i class="fas fa-cogs"></i>
                            功能配置
                        </button>
                        <button class="btn btn-success" onclick="showNotification('优化分析功能开发中...', 'info')">
                            <i class="fas fa-chart-bar"></i>
                            优化分析
                        </button>
                        <button class="btn btn-warning" onclick="showNotification('报告导出功能开发中...', 'info')">
                            <i class="fas fa-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>
            `;
        }

        // 初始化优化分析图表
        function initOptimizationCharts() {
            initOptimizationTrendChart();
            initEnergyDistributionChart();
        }

        // 优化趋势图表
        function initOptimizationTrendChart() {
            const ctx = document.getElementById('optimizationTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            charts.optimizationTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '运行效率 (%)',
                        data: [94.5, 95.2, 96.8, 97.2, 96.5, 95.8, 96.2],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '节能效果 (%)',
                        data: [12.5, 13.2, 15.8, 16.2, 15.5, 14.8, 15.8],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }, {
                        label: '调度频次 (次)',
                        data: [2, 1, 3, 2, 1, 2, 1],
                        borderColor: '#ff9800',
                        backgroundColor: 'rgba(255, 152, 0, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y2'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '效率 (%)' },
                            min: 90,
                            max: 100
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '节能效果 (%)' },
                            grid: { drawOnChartArea: false },
                            min: 10,
                            max: 20
                        },
                        y2: {
                            type: 'linear',
                            display: false,
                            position: 'right'
                        }
                    }
                }
            });
        }

        // 能耗分布图表
        function initEnergyDistributionChart() {
            const ctx = document.getElementById('energyDistributionChart');
            if (!ctx || Chart.getChart(ctx)) return;
            
            charts.energyDistribution = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['主力水泵', '辅助水泵', '备用水泵', '系统损耗', '其他设备'],
                    datasets: [{
                        data: [65, 20, 5, 8, 2],
                        backgroundColor: [
                            '#2196f3',
                            '#4caf50',
                            '#ff9800',
                            '#f44336',
                            '#9c27b0'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 优化周期切换
        function switchOptimizationPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const periodNames = {
                '24h': '24小时',
                '7d': '7天',
                '30d': '30天'
            };
            
            showNotification(`已切换到${periodNames[period]}优化分析视图`, 'info');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 18px 25px;
                border-radius: 15px;
                z-index: 10000;
                box-shadow: 0 12px 35px rgba(0,0,0,0.15);
                max-width: 400px;
                animation: slideInRight 0.5s ease-out;
                font-weight: 700;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 12px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.5s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 500);
            }, 4000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
