<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>水质化验数据管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .main-title {
            font-size: 2.2rem;
            color: #2c3e50;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 25px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            gap: 5px;
        }

        .nav-tab {
            flex: 1;
            padding: 15px 20px;
            background: transparent;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            min-height: 600px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-label .required {
            color: #e74c3c;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        /* 水质数据网格 */
        .water-quality-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
        }

        .quality-item {
            display: flex;
            flex-direction: column;
        }

        .quality-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
            font-size: 0.9rem;
        }

        .quality-input {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 0.95rem;
        }

        .quality-unit {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 2px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f093fb, #f5576c);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff416c, #ff4b2b);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .status-draft {
            background: #fff3cd;
            color: #856404;
        }

        .status-submitted {
            background: #cce5ff;
            color: #004085;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        /* 搜索过滤区域 */
        .filter-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }

        .filter-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }

        /* 详情模态框 */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 1000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: white;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 25px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-body {
            padding: 25px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .modal-close {
            background: none;
            border: none;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 审批流程 */
        .approval-flow {
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 20px 0;
            gap: 20px;
        }

        .flow-step {
            display: flex;
            flex-direction: column;
            align-items: center;
            position: relative;
        }

        .flow-step::after {
            content: '';
            position: absolute;
            top: 20px;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #dee2e6;
        }

        .flow-step:last-child::after {
            display: none;
        }

        .flow-step.completed::after {
            background: #28a745;
        }

        .flow-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            font-size: 1.2rem;
        }

        .flow-step.completed .flow-icon {
            background: #28a745;
            color: white;
        }

        .flow-step.current .flow-icon {
            background: #007bff;
            color: white;
        }

        .flow-step.pending .flow-icon {
            background: #dee2e6;
            color: #6c757d;
        }

        .flow-label {
            font-size: 0.9rem;
            color: #495057;
            text-align: center;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 1.8rem;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .water-quality-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .filter-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }

            .data-table {
                font-size: 0.9rem;
            }

            .data-table th,
            .data-table td {
                padding: 10px 8px;
            }
        }

        /* 动画效果 */
        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1 class="main-title">
                <i class="fas fa-flask"></i>
                水质化验数据管理系统
            </h1>
            <p class="subtitle">Water Quality Laboratory Data Management System</p>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" onclick="showTab('data-entry')">
                <i class="fas fa-plus-circle"></i>
                数据录入
            </button>
            <button class="nav-tab" onclick="showTab('data-browse')">
                <i class="fas fa-search"></i>
                数据浏览
            </button>
            <button class="nav-tab" onclick="showTab('approval-flow')">
                <i class="fas fa-clipboard-check"></i>
                流程审批
            </button>
            <button class="nav-tab" onclick="showTab('report-export')">
                <i class="fas fa-file-export"></i>
                报表导出
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 数据录入标签页 -->
            <div id="data-entry" class="tab-content active">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-edit"></i>
                    水质化验数据录入
                </h2>

                <form id="waterQualityForm">
                    <!-- 基本信息 -->
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-barcode"></i>
                                化验编号 <span class="required">*</span>
                            </label>
                            <input type="text" class="form-input" id="testNumber" placeholder="自动生成：WQ20240115001" readonly>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-calendar-alt"></i>
                                化验时间 <span class="required">*</span>
                            </label>
                            <input type="datetime-local" class="form-input" id="testTime" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-map-marker-alt"></i>
                                化验地点 <span class="required">*</span>
                            </label>
                            <select class="form-select" id="testLocation" required>
                                <option value="">请选择化验地点</option>
                                <option value="进水口">进水口</option>
                                <option value="沉淀池">沉淀池</option>
                                <option value="过滤池">过滤池</option>
                                <option value="出水口">出水口</option>
                                <option value="回用水池">回用水池</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-user"></i>
                                化验人员 <span class="required">*</span>
                            </label>
                            <select class="form-select" id="testPersonnel" required>
                                <option value="">请选择化验人员</option>
                                <option value="张三">张三</option>
                                <option value="李四">李四</option>
                                <option value="王五">王五</option>
                                <option value="赵六">赵六</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-file-alt"></i>
                            化验描述
                        </label>
                        <textarea class="form-textarea" id="testDescription" placeholder="请输入化验描述信息..."></textarea>
                    </div>

                    <!-- 水质数据 -->
                    <h3 style="color: #495057; margin: 25px 0 15px 0; display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-tint"></i>
                        水质检测数据
                    </h3>

                    <div class="water-quality-grid">
                        <div class="quality-item">
                            <label class="quality-label">温度 <span class="required">*</span></label>
                            <input type="number" class="quality-input" id="temperature" step="0.1" placeholder="25.0" required>
                            <span class="quality-unit">°C</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">色度</label>
                            <input type="number" class="quality-input" id="chromaticity" step="1" placeholder="15">
                            <span class="quality-unit">度</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">PH值 <span class="required">*</span></label>
                            <input type="number" class="quality-input" id="phValue" step="0.01" placeholder="7.2" required>
                            <span class="quality-unit">pH</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">悬浮物(SS)</label>
                            <input type="number" class="quality-input" id="suspendedSolids" step="0.1" placeholder="10.5">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">总磷(TP)</label>
                            <input type="number" class="quality-input" id="totalPhosphorus" step="0.01" placeholder="0.5">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">总氮(TN)</label>
                            <input type="number" class="quality-input" id="totalNitrogen" step="0.01" placeholder="15.2">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">化学需氧量(COD)</label>
                            <input type="number" class="quality-input" id="codValue" step="0.1" placeholder="50.0">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">生化需氧量(BOD)</label>
                            <input type="number" class="quality-input" id="bodValue" step="0.1" placeholder="20.0">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">溶解氧(DO)</label>
                            <input type="number" class="quality-input" id="dissolvedOxygen" step="0.1" placeholder="6.5">
                            <span class="quality-unit">mg/L</span>
                        </div>

                        <div class="quality-item">
                            <label class="quality-label">氨氮(NH3-N)</label>
                            <input type="number" class="quality-input" id="ammoniaNitrogen" step="0.01" placeholder="1.2">
                            <span class="quality-unit">mg/L</span>
                        </div>
                    </div>

                    <div class="btn-group">
                        <button type="button" class="btn btn-primary" onclick="saveData()">
                            <i class="fas fa-save"></i>
                            保存数据
                        </button>
                        <button type="button" class="btn btn-success" onclick="submitForApproval()">
                            <i class="fas fa-paper-plane"></i>
                            提交审批
                        </button>
                        <button type="reset" class="btn btn-secondary">
                            <i class="fas fa-undo"></i>
                            重置表单
                        </button>
                    </div>
                </form>
            </div>

            <!-- 数据浏览标签页 -->
            <div id="data-browse" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-search"></i>
                    水质化验数据浏览
                </h2>

                <!-- 搜索过滤区域 -->
                <div class="filter-section">
                    <h3 style="margin-bottom: 15px; color: #495057;">
                        <i class="fas fa-filter"></i>
                        数据筛选
                    </h3>
                    <div class="filter-grid">
                        <div class="form-group">
                            <label class="form-label">化验编号</label>
                            <input type="text" class="form-input" id="filterTestNumber" placeholder="输入化验编号">
                        </div>
                        <div class="form-group">
                            <label class="form-label">化验地点</label>
                            <select class="form-select" id="filterLocation">
                                <option value="">全部地点</option>
                                <option value="进水口">进水口</option>
                                <option value="沉淀池">沉淀池</option>
                                <option value="过滤池">过滤池</option>
                                <option value="出水口">出水口</option>
                                <option value="回用水池">回用水池</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">化验人员</label>
                            <select class="form-select" id="filterPersonnel">
                                <option value="">全部人员</option>
                                <option value="张三">张三</option>
                                <option value="李四">李四</option>
                                <option value="王五">王五</option>
                                <option value="赵六">赵六</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">审批状态</label>
                            <select class="form-select" id="filterStatus">
                                <option value="">全部状态</option>
                                <option value="draft">草稿</option>
                                <option value="submitted">已提交</option>
                                <option value="approved">已审批</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">开始时间</label>
                            <input type="date" class="form-input" id="filterStartDate">
                        </div>
                        <div class="form-group">
                            <label class="form-label">结束时间</label>
                            <input type="date" class="form-input" id="filterEndDate">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="searchData()">
                                <i class="fas fa-search"></i>
                                搜索
                            </button>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-secondary" onclick="resetFilter()">
                                <i class="fas fa-undo"></i>
                                重置
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 数据表格 -->
                <div style="overflow-x: auto;">
                    <table class="data-table" id="dataTable">
                        <thead>
                            <tr>
                                <th>化验编号</th>
                                <th>化验时间</th>
                                <th>化验地点</th>
                                <th>化验人员</th>
                                <th>温度(°C)</th>
                                <th>PH值</th>
                                <th>审批状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="dataTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 流程审批标签页 -->
            <div id="approval-flow" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-clipboard-check"></i>
                    流程审批管理
                </h2>

                <!-- 待审批数据 -->
                <div style="margin-bottom: 30px;">
                    <h3 style="color: #495057; margin-bottom: 15px;">
                        <i class="fas fa-clock"></i>
                        待审批化验单
                    </h3>
                    <div style="overflow-x: auto;">
                        <table class="data-table" id="approvalTable">
                            <thead>
                                <tr>
                                    <th>化验编号</th>
                                    <th>化验时间</th>
                                    <th>化验地点</th>
                                    <th>化验人员</th>
                                    <th>提交时间</th>
                                    <th>当前状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="approvalTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 审批历史 -->
                <div>
                    <h3 style="color: #495057; margin-bottom: 15px;">
                        <i class="fas fa-history"></i>
                        审批历史记录
                    </h3>
                    <div style="overflow-x: auto;">
                        <table class="data-table" id="historyTable">
                            <thead>
                                <tr>
                                    <th>化验编号</th>
                                    <th>化验人员</th>
                                    <th>审批人</th>
                                    <th>审批时间</th>
                                    <th>审批结果</th>
                                    <th>审批意见</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- 数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 报表导出标签页 -->
            <div id="report-export" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 10px;">
                    <i class="fas fa-file-export"></i>
                    报表导出
                </h2>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 30px;">
                    <!-- 报表生成 -->
                    <div>
                        <h3 style="color: #495057; margin-bottom: 20px;">
                            <i class="fas fa-chart-bar"></i>
                            报表生成设置
                        </h3>

                        <div class="form-group">
                            <label class="form-label">报表类型</label>
                            <select class="form-select" id="reportType">
                                <option value="daily">日报表</option>
                                <option value="weekly">周报表</option>
                                <option value="monthly">月报表</option>
                                <option value="custom">自定义时间段</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label class="form-label">化验地点</label>
                            <select class="form-select" id="reportLocation">
                                <option value="">全部地点</option>
                                <option value="进水口">进水口</option>
                                <option value="沉淀池">沉淀池</option>
                                <option value="过滤池">过滤池</option>
                                <option value="出水口">出水口</option>
                                <option value="回用水池">回用水池</option>
                            </select>
                        </div>

                        <div class="form-group" id="customDateRange" style="display: none;">
                            <label class="form-label">自定义时间段</label>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
                                <input type="date" class="form-input" id="reportStartDate">
                                <input type="date" class="form-input" id="reportEndDate">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">导出格式</label>
                            <select class="form-select" id="exportFormat">
                                <option value="excel">Excel格式</option>
                                <option value="pdf">PDF格式</option>
                                <option value="csv">CSV格式</option>
                            </select>
                        </div>

                        <div class="btn-group">
                            <button class="btn btn-primary" onclick="generateReport()">
                                <i class="fas fa-file-alt"></i>
                                生成报表
                            </button>
                            <button class="btn btn-success" onclick="exportReport()">
                                <i class="fas fa-download"></i>
                                导出报表
                            </button>
                        </div>
                    </div>

                    <!-- 统计信息 -->
                    <div>
                        <h3 style="color: #495057; margin-bottom: 20px;">
                            <i class="fas fa-chart-pie"></i>
                            数据统计概览
                        </h3>

                        <div style="display: grid; gap: 15px;">
                            <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; border-left: 4px solid #2196f3;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-size: 0.9rem; color: #666;">总化验单数</div>
                                        <div style="font-size: 1.8rem; font-weight: bold; color: #2196f3;" id="totalCount">0</div>
                                    </div>
                                    <i class="fas fa-flask" style="font-size: 2rem; color: #2196f3; opacity: 0.7;"></i>
                                </div>
                            </div>

                            <div style="background: #e8f5e8; padding: 20px; border-radius: 10px; border-left: 4px solid #4caf50;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-size: 0.9rem; color: #666;">已审批通过</div>
                                        <div style="font-size: 1.8rem; font-weight: bold; color: #4caf50;" id="approvedCount">0</div>
                                    </div>
                                    <i class="fas fa-check-circle" style="font-size: 2rem; color: #4caf50; opacity: 0.7;"></i>
                                </div>
                            </div>

                            <div style="background: #fff3e0; padding: 20px; border-radius: 10px; border-left: 4px solid #ff9800;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-size: 0.9rem; color: #666;">待审批</div>
                                        <div style="font-size: 1.8rem; font-weight: bold; color: #ff9800;" id="pendingCount">0</div>
                                    </div>
                                    <i class="fas fa-clock" style="font-size: 2rem; color: #ff9800; opacity: 0.7;"></i>
                                </div>
                            </div>

                            <div style="background: #ffebee; padding: 20px; border-radius: 10px; border-left: 4px solid #f44336;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <div>
                                        <div style="font-size: 0.9rem; color: #666;">审批拒绝</div>
                                        <div style="font-size: 1.8rem; font-weight: bold; color: #f44336;" id="rejectedCount">0</div>
                                    </div>
                                    <i class="fas fa-times-circle" style="font-size: 2rem; color: #f44336; opacity: 0.7;"></i>
                                </div>
                            </div>
                        </div>

                        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
                            <h4 style="margin-bottom: 10px; color: #495057;">
                                <i class="fas fa-info-circle"></i>
                                注意事项
                            </h4>
                            <ul style="margin: 0; padding-left: 20px; color: #666; font-size: 0.9rem;">
                                <li>只有审批通过的数据才能生成报表</li>
                                <li>报表数据基于化验时间进行统计</li>
                                <li>导出的报表包含完整的水质检测数据</li>
                                <li>PDF格式报表包含图表和统计分析</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal" id="detailModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>化验单详情</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>

    <!-- 审批模态框 -->
    <div class="modal" id="approvalModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>审批化验单</h3>
                <button class="modal-close" onclick="closeApprovalModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="approvalContent">
                    <!-- 审批内容将通过JavaScript动态加载 -->
                </div>
                <div style="margin-top: 20px;">
                    <div class="form-group">
                        <label class="form-label">审批意见</label>
                        <textarea class="form-textarea" id="approvalComment" placeholder="请输入审批意见..."></textarea>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-success" onclick="approveData()">
                            <i class="fas fa-check"></i>
                            审批通过
                        </button>
                        <button class="btn btn-danger" onclick="rejectData()">
                            <i class="fas fa-times"></i>
                            审批拒绝
                        </button>
                        <button class="btn btn-secondary" onclick="closeApprovalModal()">
                            <i class="fas fa-times"></i>
                            取消
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前时间
            const now = new Date();
            const localDateTime = now.getFullYear() + '-' + 
                String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                String(now.getDate()).padStart(2, '0') + 'T' + 
                String(now.getHours()).padStart(2, '0') + ':' + 
                String(now.getMinutes()).padStart(2, '0');
            document.getElementById('testTime').value = localDateTime;

            // 生成化验编号
            generateTestNumber();
        });

        // 生成化验编号
        function generateTestNumber() {
            const now = new Date();
            const dateStr = now.getFullYear() + 
                String(now.getMonth() + 1).padStart(2, '0') + 
                String(now.getDate()).padStart(2, '0');
            const randomNum = String(Math.floor(Math.random() * 1000)).padStart(3, '0');
            document.getElementById('testNumber').value = `WQ${dateStr}${randomNum}`;
        }

        // 标签页切换
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活选中的标签
            event.target.classList.add('active');

            // 根据标签页加载相应内容
            if (tabId === 'data-browse') {
                loadDataBrowse();
            } else if (tabId === 'approval-flow') {
                loadApprovalFlow();
            } else if (tabId === 'report-export') {
                loadReportExport();
            }
        }

        // 保存数据
        function saveData() {
            const formData = collectFormData();
            if (validateFormData(formData)) {
                // 模拟保存到本地存储
                const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
                formData.id = Date.now();
                formData.status = 'draft';
                formData.createTime = new Date().toLocaleString();
                savedData.push(formData);
                localStorage.setItem('waterQualityData', JSON.stringify(savedData));
                
                showNotification('数据保存成功！', 'success');
                generateTestNumber(); // 生成新的编号
            }
        }

        // 提交审批
        function submitForApproval() {
            const formData = collectFormData();
            if (validateFormData(formData)) {
                const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
                formData.id = Date.now();
                formData.status = 'submitted';
                formData.createTime = new Date().toLocaleString();
                formData.submitTime = new Date().toLocaleString();
                savedData.push(formData);
                localStorage.setItem('waterQualityData', JSON.stringify(savedData));
                
                showNotification('数据已提交审批！', 'success');
                generateTestNumber();
                document.getElementById('waterQualityForm').reset();
                document.getElementById('testTime').value = new Date().toISOString().slice(0, 16);
            }
        }

        // 收集表单数据
        function collectFormData() {
            return {
                testNumber: document.getElementById('testNumber').value,
                testTime: document.getElementById('testTime').value,
                testLocation: document.getElementById('testLocation').value,
                testPersonnel: document.getElementById('testPersonnel').value,
                testDescription: document.getElementById('testDescription').value,
                waterQuality: {
                    temperature: document.getElementById('temperature').value,
                    chromaticity: document.getElementById('chromaticity').value,
                    phValue: document.getElementById('phValue').value,
                    suspendedSolids: document.getElementById('suspendedSolids').value,
                    totalPhosphorus: document.getElementById('totalPhosphorus').value,
                    totalNitrogen: document.getElementById('totalNitrogen').value,
                    codValue: document.getElementById('codValue').value,
                    bodValue: document.getElementById('bodValue').value,
                    dissolvedOxygen: document.getElementById('dissolvedOxygen').value,
                    ammoniaNitrogen: document.getElementById('ammoniaNitrogen').value
                }
            };
        }

        // 验证表单数据
        function validateFormData(data) {
            if (!data.testTime || !data.testLocation || !data.testPersonnel) {
                showNotification('请填写必填项！', 'error');
                return false;
            }
            if (!data.waterQuality.temperature || !data.waterQuality.phValue) {
                showNotification('请填写必填的水质数据！', 'error');
                return false;
            }
            return true;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                padding: 15px 20px;
                border-radius: 8px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            `;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 全局变量
        let currentApprovalId = null;

        // 数据浏览功能
        function loadDataBrowse() {
            loadDataTable();
        }

        // 加载数据表格
        function loadDataTable(filteredData = null) {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const dataToShow = filteredData || savedData;
            const tbody = document.getElementById('dataTableBody');

            if (dataToShow.length === 0) {
                tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666; padding: 40px;">暂无数据</td></tr>';
                return;
            }

            tbody.innerHTML = dataToShow.map(item => `
                <tr>
                    <td>${item.testNumber}</td>
                    <td>${new Date(item.testTime).toLocaleString()}</td>
                    <td>${item.testLocation}</td>
                    <td>${item.testPersonnel}</td>
                    <td>${item.waterQuality.temperature || '-'}</td>
                    <td>${item.waterQuality.phValue || '-'}</td>
                    <td>${getStatusBadge(item.status)}</td>
                    <td>
                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-right: 5px;" onclick="viewDetail(${item.id})">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        ${item.status === 'draft' ? `
                            <button class="btn btn-warning" style="padding: 5px 10px; font-size: 0.8rem; margin-right: 5px;" onclick="editData(${item.id})">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="btn btn-danger" style="padding: 5px 10px; font-size: 0.8rem;" onclick="deleteData(${item.id})">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        ` : ''}
                    </td>
                </tr>
            `).join('');
        }

        // 获取状态标签
        function getStatusBadge(status) {
            const statusMap = {
                'draft': '<span class="status-badge status-draft"><i class="fas fa-edit"></i> 草稿</span>',
                'submitted': '<span class="status-badge status-submitted"><i class="fas fa-paper-plane"></i> 已提交</span>',
                'approved': '<span class="status-badge status-approved"><i class="fas fa-check-circle"></i> 已审批</span>',
                'rejected': '<span class="status-badge status-rejected"><i class="fas fa-times-circle"></i> 已拒绝</span>'
            };
            return statusMap[status] || status;
        }

        // 搜索数据
        function searchData() {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const filters = {
                testNumber: document.getElementById('filterTestNumber').value.toLowerCase(),
                location: document.getElementById('filterLocation').value,
                personnel: document.getElementById('filterPersonnel').value,
                status: document.getElementById('filterStatus').value,
                startDate: document.getElementById('filterStartDate').value,
                endDate: document.getElementById('filterEndDate').value
            };

            const filteredData = savedData.filter(item => {
                const testDate = new Date(item.testTime).toISOString().split('T')[0];

                return (!filters.testNumber || item.testNumber.toLowerCase().includes(filters.testNumber)) &&
                       (!filters.location || item.testLocation === filters.location) &&
                       (!filters.personnel || item.testPersonnel === filters.personnel) &&
                       (!filters.status || item.status === filters.status) &&
                       (!filters.startDate || testDate >= filters.startDate) &&
                       (!filters.endDate || testDate <= filters.endDate);
            });

            loadDataTable(filteredData);
        }

        // 重置过滤器
        function resetFilter() {
            document.getElementById('filterTestNumber').value = '';
            document.getElementById('filterLocation').value = '';
            document.getElementById('filterPersonnel').value = '';
            document.getElementById('filterStatus').value = '';
            document.getElementById('filterStartDate').value = '';
            document.getElementById('filterEndDate').value = '';
            loadDataTable();
        }

        // 查看详情
        function viewDetail(id) {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const item = savedData.find(data => data.id === id);

            if (!item) return;

            const modalBody = document.getElementById('modalBody');
            modalBody.innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 25px;">
                    <div>
                        <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #3498db; padding-bottom: 5px;">
                            <i class="fas fa-info-circle"></i> 基本信息
                        </h4>
                        <div style="space-y: 10px;">
                            <div style="margin-bottom: 10px;"><strong>化验编号：</strong>${item.testNumber}</div>
                            <div style="margin-bottom: 10px;"><strong>化验时间：</strong>${new Date(item.testTime).toLocaleString()}</div>
                            <div style="margin-bottom: 10px;"><strong>化验地点：</strong>${item.testLocation}</div>
                            <div style="margin-bottom: 10px;"><strong>化验人员：</strong>${item.testPersonnel}</div>
                            <div style="margin-bottom: 10px;"><strong>审批状态：</strong>${getStatusBadge(item.status)}</div>
                            <div style="margin-bottom: 10px;"><strong>创建时间：</strong>${item.createTime}</div>
                        </div>
                    </div>
                    <div>
                        <h4 style="color: #2c3e50; margin-bottom: 15px; border-bottom: 2px solid #e74c3c; padding-bottom: 5px;">
                            <i class="fas fa-tint"></i> 水质数据
                        </h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 0.9rem;">
                            <div><strong>温度：</strong>${item.waterQuality.temperature || '-'} °C</div>
                            <div><strong>色度：</strong>${item.waterQuality.chromaticity || '-'} 度</div>
                            <div><strong>PH值：</strong>${item.waterQuality.phValue || '-'}</div>
                            <div><strong>悬浮物(SS)：</strong>${item.waterQuality.suspendedSolids || '-'} mg/L</div>
                            <div><strong>总磷(TP)：</strong>${item.waterQuality.totalPhosphorus || '-'} mg/L</div>
                            <div><strong>总氮(TN)：</strong>${item.waterQuality.totalNitrogen || '-'} mg/L</div>
                            <div><strong>COD：</strong>${item.waterQuality.codValue || '-'} mg/L</div>
                            <div><strong>BOD：</strong>${item.waterQuality.bodValue || '-'} mg/L</div>
                            <div><strong>溶解氧(DO)：</strong>${item.waterQuality.dissolvedOxygen || '-'} mg/L</div>
                            <div><strong>氨氮：</strong>${item.waterQuality.ammoniaNitrogen || '-'} mg/L</div>
                        </div>
                    </div>
                </div>
                ${item.testDescription ? `
                    <div>
                        <h4 style="color: #2c3e50; margin-bottom: 10px;">
                            <i class="fas fa-file-alt"></i> 化验描述
                        </h4>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #6c757d;">
                            ${item.testDescription}
                        </div>
                    </div>
                ` : ''}
            `;

            document.getElementById('detailModal').style.display = 'flex';
        }

        // 编辑数据
        function editData(id) {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const item = savedData.find(data => data.id === id);

            if (!item) return;

            // 切换到数据录入标签页
            showTab('data-entry');

            // 填充表单数据
            document.getElementById('testNumber').value = item.testNumber;
            document.getElementById('testTime').value = item.testTime;
            document.getElementById('testLocation').value = item.testLocation;
            document.getElementById('testPersonnel').value = item.testPersonnel;
            document.getElementById('testDescription').value = item.testDescription || '';

            // 填充水质数据
            Object.keys(item.waterQuality).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = item.waterQuality[key] || '';
                }
            });

            // 删除原数据，保存时会创建新记录
            deleteData(id, false);
        }

        // 删除数据
        function deleteData(id, showConfirm = true) {
            if (showConfirm && !confirm('确定要删除这条化验数据吗？')) {
                return;
            }

            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const filteredData = savedData.filter(item => item.id !== id);
            localStorage.setItem('waterQualityData', JSON.stringify(filteredData));

            if (showConfirm) {
                showNotification('数据删除成功！', 'success');
                loadDataTable();
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('detailModal').style.display = 'none';
        }

        // 流程审批功能
        function loadApprovalFlow() {
            loadApprovalTable();
            loadHistoryTable();
            updateStatistics();
        }

        // 加载待审批表格
        function loadApprovalTable() {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const pendingData = savedData.filter(item => item.status === 'submitted');
            const tbody = document.getElementById('approvalTableBody');

            if (pendingData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" style="text-align: center; color: #666; padding: 40px;">暂无待审批数据</td></tr>';
                return;
            }

            tbody.innerHTML = pendingData.map(item => `
                <tr>
                    <td>${item.testNumber}</td>
                    <td>${new Date(item.testTime).toLocaleString()}</td>
                    <td>${item.testLocation}</td>
                    <td>${item.testPersonnel}</td>
                    <td>${item.submitTime || '-'}</td>
                    <td>${getStatusBadge(item.status)}</td>
                    <td>
                        <button class="btn btn-primary" style="padding: 5px 10px; font-size: 0.8rem; margin-right: 5px;" onclick="viewDetail(${item.id})">
                            <i class="fas fa-eye"></i> 查看
                        </button>
                        <button class="btn btn-success" style="padding: 5px 10px; font-size: 0.8rem;" onclick="showApprovalModal(${item.id})">
                            <i class="fas fa-check"></i> 审批
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // 加载审批历史表格
        function loadHistoryTable() {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const historyData = savedData.filter(item => item.status === 'approved' || item.status === 'rejected');
            const tbody = document.getElementById('historyTableBody');

            if (historyData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666; padding: 40px;">暂无审批历史</td></tr>';
                return;
            }

            tbody.innerHTML = historyData.map(item => `
                <tr>
                    <td>${item.testNumber}</td>
                    <td>${item.testPersonnel}</td>
                    <td>${item.approver || '班长'}</td>
                    <td>${item.approvalTime || '-'}</td>
                    <td>${getStatusBadge(item.status)}</td>
                    <td>${item.approvalComment || '-'}</td>
                </tr>
            `).join('');
        }

        // 显示审批模态框
        function showApprovalModal(id) {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const item = savedData.find(data => data.id === id);

            if (!item) return;

            currentApprovalId = id;

            const approvalContent = document.getElementById('approvalContent');
            approvalContent.innerHTML = `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">
                        <i class="fas fa-clipboard-list"></i> 审批流程
                    </h4>
                    <div class="approval-flow">
                        <div class="flow-step completed">
                            <div class="flow-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="flow-label">化验人员提交</div>
                        </div>
                        <div class="flow-step current">
                            <div class="flow-icon">
                                <i class="fas fa-user-tie"></i>
                            </div>
                            <div class="flow-label">班长审核</div>
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin-bottom: 20px;">
                    <h4 style="color: #495057; margin-bottom: 15px;">化验单信息</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div><strong>化验编号：</strong>${item.testNumber}</div>
                        <div><strong>化验人员：</strong>${item.testPersonnel}</div>
                        <div><strong>化验地点：</strong>${item.testLocation}</div>
                        <div><strong>提交时间：</strong>${item.submitTime}</div>
                    </div>
                </div>
            `;

            document.getElementById('approvalComment').value = '';
            document.getElementById('approvalModal').style.display = 'flex';
        }

        // 审批通过
        function approveData() {
            if (!currentApprovalId) return;

            const comment = document.getElementById('approvalComment').value;
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const itemIndex = savedData.findIndex(item => item.id === currentApprovalId);

            if (itemIndex !== -1) {
                savedData[itemIndex].status = 'approved';
                savedData[itemIndex].approver = '班长';
                savedData[itemIndex].approvalTime = new Date().toLocaleString();
                savedData[itemIndex].approvalComment = comment;

                localStorage.setItem('waterQualityData', JSON.stringify(savedData));

                showNotification('审批通过成功！', 'success');
                closeApprovalModal();
                loadApprovalFlow();
            }
        }

        // 审批拒绝
        function rejectData() {
            if (!currentApprovalId) return;

            const comment = document.getElementById('approvalComment').value;
            if (!comment.trim()) {
                showNotification('请填写拒绝原因！', 'error');
                return;
            }

            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const itemIndex = savedData.findIndex(item => item.id === currentApprovalId);

            if (itemIndex !== -1) {
                savedData[itemIndex].status = 'rejected';
                savedData[itemIndex].approver = '班长';
                savedData[itemIndex].approvalTime = new Date().toLocaleString();
                savedData[itemIndex].approvalComment = comment;

                localStorage.setItem('waterQualityData', JSON.stringify(savedData));

                showNotification('审批拒绝成功！', 'success');
                closeApprovalModal();
                loadApprovalFlow();
            }
        }

        // 关闭审批模态框
        function closeApprovalModal() {
            document.getElementById('approvalModal').style.display = 'none';
            currentApprovalId = null;
        }

        // 更新统计信息
        function updateStatistics() {
            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');

            document.getElementById('totalCount').textContent = savedData.length;
            document.getElementById('approvedCount').textContent = savedData.filter(item => item.status === 'approved').length;
            document.getElementById('pendingCount').textContent = savedData.filter(item => item.status === 'submitted').length;
            document.getElementById('rejectedCount').textContent = savedData.filter(item => item.status === 'rejected').length;
        }

        // 报表导出功能
        function loadReportExport() {
            updateStatistics();

            // 监听报表类型变化
            document.getElementById('reportType').addEventListener('change', function() {
                const customDateRange = document.getElementById('customDateRange');
                if (this.value === 'custom') {
                    customDateRange.style.display = 'block';
                } else {
                    customDateRange.style.display = 'none';
                }
            });
        }

        // 生成报表
        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            const location = document.getElementById('reportLocation').value;
            const format = document.getElementById('exportFormat').value;

            const savedData = JSON.parse(localStorage.getItem('waterQualityData') || '[]');
            const approvedData = savedData.filter(item => item.status === 'approved');

            if (approvedData.length === 0) {
                showNotification('没有已审批的数据可以生成报表！', 'error');
                return;
            }

            // 根据条件过滤数据
            let filteredData = approvedData;

            if (location) {
                filteredData = filteredData.filter(item => item.testLocation === location);
            }

            // 根据报表类型过滤时间
            const now = new Date();
            if (reportType === 'daily') {
                const today = now.toISOString().split('T')[0];
                filteredData = filteredData.filter(item => {
                    const testDate = new Date(item.testTime).toISOString().split('T')[0];
                    return testDate === today;
                });
            } else if (reportType === 'weekly') {
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                filteredData = filteredData.filter(item => {
                    const testDate = new Date(item.testTime);
                    return testDate >= weekAgo && testDate <= now;
                });
            } else if (reportType === 'monthly') {
                const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
                filteredData = filteredData.filter(item => {
                    const testDate = new Date(item.testTime);
                    return testDate >= monthAgo && testDate <= now;
                });
            } else if (reportType === 'custom') {
                const startDate = document.getElementById('reportStartDate').value;
                const endDate = document.getElementById('reportEndDate').value;

                if (startDate && endDate) {
                    filteredData = filteredData.filter(item => {
                        const testDate = new Date(item.testTime).toISOString().split('T')[0];
                        return testDate >= startDate && testDate <= endDate;
                    });
                }
            }

            if (filteredData.length === 0) {
                showNotification('所选条件下没有数据可以生成报表！', 'error');
                return;
            }

            showNotification(`正在生成${getReportTypeName(reportType)}报表，共${filteredData.length}条数据...`, 'info');

            // 模拟报表生成过程
            setTimeout(() => {
                showNotification(`${format.toUpperCase()}格式报表生成成功！`, 'success');
            }, 2000);
        }

        // 导出报表
        function exportReport() {
            const format = document.getElementById('exportFormat').value;
            showNotification(`正在导出${format.toUpperCase()}格式报表...`, 'info');

            setTimeout(() => {
                showNotification('报表导出成功！', 'success');
            }, 1500);
        }

        // 获取报表类型名称
        function getReportTypeName(type) {
            const typeMap = {
                'daily': '日',
                'weekly': '周',
                'monthly': '月',
                'custom': '自定义'
            };
            return typeMap[type] || type;
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const detailModal = document.getElementById('detailModal');
            const approvalModal = document.getElementById('approvalModal');

            if (event.target === detailModal) {
                closeModal();
            }
            if (event.target === approvalModal) {
                closeApprovalModal();
            }
        }
    </script>
</body>
</html>
