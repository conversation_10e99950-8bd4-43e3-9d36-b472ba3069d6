<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管网漏损控制系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 25%, #4facfe 50%, #00f2fe 75%, #43e97b 100%);
            min-height: 100vh;
            color: #333;
        }

        .leakage-container {
            max-width: 1900px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .leakage-header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 30px;
            padding: 40px 50px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .leakage-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(30, 60, 114, 0.08), transparent);
            animation: shimmer 4s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .main-title {
            font-size: 3.5rem;
            background: linear-gradient(135deg, #1e3c72, #2a5298, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            position: relative;
            z-index: 1;
        }

        .subtitle {
            color: #666;
            font-size: 1.4rem;
            margin-bottom: 30px;
            position: relative;
            z-index: 1;
        }

        .system-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin-top: 35px;
            position: relative;
            z-index: 1;
        }

        .metric-item {
            background: rgba(255, 255, 255, 0.85);
            padding: 25px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(15px);
            transition: all 0.4s ease;
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .metric-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #1e3c72, #4facfe, #00f2fe);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .metric-item:hover {
            transform: translateY(-8px);
            border-color: #4facfe;
            box-shadow: 0 15px 40px rgba(79, 172, 254, 0.3);
        }

        .metric-item:hover::before {
            transform: scaleX(1);
        }

        .metric-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .metric-title {
            font-size: 1.2rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 8px;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 5px;
        }

        .metric-desc {
            font-size: 0.9rem;
            color: #666;
        }

        /* 导航标签 */
        .nav-tabs {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 18px;
            margin-bottom: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            display: flex;
            gap: 12px;
            overflow-x: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }

        .nav-tabs::-webkit-scrollbar {
            display: none;
        }

        .nav-tab {
            flex: 1;
            min-width: 200px;
            padding: 22px 30px;
            background: transparent;
            border: none;
            border-radius: 18px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 700;
            color: #7f8c8d;
            transition: all 0.4s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
            position: relative;
            overflow: hidden;
        }

        .nav-tab::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            transition: left 0.4s ease;
            z-index: -1;
        }

        .nav-tab.active::before {
            left: 0;
        }

        .nav-tab.active {
            color: white;
            box-shadow: 0 12px 35px rgba(30, 60, 114, 0.4);
            transform: translateY(-4px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(30, 60, 114, 0.1);
            color: #1e3c72;
            transform: translateY(-3px);
        }

        .nav-icon {
            font-size: 1.8rem;
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 30px;
            padding: 45px;
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.1);
            min-height: 900px;
        }

        .tab-content {
            display: none;
            animation: fadeInUp 0.8s ease-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 模块标题 */
        .module-title {
            font-size: 2.2rem;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 35px;
            display: flex;
            align-items: center;
            gap: 18px;
            padding-bottom: 20px;
            border-bottom: 3px solid #f0f0f0;
            position: relative;
        }

        .module-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 100px;
            height: 3px;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
        }

        /* KPI指标卡片 */
        .kpi-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .kpi-card {
            background: linear-gradient(135deg, #ffffff, #f8f9fa);
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.4s ease;
            border-left: 8px solid;
            cursor: pointer;
        }

        .kpi-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 70px rgba(0, 0, 0, 0.15);
        }

        .kpi-card.zone { border-left-color: #2196f3; }
        .kpi-card.warning { border-left-color: #ff9800; }
        .kpi-card.analysis { border-left-color: #4caf50; }
        .kpi-card.difference { border-left-color: #9c27b0; }
        .kpi-card.leakage { border-left-color: #f44336; }

        .kpi-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
        }

        .kpi-title {
            font-size: 1.3rem;
            color: #666;
            display: flex;
            align-items: center;
            gap: 12px;
            font-weight: 700;
        }

        .kpi-trend {
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .trend-up { background: #ffebee; color: #c62828; }
        .trend-down { background: #e8f5e8; color: #2e7d32; }
        .trend-stable { background: #fff3e0; color: #ef6c00; }

        .kpi-value {
            font-size: 3.2rem;
            font-weight: bold;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 18px;
            display: flex;
            align-items: baseline;
            gap: 12px;
        }

        .kpi-unit {
            font-size: 1.2rem;
            color: #888;
        }

        .kpi-description {
            font-size: 1rem;
            color: #666;
            line-height: 1.6;
        }

        /* 图表区域 */
        .charts-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 35px;
            margin-bottom: 40px;
        }

        .chart-card {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 35px;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
        }

        .chart-title {
            font-size: 1.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .chart-controls {
            display: flex;
            gap: 12px;
        }

        .chart-btn {
            padding: 10px 18px;
            border: 2px solid #e0e0e0;
            border-radius: 12px;
            background: white;
            color: #666;
            font-size: 0.95rem;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .chart-btn.active {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
            border-color: transparent;
            box-shadow: 0 8px 25px rgba(30, 60, 114, 0.3);
        }

        .chart-btn:hover:not(.active) {
            background: #f5f5f5;
            border-color: #1e3c72;
        }

        .chart-container {
            position: relative;
            height: 450px;
        }

        /* 数据表格 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
            background: white;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 18px 25px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-table th {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
            font-weight: 700;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: rgba(30, 60, 114, 0.05);
        }

        .data-table .number {
            text-align: right;
            font-weight: 700;
        }

        .status-normal { color: #4caf50; font-weight: 700; }
        .status-warning { color: #ff9800; font-weight: 700; }
        .status-danger { color: #f44336; font-weight: 700; }
        .status-info { color: #2196f3; font-weight: 700; }

        /* 按钮样式 */
        .btn {
            padding: 14px 28px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.05rem;
            font-weight: 700;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(30, 60, 114, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #4caf50, #45a049);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #f44336, #d32f2f);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 18px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        /* 预警面板 */
        .alert-panel {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-left: 8px solid #ffc107;
            border-radius: 20px;
            padding: 25px 30px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 20px;
            box-shadow: 0 12px 40px rgba(255, 193, 7, 0.2);
        }

        .alert-panel.danger {
            background: linear-gradient(135deg, #f8d7da, #fab1a0);
            border-left-color: #dc3545;
            box-shadow: 0 12px 40px rgba(220, 53, 69, 0.2);
        }

        .alert-panel.success {
            background: linear-gradient(135deg, #d4edda, #a8e6cf);
            border-left-color: #28a745;
            box-shadow: 0 12px 40px rgba(40, 167, 69, 0.2);
        }

        .alert-icon {
            font-size: 2.5rem;
            color: #856404;
        }

        .alert-panel.danger .alert-icon { color: #721c24; }
        .alert-panel.success .alert-icon { color: #155724; }

        .alert-content {
            flex: 1;
        }

        .alert-title {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            color: #856404;
        }

        .alert-panel.danger .alert-title { color: #721c24; }
        .alert-panel.success .alert-title { color: #155724; }

        .alert-message {
            font-size: 1rem;
            line-height: 1.6;
            color: #856404;
        }

        .alert-panel.danger .alert-message { color: #721c24; }
        .alert-panel.success .alert-message { color: #155724; }

        /* 地图容器 */
        .map-container {
            height: 500px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.1);
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #1e3c72;
            font-size: 1.3rem;
            position: relative;
        }

        .map-placeholder {
            text-align: center;
        }

        .map-placeholder i {
            font-size: 6rem;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #1e3c72, #4facfe);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* 响应式设计 */
        @media (max-width: 1400px) {
            .charts-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .leakage-container {
                padding: 15px;
            }

            .main-title {
                font-size: 2.8rem;
                flex-direction: column;
                gap: 20px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                min-width: auto;
                padding: 18px 25px;
                flex-direction: row;
                justify-content: center;
            }

            .kpi-grid {
                grid-template-columns: 1fr;
            }

            .system-metrics {
                grid-template-columns: repeat(2, 1fr);
            }

            .btn-group {
                flex-direction: column;
                align-items: center;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 1s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(40px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.8s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-40px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* 加载动画 */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 250px;
            color: #1e3c72;
            font-size: 1.2rem;
        }

        .loading i {
            animation: spin 1s linear infinite;
            margin-right: 12px;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="leakage-container">
        <!-- 系统头部 -->
        <div class="leakage-header fade-in">
            <h1 class="main-title">
                <i class="fas fa-tint"></i>
                管网漏损控制系统
                <i class="fas fa-chart-line"></i>
            </h1>
            <p class="subtitle">Pipeline Leakage Control System - 智能分区管理 · 精准漏损分析 · 高效预警处置</p>
            
            <div class="system-metrics">
                <div class="metric-item">
                    <div class="metric-icon">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <div class="metric-title">DMA分区</div>
                    <div class="metric-value">28</div>
                    <div class="metric-desc">个分区</div>
                </div>
                <div class="metric-item">
                    <div class="metric-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="metric-title">实时预警</div>
                    <div class="metric-value">5</div>
                    <div class="metric-desc">条预警</div>
                </div>
                <div class="metric-item">
                    <div class="metric-icon">
                        <i class="fas fa-chart-pie"></i>
                    </div>
                    <div class="metric-title">总漏损率</div>
                    <div class="metric-value">6.8%</div>
                    <div class="metric-desc">目标≤5%</div>
                </div>
                <div class="metric-item">
                    <div class="metric-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="metric-title">产销差率</div>
                    <div class="metric-value">8.2%</div>
                    <div class="metric-desc">较上月-1.5%</div>
                </div>
                <div class="metric-item">
                    <div class="metric-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="metric-title">漏点定位</div>
                    <div class="metric-value">12</div>
                    <div class="metric-desc">个疑似点</div>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" data-tab="zone-management">
                <i class="nav-icon fas fa-map-marked-alt"></i>
                <span>分区管理</span>
            </button>
            <button class="nav-tab" data-tab="warning-handling">
                <i class="nav-icon fas fa-exclamation-triangle"></i>
                <span>预警处置</span>
            </button>
            <button class="nav-tab" data-tab="operation-analysis">
                <i class="nav-icon fas fa-chart-line"></i>
                <span>运行分析</span>
            </button>
            <button class="nav-tab" data-tab="difference-analysis">
                <i class="nav-icon fas fa-balance-scale"></i>
                <span>产销差分析</span>
            </button>
            <button class="nav-tab" data-tab="leakage-analysis">
                <i class="nav-icon fas fa-search"></i>
                <span>漏损分析</span>
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 分区管理 -->
            <div id="zone-management" class="tab-content active">
                <h2 class="module-title">
                    <i class="fas fa-map-marked-alt"></i>
                    DMA分区管理
                </h2>

                <!-- 分区概况 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">分区管理状态良好</div>
                        <div class="alert-message">全市共划分28个DMA分区，覆盖率100%，各分区监测设备运行正常，数据采集完整率98.5%</div>
                    </div>
                </div>

                <!-- 分区统计 -->
                <div class="kpi-grid">
                    <div class="kpi-card zone">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-layer-group"></i>
                                DMA分区总数
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-check-circle"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            28
                            <span class="kpi-unit">个</span>
                        </div>
                        <div class="kpi-description">
                            覆盖全市供水网络，平均服务人口1.8万人/区
                        </div>
                    </div>

                    <div class="kpi-card zone">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-eye"></i>
                                监测设备在线率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.1%
                            </div>
                        </div>
                        <div class="kpi-value">
                            98.5
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            流量计、压力计等监测设备运行状态良好
                        </div>
                    </div>

                    <div class="kpi-card zone">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-database"></i>
                                数据采集完整率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +1.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            97.2
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            24小时连续数据采集，缺失数据及时补充
                        </div>
                    </div>

                    <div class="kpi-card zone">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-exclamation-triangle"></i>
                                异常分区数量
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -2个
                            </div>
                        </div>
                        <div class="kpi-value">
                            3
                            <span class="kpi-unit">个</span>
                        </div>
                        <div class="kpi-description">
                            DMA-05、DMA-12、DMA-18存在异常，正在处理
                        </div>
                    </div>
                </div>

                <!-- 分区地图 -->
                <div class="chart-card">
                    <div class="chart-header">
                        <h3 class="chart-title">
                            <i class="fas fa-map"></i>
                            DMA分区分布图
                        </h3>
                        <div class="chart-controls">
                            <button class="chart-btn active" onclick="switchMapView('overview')">总览</button>
                            <button class="chart-btn" onclick="switchMapView('leakage')">漏损</button>
                            <button class="chart-btn" onclick="switchMapView('pressure')">压力</button>
                            <button class="chart-btn" onclick="switchMapView('flow')">流量</button>
                        </div>
                    </div>
                    <div class="map-container">
                        <div class="map-placeholder">
                            <i class="fas fa-map-marked-alt"></i>
                            <div style="font-size: 1.8rem; font-weight: 700; margin-bottom: 15px;">DMA分区管理地图</div>
                            <div style="font-size: 1.1rem; margin-bottom: 10px;">28个DMA分区实时监控</div>
                            <div style="font-size: 1rem; color: #666;">点击切换不同视图模式</div>
                            <div style="margin-top: 25px;">
                                <button class="btn btn-primary" onclick="loadDMAMap()">
                                    <i class="fas fa-play"></i>
                                    加载分区地图
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分区数据表格 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>分区编号</th>
                            <th>分区名称</th>
                            <th>服务人口</th>
                            <th>管网长度</th>
                            <th>夜间最小流量</th>
                            <th>漏损率</th>
                            <th>监测状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DMA-01</td>
                            <td>东区中心</td>
                            <td class="number">22,500</td>
                            <td class="number">45.2 km</td>
                            <td class="number">85 m³/h</td>
                            <td class="number status-normal">3.8%</td>
                            <td class="status-normal">正常</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>DMA-05</td>
                            <td>西区工业</td>
                            <td class="number">18,200</td>
                            <td class="number">38.6 km</td>
                            <td class="number">125 m³/h</td>
                            <td class="number status-danger">8.5%</td>
                            <td class="status-warning">异常</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>DMA-12</td>
                            <td>南区住宅</td>
                            <td class="number">25,800</td>
                            <td class="number">52.1 km</td>
                            <td class="number">95 m³/h</td>
                            <td class="number status-warning">6.2%</td>
                            <td class="status-warning">预警</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>DMA-18</td>
                            <td>北区商业</td>
                            <td class="number">19,600</td>
                            <td class="number">41.3 km</td>
                            <td class="number">110 m³/h</td>
                            <td class="number status-warning">5.8%</td>
                            <td class="status-info">监测中</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>DMA-23</td>
                            <td>开发区</td>
                            <td class="number">15,400</td>
                            <td class="number">32.8 km</td>
                            <td class="number">68 m³/h</td>
                            <td class="number status-normal">4.2%</td>
                            <td class="status-normal">正常</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 预警处置 -->
            <div id="warning-handling" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-exclamation-triangle"></i>
                    预警处置管理
                </h2>

                <!-- 预警状态 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">高级预警 - 需要立即处置</div>
                        <div class="alert-message">DMA-05西区工业分区夜间最小流量异常增加35%，疑似存在较大漏点，已自动启动应急处置流程</div>
                    </div>
                </div>

                <!-- 预警统计 -->
                <div class="kpi-grid">
                    <div class="kpi-card warning">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-bell"></i>
                                当前活跃预警
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2条
                            </div>
                        </div>
                        <div class="kpi-value">
                            5
                            <span class="kpi-unit">条</span>
                        </div>
                        <div class="kpi-description">
                            高级预警1条，中级预警2条，低级预警2条
                        </div>
                    </div>

                    <div class="kpi-card warning">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-clock"></i>
                                平均响应时间
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8分钟
                            </div>
                        </div>
                        <div class="kpi-value">
                            15
                            <span class="kpi-unit">分钟</span>
                        </div>
                        <div class="kpi-description">
                            预警响应时间持续优化，处置效率显著提升
                        </div>
                    </div>

                    <div class="kpi-card warning">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-check-circle"></i>
                                处置成功率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +5.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            94.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            预警处置成功率高，误报率控制在合理范围
                        </div>
                    </div>

                    <div class="kpi-card warning">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-chart-line"></i>
                                预警准确率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            87.5
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            智能算法持续优化，预警准确性不断提升
                        </div>
                    </div>
                </div>

                <!-- 预警列表 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>预警编号</th>
                            <th>预警类型</th>
                            <th>分区位置</th>
                            <th>预警等级</th>
                            <th>触发时间</th>
                            <th>异常指标</th>
                            <th>处置状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>YJ-2024-001</td>
                            <td class="status-danger">流量异常</td>
                            <td>DMA-05西区工业</td>
                            <td class="status-danger">高级</td>
                            <td>14:25:30</td>
                            <td>夜间流量+35%</td>
                            <td class="status-warning">处置中</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">立即处置</button></td>
                        </tr>
                        <tr>
                            <td>YJ-2024-002</td>
                            <td class="status-warning">压力波动</td>
                            <td>DMA-12南区住宅</td>
                            <td class="status-warning">中级</td>
                            <td>13:45:15</td>
                            <td>压力波动±0.15MPa</td>
                            <td class="status-info">已派工</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                        <tr>
                            <td>YJ-2024-003</td>
                            <td class="status-info">设备离线</td>
                            <td>DMA-18北区商业</td>
                            <td class="status-info">低级</td>
                            <td>12:30:45</td>
                            <td>流量计离线</td>
                            <td class="status-normal">已处理</td>
                            <td><button class="btn btn-primary" style="padding: 8px 16px; font-size: 0.9rem;">查看详情</button></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 运行分析 -->
            <div id="operation-analysis" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-chart-line"></i>
                    管网运行分析
                </h2>

                <!-- 运行状态 -->
                <div class="alert-panel success">
                    <i class="fas fa-check-circle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">管网运行状态良好</div>
                        <div class="alert-message">整体管网运行平稳，供水保证率99.2%，各项运行指标均在正常范围内，系统运行效率持续优化</div>
                    </div>
                </div>

                <!-- 运行指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                供水保证率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +0.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            99.2
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            供水保证率持续提升，用户满意度显著改善
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-gauge"></i>
                                平均管网压力
                            </div>
                            <div class="kpi-trend trend-stable">
                                <i class="fas fa-minus"></i>
                                稳定
                            </div>
                        </div>
                        <div class="kpi-value">
                            0.58
                            <span class="kpi-unit">MPa</span>
                        </div>
                        <div class="kpi-description">
                            管网压力保持稳定，满足用户用水需求
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-water"></i>
                                日均供水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            42,580
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-description">
                            供水量稳步增长，满足城市发展需求
                        </div>
                    </div>

                    <div class="kpi-card analysis">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-cogs"></i>
                                系统运行效率
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            91.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            系统运行效率持续优化，能耗显著降低
                        </div>
                    </div>
                </div>

                <!-- 运行趋势图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                供水量与压力趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchAnalysisPeriod('24h')">24小时</button>
                                <button class="chart-btn" onclick="switchAnalysisPeriod('7d')">7天</button>
                                <button class="chart-btn" onclick="switchAnalysisPeriod('30d')">30天</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="operationTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                分区运行状态分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="zoneStatusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 产销差分析 -->
            <div id="difference-analysis" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-balance-scale"></i>
                    产销差分析
                </h2>

                <!-- 产销差状态 -->
                <div class="alert-panel warning">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">产销差率需要关注</div>
                        <div class="alert-message">本月产销差率为8.2%，虽较上月下降1.5%，但仍高于目标值6%，需要进一步分析原因并采取措施</div>
                    </div>
                </div>

                <!-- 产销差指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card difference">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-percentage"></i>
                                产销差率
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.5%
                            </div>
                        </div>
                        <div class="kpi-value">
                            8.2
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            较上月下降1.5%，但仍高于目标值6%
                        </div>
                    </div>

                    <div class="kpi-card difference">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-industry"></i>
                                月产水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +2.8%
                            </div>
                        </div>
                        <div class="kpi-value">
                            1,285,600
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-description">
                            水厂总产水量，较上月增长2.8%
                        </div>
                    </div>

                    <div class="kpi-card difference">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-shopping-cart"></i>
                                月售水量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +4.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            1,180,200
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-description">
                            计费售水量，较上月增长4.2%
                        </div>
                    </div>

                    <div class="kpi-card difference">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-minus-circle"></i>
                                产销差水量
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -8,500m³
                            </div>
                        </div>
                        <div class="kpi-value">
                            105,400
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-description">
                            产销差绝对量，较上月减少8,500m³
                        </div>
                    </div>
                </div>

                <!-- 产销差分析图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-line"></i>
                                产销差趋势分析
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchDifferencePeriod('month')">月度</button>
                                <button class="chart-btn" onclick="switchDifferencePeriod('quarter')">季度</button>
                                <button class="chart-btn" onclick="switchDifferencePeriod('year')">年度</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="differenceTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-pie"></i>
                                产销差构成分析
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="differenceCompositionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 漏损分析 -->
            <div id="leakage-analysis" class="tab-content">
                <h2 class="module-title">
                    <i class="fas fa-search"></i>
                    漏损分析
                </h2>

                <!-- 漏损状态 -->
                <div class="alert-panel danger">
                    <i class="fas fa-exclamation-triangle alert-icon"></i>
                    <div class="alert-content">
                        <div class="alert-title">发现疑似漏点</div>
                        <div class="alert-message">通过夜间最小流量分析和声学检测，在DMA-05、DMA-12、DMA-18发现12个疑似漏点，建议立即组织现场确认</div>
                    </div>
                </div>

                <!-- 漏损指标 -->
                <div class="kpi-grid">
                    <div class="kpi-card leakage">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-tint"></i>
                                总漏损率
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -1.2%
                            </div>
                        </div>
                        <div class="kpi-value">
                            6.8
                            <span class="kpi-unit">%</span>
                        </div>
                        <div class="kpi-description">
                            较上月下降1.2%，但仍高于目标值5%
                        </div>
                    </div>

                    <div class="kpi-card leakage">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-search"></i>
                                疑似漏点数量
                            </div>
                            <div class="kpi-trend trend-up">
                                <i class="fas fa-arrow-up"></i>
                                +3个
                            </div>
                        </div>
                        <div class="kpi-value">
                            12
                            <span class="kpi-unit">个</span>
                        </div>
                        <div class="kpi-description">
                            通过智能分析发现的疑似漏点，待现场确认
                        </div>
                    </div>

                    <div class="kpi-card leakage">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-water"></i>
                                日漏损水量
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -850m³
                            </div>
                        </div>
                        <div class="kpi-value">
                            2,895
                            <span class="kpi-unit">m³</span>
                        </div>
                        <div class="kpi-description">
                            日均漏损水量，较上月减少850m³
                        </div>
                    </div>

                    <div class="kpi-card leakage">
                        <div class="kpi-header">
                            <div class="kpi-title">
                                <i class="fas fa-dollar-sign"></i>
                                月漏损损失
                            </div>
                            <div class="kpi-trend trend-down">
                                <i class="fas fa-arrow-down"></i>
                                -2.5万
                            </div>
                        </div>
                        <div class="kpi-value">
                            18.6
                            <span class="kpi-unit">万元</span>
                        </div>
                        <div class="kpi-description">
                            漏损造成的经济损失，较上月减少2.5万元
                        </div>
                    </div>
                </div>

                <!-- 漏损分析图表 -->
                <div class="charts-grid">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-chart-area"></i>
                                漏损率变化趋势
                            </h3>
                            <div class="chart-controls">
                                <button class="chart-btn active" onclick="switchLeakagePeriod('month')">月度</button>
                                <button class="chart-btn" onclick="switchLeakagePeriod('quarter')">季度</button>
                                <button class="chart-btn" onclick="switchLeakagePeriod('year')">年度</button>
                            </div>
                        </div>
                        <div class="chart-container">
                            <canvas id="leakageTrendChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3 class="chart-title">
                                <i class="fas fa-map-marked-alt"></i>
                                分区漏损分布
                            </h3>
                        </div>
                        <div class="chart-container">
                            <canvas id="zoneLeakageChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- 疑似漏点列表 -->
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>漏点编号</th>
                            <th>所属分区</th>
                            <th>检测方法</th>
                            <th>疑似位置</th>
                            <th>置信度</th>
                            <th>预估流量</th>
                            <th>发现时间</th>
                            <th>处理状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>LD-2024-001</td>
                            <td>DMA-05</td>
                            <td>夜间流量分析</td>
                            <td>工业路段</td>
                            <td class="number status-danger">95%</td>
                            <td class="number">25 L/min</td>
                            <td>2024-01-15 02:30</td>
                            <td class="status-warning">待确认</td>
                        </tr>
                        <tr>
                            <td>LD-2024-002</td>
                            <td>DMA-12</td>
                            <td>声学检测</td>
                            <td>住宅小区内</td>
                            <td class="number status-warning">78%</td>
                            <td class="number">15 L/min</td>
                            <td>2024-01-14 23:45</td>
                            <td class="status-info">现场检查中</td>
                        </tr>
                        <tr>
                            <td>LD-2024-003</td>
                            <td>DMA-18</td>
                            <td>压力分析</td>
                            <td>商业街区</td>
                            <td class="number status-warning">82%</td>
                            <td class="number">18 L/min</td>
                            <td>2024-01-14 21:15</td>
                            <td class="status-warning">待确认</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentTab = 'zone-management';
        let charts = {};

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('管网漏损控制系统初始化...');
            
            // 初始化导航事件
            initNavEvents();
            
            console.log('系统初始化完成');
        });

        // 初始化导航事件
        function initNavEvents() {
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab');
                    switchTab(tabId, this);
                });
            });
        }

        // 标签页切换
        function switchTab(tabId, clickedTab) {
            console.log('切换到标签页:', tabId);
            
            currentTab = tabId;
            
            // 隐藏所有内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有激活状态
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中内容
            const targetContent = document.getElementById(tabId);
            if (targetContent) {
                targetContent.classList.add('active');
            } else {
                // 如果内容不存在，动态加载
                loadTabContent(tabId);
            }
            
            // 激活选中导航
            if (clickedTab) {
                clickedTab.classList.add('active');
            }
        }

        // 动态加载标签页内容
        function loadTabContent(tabId) {
            const contentArea = document.querySelector('.content-area');
            
            // 显示加载动画
            contentArea.innerHTML = `
                <div class="loading">
                    <i class="fas fa-spinner"></i>
                    正在加载${getTabName(tabId)}模块...
                </div>
            `;
            
            // 模拟加载延迟
            setTimeout(() => {
                generateTabContent(tabId);
            }, 1000);
        }

        // 获取标签页名称
        function getTabName(tabId) {
            const names = {
                'zone-management': '分区管理',
                'warning-handling': '预警处置',
                'operation-analysis': '运行分析',
                'difference-analysis': '产销差分析',
                'leakage-analysis': '漏损分析'
            };
            return names[tabId] || '未知模块';
        }

        // 生成标签页内容
        function generateTabContent(tabId) {
            // 内容已经在HTML中预定义，这里只需要初始化对应的图表
            setTimeout(() => {
                initTabCharts(tabId);
            }, 100);
        }

        // 初始化标签页图表
        function initTabCharts(tabId) {
            switch(tabId) {
                case 'operation-analysis':
                    initOperationCharts();
                    break;
                case 'difference-analysis':
                    initDifferenceCharts();
                    break;
                case 'leakage-analysis':
                    initLeakageCharts();
                    break;
                default:
                    console.log(`${tabId} 图表初始化`);
                    break;
            }
        }

        // 初始化运行分析图表
        function initOperationCharts() {
            initOperationTrendChart();
            initZoneStatusChart();
        }

        // 运行趋势图表
        function initOperationTrendChart() {
            const ctx = document.getElementById('operationTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.operationTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00'],
                    datasets: [{
                        label: '供水量 (m³/h)',
                        data: [1800, 1600, 2200, 2800, 3200, 2600, 2000],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '管网压力 (MPa)',
                        data: [0.55, 0.58, 0.62, 0.65, 0.63, 0.60, 0.57],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '供水量 (m³/h)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '压力 (MPa)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // 分区状态图表
        function initZoneStatusChart() {
            const ctx = document.getElementById('zoneStatusChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.zoneStatus = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['正常运行', '需要关注', '异常状态', '维护中'],
                    datasets: [{
                        data: [22, 3, 2, 1],
                        backgroundColor: ['#4caf50', '#ff9800', '#f44336', '#9e9e9e'],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 初始化产销差图表
        function initDifferenceCharts() {
            initDifferenceTrendChart();
            initDifferenceCompositionChart();
        }

        // 产销差趋势图表
        function initDifferenceTrendChart() {
            const ctx = document.getElementById('differenceTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.differenceTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '产水量 (万m³)',
                        data: [125, 118, 128, 132, 135, 142, 148, 145, 138, 135, 130, 128],
                        borderColor: '#2196f3',
                        backgroundColor: 'rgba(33, 150, 243, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '售水量 (万m³)',
                        data: [115, 108, 118, 121, 124, 130, 135, 133, 127, 124, 119, 118],
                        borderColor: '#4caf50',
                        backgroundColor: 'rgba(76, 175, 80, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y'
                    }, {
                        label: '产销差率 (%)',
                        data: [8.0, 8.5, 7.8, 8.3, 8.1, 8.5, 8.8, 8.3, 8.0, 8.1, 8.5, 7.8],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: { display: true, text: '水量 (万m³)' }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: { display: true, text: '产销差率 (%)' },
                            grid: { drawOnChartArea: false }
                        }
                    }
                }
            });
        }

        // 产销差构成图表
        function initDifferenceCompositionChart() {
            const ctx = document.getElementById('differenceCompositionChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.differenceComposition = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['管网漏损', '计量误差', '管理损失', '工艺用水', '其他'],
                    datasets: [{
                        data: [45, 20, 15, 12, 8],
                        backgroundColor: [
                            '#f44336',
                            '#ff9800',
                            '#2196f3',
                            '#4caf50',
                            '#9c27b0'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'bottom' }
                    }
                }
            });
        }

        // 初始化漏损分析图表
        function initLeakageCharts() {
            initLeakageTrendChart();
            initZoneLeakageChart();
        }

        // 漏损趋势图表
        function initLeakageTrendChart() {
            const ctx = document.getElementById('leakageTrendChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.leakageTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
                    datasets: [{
                        label: '漏损率 (%)',
                        data: [8.5, 8.2, 7.8, 7.5, 7.2, 6.8, 7.1, 6.9, 6.5, 6.8, 7.2, 6.8],
                        borderColor: '#f44336',
                        backgroundColor: 'rgba(244, 67, 54, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: '目标值 (%)',
                        data: [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5],
                        borderColor: '#4caf50',
                        borderDash: [5, 5],
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '漏损率 (%)' }
                        }
                    }
                }
            });
        }

        // 分区漏损图表
        function initZoneLeakageChart() {
            const ctx = document.getElementById('zoneLeakageChart');
            if (!ctx || Chart.getChart(ctx)) return;

            charts.zoneLeakage = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['DMA-01', 'DMA-05', 'DMA-12', 'DMA-18', 'DMA-23'],
                    datasets: [{
                        label: '漏损率 (%)',
                        data: [3.8, 8.5, 6.2, 5.8, 4.2],
                        backgroundColor: [
                            '#4caf50',
                            '#f44336',
                            '#ff9800',
                            '#ff9800',
                            '#4caf50'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '漏损率 (%)' }
                        }
                    }
                }
            });
        }

        // 地图视图切换
        function switchMapView(view) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            const viewNames = {
                'overview': '总览视图',
                'leakage': '漏损分布',
                'pressure': '压力分布',
                'flow': '流量分布'
            };
            
            showNotification(`已切换到${viewNames[view]}`, 'info');
        }

        // 加载DMA地图
        function loadDMAMap() {
            showNotification('正在加载DMA分区地图...', 'info');

            setTimeout(() => {
                showNotification('DMA分区地图加载完成！', 'success');
            }, 2000);
        }

        // 分析周期切换函数
        function switchAnalysisPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                '24h': '24小时',
                '7d': '7天',
                '30d': '30天'
            };

            showNotification(`已切换到${periodNames[period]}分析视图`, 'info');
        }

        function switchDifferencePeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                'month': '月度',
                'quarter': '季度',
                'year': '年度'
            };

            showNotification(`已切换到${periodNames[period]}产销差分析`, 'info');
        }

        function switchLeakagePeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                'month': '月度',
                'quarter': '季度',
                'year': '年度'
            };

            showNotification(`已切换到${periodNames[period]}漏损分析`, 'info');
        }

        // 预警处置功能
        function handleWarning(warningId) {
            showNotification(`正在处置预警 ${warningId}...`, 'warning');

            setTimeout(() => {
                showNotification(`预警 ${warningId} 处置完成！`, 'success');
            }, 2000);
        }

        // 漏点确认功能
        function confirmLeakage(leakId) {
            showNotification(`正在确认漏点 ${leakId}...`, 'info');

            setTimeout(() => {
                showNotification(`漏点 ${leakId} 确认完成，已安排维修！`, 'success');
            }, 1500);
        }

        // 分区详情查看
        function viewZoneDetail(zoneId) {
            showNotification(`正在加载 ${zoneId} 分区详情...`, 'info');

            setTimeout(() => {
                showNotification(`${zoneId} 分区详情加载完成！`, 'success');
            }, 1000);
        }

        // 数据导出功能
        function exportData(type) {
            const typeNames = {
                'zone': 'DMA分区数据',
                'warning': '预警数据',
                'analysis': '运行分析数据',
                'difference': '产销差数据',
                'leakage': '漏损分析数据'
            };

            showNotification(`正在导出${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}导出成功！`, 'success');
            }, 1500);
        }

        // 生成报告功能
        function generateReport(type) {
            const typeNames = {
                'zone': 'DMA分区管理报告',
                'warning': '预警处置报告',
                'analysis': '运行分析报告',
                'difference': '产销差分析报告',
                'leakage': '漏损分析报告'
            };

            showNotification(`正在生成${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}生成完成！`, 'success');
            }, 2000);
        }

        // 实时数据刷新
        function refreshData() {
            showNotification('正在刷新实时数据...', 'info');

            setTimeout(() => {
                updateMetrics();
                showNotification('实时数据刷新完成！', 'success');
            }, 1500);
        }

        // 更新指标数据
        function updateMetrics() {
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                const currentValue = parseFloat(metric.textContent);
                if (!isNaN(currentValue)) {
                    const variation = (Math.random() - 0.5) * 0.1;
                    const newValue = currentValue * (1 + variation);

                    if (currentValue < 100) {
                        metric.textContent = newValue.toFixed(1);
                    } else {
                        metric.textContent = Math.round(newValue).toLocaleString();
                    }
                }
            });
        }

        // 启动自动刷新
        function startAutoRefresh() {
            setInterval(() => {
                updateMetrics();
            }, 30000); // 30秒刷新一次
        }

        // 页面加载完成后启动自动刷新
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                startAutoRefresh();
            }, 5000);
        });

        // 分析周期切换函数
        function switchAnalysisPeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                '24h': '24小时',
                '7d': '7天',
                '30d': '30天'
            };

            showNotification(`已切换到${periodNames[period]}分析视图`, 'info');
        }

        function switchDifferencePeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                'month': '月度',
                'quarter': '季度',
                'year': '年度'
            };

            showNotification(`已切换到${periodNames[period]}产销差分析`, 'info');
        }

        function switchLeakagePeriod(period) {
            document.querySelectorAll('.chart-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');

            const periodNames = {
                'month': '月度',
                'quarter': '季度',
                'year': '年度'
            };

            showNotification(`已切换到${periodNames[period]}漏损分析`, 'info');
        }

        // 预警处置功能
        function handleWarning(warningId) {
            showNotification(`正在处置预警 ${warningId}...`, 'warning');

            setTimeout(() => {
                showNotification(`预警 ${warningId} 处置完成！`, 'success');
            }, 2000);
        }

        // 漏点确认功能
        function confirmLeakage(leakId) {
            showNotification(`正在确认漏点 ${leakId}...`, 'info');

            setTimeout(() => {
                showNotification(`漏点 ${leakId} 确认完成，已安排维修！`, 'success');
            }, 1500);
        }

        // 分区详情查看
        function viewZoneDetail(zoneId) {
            showNotification(`正在加载 ${zoneId} 分区详情...`, 'info');

            setTimeout(() => {
                showNotification(`${zoneId} 分区详情加载完成！`, 'success');
            }, 1000);
        }

        // 数据导出功能
        function exportData(type) {
            const typeNames = {
                'zone': 'DMA分区数据',
                'warning': '预警数据',
                'analysis': '运行分析数据',
                'difference': '产销差数据',
                'leakage': '漏损分析数据'
            };

            showNotification(`正在导出${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}导出成功！`, 'success');
            }, 1500);
        }

        // 生成报告功能
        function generateReport(type) {
            const typeNames = {
                'zone': 'DMA分区管理报告',
                'warning': '预警处置报告',
                'analysis': '运行分析报告',
                'difference': '产销差分析报告',
                'leakage': '漏损分析报告'
            };

            showNotification(`正在生成${typeNames[type]}...`, 'info');

            setTimeout(() => {
                showNotification(`${typeNames[type]}生成完成！`, 'success');
            }, 2000);
        }

        // 系统配置功能
        function configureSystem(module) {
            const moduleNames = {
                'zone': 'DMA分区管理',
                'warning': '预警处置',
                'analysis': '运行分析',
                'difference': '产销差分析',
                'leakage': '漏损分析'
            };

            showNotification(`正在打开${moduleNames[module]}配置...`, 'info');

            setTimeout(() => {
                showNotification(`${moduleNames[module]}配置页面已打开！`, 'success');
            }, 1000);
        }

        // 实时数据刷新
        function refreshData() {
            showNotification('正在刷新实时数据...', 'info');

            // 模拟数据更新
            setTimeout(() => {
                updateMetrics();
                showNotification('实时数据刷新完成！', 'success');
            }, 1500);
        }

        // 更新指标数据
        function updateMetrics() {
            const metrics = document.querySelectorAll('.metric-value');
            metrics.forEach(metric => {
                const currentValue = parseFloat(metric.textContent);
                if (!isNaN(currentValue)) {
                    const variation = (Math.random() - 0.5) * 0.1;
                    const newValue = currentValue * (1 + variation);

                    if (currentValue < 100) {
                        metric.textContent = newValue.toFixed(1);
                    } else {
                        metric.textContent = Math.round(newValue).toLocaleString();
                    }
                }
            });
        }

        // 启动自动刷新
        function startAutoRefresh() {
            setInterval(() => {
                updateMetrics();
            }, 30000); // 30秒刷新一次
        }

        // 页面加载完成后启动自动刷新
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                startAutoRefresh();
            }, 5000);
        });

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 16px 22px;
                border-radius: 12px;
                z-index: 10000;
                box-shadow: 0 10px 30px rgba(0,0,0,0.15);
                max-width: 380px;
                animation: slideInRight 0.4s ease-out;
                font-weight: 600;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 10px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.4s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 400);
            }, 3500);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#4caf50',
                'error': '#f44336',
                'warning': '#ff9800',
                'info': '#2196f3'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
