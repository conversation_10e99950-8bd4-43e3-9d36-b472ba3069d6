<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交接班管理系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 头部样式 */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .main-title {
            font-size: 2.5rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
        }

        .subtitle {
            color: #7f8c8d;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .feature-tags {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .feature-tag {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 导航标签 */
        .nav-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 10px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            gap: 8px;
        }

        .nav-tab {
            flex: 1;
            padding: 18px 25px;
            background: transparent;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            font-size: 1.1rem;
            font-weight: 600;
            color: #7f8c8d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
        }

        .nav-tab.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
            transform: translateY(-2px);
        }

        .nav-tab:hover:not(.active) {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            transform: translateY(-1px);
        }

        /* 内容区域 */
        .content-area {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            min-height: 700px;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .stat-content {
            position: relative;
            z-index: 1;
        }

        .stat-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            opacity: 0.8;
        }

        .stat-number {
            font-size: 2.2rem;
            font-weight: bold;
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.9;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-label {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .form-label .required {
            color: #e74c3c;
        }

        .form-input,
        .form-select,
        .form-textarea {
            padding: 12px 15px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-input:focus,
        .form-select:focus,
        .form-textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }

        /* 按钮样式 */
        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #fd7e14);
            color: white;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .data-table th,
        .data-table td {
            padding: 15px 12px;
            text-align: left;
            border-bottom: 1px solid #ecf0f1;
        }

        .data-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        /* 状态标签 */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-rejected {
            background: #f8d7da;
            color: #721c24;
        }

        .status-morning {
            background: #e3f2fd;
            color: #1565c0;
        }

        .status-afternoon {
            background: #fff3e0;
            color: #ef6c00;
        }

        .status-night {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        /* 卡片样式 */
        .card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid #e9ecef;
        }

        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 700;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 日志条目样式 */
        .log-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .log-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .log-meta {
            display: flex;
            gap: 15px;
            font-size: 0.9rem;
            color: #6c757d;
        }

        .log-content {
            line-height: 1.6;
            color: #495057;
        }

        /* 图表容器 */
        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin-top: 30px;
        }

        /* 排班表格 */
        .schedule-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .schedule-table th,
        .schedule-table td {
            padding: 12px 10px;
            text-align: center;
            border: 1px solid #dee2e6;
            font-size: 0.9rem;
        }

        .schedule-table th {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-weight: 600;
        }

        .schedule-cell {
            min-height: 60px;
            vertical-align: top;
            position: relative;
        }

        .shift-item {
            background: rgba(102, 126, 234, 0.1);
            border-radius: 4px;
            padding: 4px 6px;
            margin: 2px 0;
            font-size: 0.8rem;
            border-left: 3px solid #667eea;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }

            .main-title {
                font-size: 2rem;
                flex-direction: column;
                gap: 10px;
            }

            .nav-tabs {
                flex-direction: column;
            }

            .nav-tab {
                padding: 15px 20px;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }

            .chart-grid {
                grid-template-columns: 1fr;
            }

            .btn-group {
                flex-direction: column;
            }

            .data-table {
                font-size: 0.9rem;
            }

            .data-table th,
            .data-table td {
                padding: 10px 8px;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-in {
            animation: slideIn 0.6s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header fade-in">
            <h1 class="main-title">
                <i class="fas fa-exchange-alt"></i>
                交接班管理系统
                <i class="fas fa-users"></i>
            </h1>
            <p class="subtitle">Shift Handover Management System</p>
            <div class="feature-tags">
                <div class="feature-tag">
                    <i class="fas fa-clipboard-list"></i>
                    交接班日志
                </div>
                <div class="feature-tag">
                    <i class="fas fa-chart-bar"></i>
                    值班统计
                </div>
                <div class="feature-tag">
                    <i class="fas fa-calendar-alt"></i>
                    排班管理
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="nav-tabs slide-in">
            <button class="nav-tab active" onclick="showTab('handover-log')">
                <i class="fas fa-clipboard-list"></i>
                交接班日志
            </button>
            <button class="nav-tab" onclick="showTab('duty-statistics')">
                <i class="fas fa-chart-bar"></i>
                值班统计
            </button>
            <button class="nav-tab" onclick="showTab('schedule-management')">
                <i class="fas fa-calendar-alt"></i>
                排班管理
            </button>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 交接班日志标签页 -->
            <div id="handover-log" class="tab-content active">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-clipboard-list"></i>
                    交接班日志管理
                </h2>

                <!-- 统计概览 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="stat-number">156</div>
                            <div class="stat-label">本月日志总数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number">142</div>
                            <div class="stat-label">已审核</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">14</div>
                            <div class="stat-label">待审核</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-number">91%</div>
                            <div class="stat-label">及时率</div>
                        </div>
                    </div>
                </div>

                <!-- 新增日志表单 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle"></i>
                            新增交接班日志
                        </h3>
                    </div>

                    <form id="handoverForm">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-calendar-day"></i>
                                    交接班日期 <span class="required">*</span>
                                </label>
                                <input type="date" class="form-input" id="handoverDate" required>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-clock"></i>
                                    班次 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="shiftType" required>
                                    <option value="">请选择班次</option>
                                    <option value="morning">早班 (08:00-16:00)</option>
                                    <option value="afternoon">中班 (16:00-24:00)</option>
                                    <option value="night">夜班 (00:00-08:00)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user"></i>
                                    交班人员 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="handoverPerson" required>
                                    <option value="">请选择交班人员</option>
                                    <option value="张三">张三</option>
                                    <option value="李四">李四</option>
                                    <option value="王五">王五</option>
                                    <option value="赵六">赵六</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user-check"></i>
                                    接班人员 <span class="required">*</span>
                                </label>
                                <select class="form-select" id="receivePerson" required>
                                    <option value="">请选择接班人员</option>
                                    <option value="张三">张三</option>
                                    <option value="李四">李四</option>
                                    <option value="王五">王五</option>
                                    <option value="赵六">赵六</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tasks"></i>
                                工作情况 <span class="required">*</span>
                            </label>
                            <textarea class="form-textarea" id="workSituation" required placeholder="请详细描述本班次的工作完成情况、设备运行状态等..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-exclamation-triangle"></i>
                                存在问题
                            </label>
                            <textarea class="form-textarea" id="problems" placeholder="请描述本班次发现的问题、异常情况等..."></textarea>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-lightbulb"></i>
                                注意事项
                            </label>
                            <textarea class="form-textarea" id="notes" placeholder="请填写需要下一班次特别注意的事项、待处理工作等..."></textarea>
                        </div>

                        <div class="btn-group">
                            <button type="button" class="btn btn-primary" onclick="saveHandoverLog()">
                                <i class="fas fa-save"></i>
                                保存日志
                            </button>
                            <button type="button" class="btn btn-success" onclick="submitHandoverLog()">
                                <i class="fas fa-paper-plane"></i>
                                提交审核
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i>
                                重置表单
                            </button>
                        </div>
                    </form>
                </div>

                <!-- 日志列表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-list"></i>
                            交接班日志列表
                        </h3>
                        <div style="display: flex; gap: 10px;">
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部班次</option>
                                <option value="morning">早班</option>
                                <option value="afternoon">中班</option>
                                <option value="night">夜班</option>
                            </select>
                            <select style="padding: 8px 12px; border: 1px solid #ddd; border-radius: 6px;">
                                <option value="">全部状态</option>
                                <option value="pending">待审核</option>
                                <option value="approved">已审核</option>
                                <option value="rejected">已拒绝</option>
                            </select>
                        </div>
                    </div>

                    <div id="logList">
                        <div class="log-item">
                            <div class="log-header">
                                <div>
                                    <strong>2024-01-15 中班交接</strong>
                                    <span class="status-badge status-afternoon">中班</span>
                                    <span class="status-badge status-approved">已审核</span>
                                </div>
                                <div class="log-meta">
                                    <span><i class="fas fa-user"></i> 交班：张三</span>
                                    <span><i class="fas fa-user-check"></i> 接班：李四</span>
                                    <span><i class="fas fa-clock"></i> 16:00</span>
                                </div>
                            </div>
                            <div class="log-content">
                                <p><strong>工作情况：</strong>设备运行正常，完成了污水处理量1200吨，出水水质达标。进行了2号泵的例行维护。</p>
                                <p><strong>存在问题：</strong>3号沉淀池出现轻微泡沫，已调整加药量。</p>
                                <p><strong>注意事项：</strong>夜班需要关注3号池情况，如有异常及时联系技术人员。</p>
                            </div>
                        </div>

                        <div class="log-item">
                            <div class="log-header">
                                <div>
                                    <strong>2024-01-15 早班交接</strong>
                                    <span class="status-badge status-morning">早班</span>
                                    <span class="status-badge status-pending">待审核</span>
                                </div>
                                <div class="log-meta">
                                    <span><i class="fas fa-user"></i> 交班：王五</span>
                                    <span><i class="fas fa-user-check"></i> 接班：张三</span>
                                    <span><i class="fas fa-clock"></i> 08:00</span>
                                </div>
                            </div>
                            <div class="log-content">
                                <p><strong>工作情况：</strong>夜班期间设备运行稳定，处理污水800吨，各项指标正常。完成了日常巡检。</p>
                                <p><strong>存在问题：</strong>1号风机有轻微异响，建议安排检修。</p>
                                <p><strong>注意事项：</strong>今日有供应商来访，需要配合检查设备。</p>
                            </div>
                        </div>

                        <div class="log-item">
                            <div class="log-header">
                                <div>
                                    <strong>2024-01-14 夜班交接</strong>
                                    <span class="status-badge status-night">夜班</span>
                                    <span class="status-badge status-approved">已审核</span>
                                </div>
                                <div class="log-meta">
                                    <span><i class="fas fa-user"></i> 交班：赵六</span>
                                    <span><i class="fas fa-user-check"></i> 接班：王五</span>
                                    <span><i class="fas fa-clock"></i> 00:00</span>
                                </div>
                            </div>
                            <div class="log-content">
                                <p><strong>工作情况：</strong>中班期间处理污水1100吨，完成了加药系统的校准工作。</p>
                                <p><strong>存在问题：</strong>无异常情况。</p>
                                <p><strong>注意事项：</strong>明日上午有环保检查，请做好准备工作。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 值班统计标签页 -->
            <div id="duty-statistics" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-chart-bar"></i>
                    值班统计与导出
                </h2>

                <!-- 统计筛选 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-filter"></i>
                            统计筛选条件
                        </h3>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">统计维度</label>
                            <select class="form-select" id="statisticsDimension">
                                <option value="time">按时间段</option>
                                <option value="team">按班组</option>
                                <option value="member">按成员</option>
                                <option value="shift">按班次</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">开始日期</label>
                            <input type="date" class="form-input" id="statsStartDate">
                        </div>
                        <div class="form-group">
                            <label class="form-label">结束日期</label>
                            <input type="date" class="form-input" id="statsEndDate">
                        </div>
                        <div class="form-group">
                            <label class="form-label">班组选择</label>
                            <select class="form-select" id="teamFilter">
                                <option value="">全部班组</option>
                                <option value="A">A班组</option>
                                <option value="B">B班组</option>
                                <option value="C">C班组</option>
                            </select>
                        </div>
                    </div>
                    <div class="btn-group">
                        <button class="btn btn-primary" onclick="generateStatistics()">
                            <i class="fas fa-chart-line"></i>
                            生成统计
                        </button>
                        <button class="btn btn-success" onclick="exportToExcel()">
                            <i class="fas fa-file-excel"></i>
                            导出Excel
                        </button>
                        <button class="btn btn-warning" onclick="exportToPDF()">
                            <i class="fas fa-file-pdf"></i>
                            导出PDF
                        </button>
                    </div>
                </div>

                <!-- 统计图表 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-chart-area"></i>
                            值班统计图表
                        </h3>
                    </div>
                    <div class="chart-grid">
                        <div>
                            <h4 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">班次分布统计</h4>
                            <div class="chart-container">
                                <canvas id="shiftDistributionChart"></canvas>
                            </div>
                        </div>
                        <div>
                            <h4 style="text-align: center; margin-bottom: 15px; color: #2c3e50;">人员工作量统计</h4>
                            <div class="chart-container">
                                <canvas id="workloadChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计表格 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-table"></i>
                            详细统计数据
                        </h3>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="data-table" id="statisticsTable">
                            <thead>
                                <tr>
                                    <th>人员姓名</th>
                                    <th>班组</th>
                                    <th>早班次数</th>
                                    <th>中班次数</th>
                                    <th>夜班次数</th>
                                    <th>总值班次数</th>
                                    <th>日志完成率</th>
                                    <th>工作量评分</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>张三</td>
                                    <td>A班组</td>
                                    <td>8</td>
                                    <td>6</td>
                                    <td>4</td>
                                    <td>18</td>
                                    <td>95%</td>
                                    <td>优秀</td>
                                </tr>
                                <tr>
                                    <td>李四</td>
                                    <td>A班组</td>
                                    <td>7</td>
                                    <td>8</td>
                                    <td>5</td>
                                    <td>20</td>
                                    <td>90%</td>
                                    <td>良好</td>
                                </tr>
                                <tr>
                                    <td>王五</td>
                                    <td>B班组</td>
                                    <td>6</td>
                                    <td>7</td>
                                    <td>6</td>
                                    <td>19</td>
                                    <td>88%</td>
                                    <td>良好</td>
                                </tr>
                                <tr>
                                    <td>赵六</td>
                                    <td>B班组</td>
                                    <td>9</td>
                                    <td>5</td>
                                    <td>3</td>
                                    <td>17</td>
                                    <td>92%</td>
                                    <td>优秀</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 统计汇总 -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number">12</div>
                            <div class="stat-label">值班人员总数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-number">234</div>
                            <div class="stat-label">本月总值班次数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number">19.5</div>
                            <div class="stat-label">人均值班次数</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-content">
                            <div class="stat-icon">
                                <i class="fas fa-percentage"></i>
                            </div>
                            <div class="stat-number">91%</div>
                            <div class="stat-label">日志完成率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 排班管理标签页 -->
            <div id="schedule-management" class="tab-content">
                <h2 style="color: #2c3e50; margin-bottom: 25px; display: flex; align-items: center; gap: 12px;">
                    <i class="fas fa-calendar-alt"></i>
                    排班管理
                </h2>

                <!-- 排班操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-plus-circle"></i>
                            排班计划制定
                        </h3>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">排班月份</label>
                            <input type="month" class="form-input" id="scheduleMonth">
                        </div>
                        <div class="form-group">
                            <label class="form-label">班组选择</label>
                            <select class="form-select" id="scheduleTeam">
                                <option value="">选择班组</option>
                                <option value="A">A班组</option>
                                <option value="B">B班组</option>
                                <option value="C">C班组</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">排班模式</label>
                            <select class="form-select" id="scheduleMode">
                                <option value="rotation">轮班制</option>
                                <option value="fixed">固定班次</option>
                                <option value="flexible">灵活排班</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">操作</label>
                            <div style="display: flex; gap: 10px;">
                                <button class="btn btn-primary" onclick="generateSchedule()">
                                    <i class="fas fa-magic"></i>
                                    自动排班
                                </button>
                                <button class="btn btn-success" onclick="publishSchedule()">
                                    <i class="fas fa-bullhorn"></i>
                                    发布排班
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 排班日历 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-calendar"></i>
                            2024年1月排班表
                        </h3>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-secondary" onclick="prevMonth()">
                                <i class="fas fa-chevron-left"></i>
                                上月
                            </button>
                            <button class="btn btn-secondary" onclick="nextMonth()">
                                <i class="fas fa-chevron-right"></i>
                                下月
                            </button>
                        </div>
                    </div>
                    <div style="overflow-x: auto;">
                        <table class="schedule-table">
                            <thead>
                                <tr>
                                    <th>日期</th>
                                    <th>星期一</th>
                                    <th>星期二</th>
                                    <th>星期三</th>
                                    <th>星期四</th>
                                    <th>星期五</th>
                                    <th>星期六</th>
                                    <th>星期日</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td style="background: #f8f9fa; font-weight: bold;">第1周</td>
                                    <td class="schedule-cell">
                                        <div>1</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>2</div>
                                        <div class="shift-item">早班: 赵六</div>
                                        <div class="shift-item">中班: 张三</div>
                                        <div class="shift-item">夜班: 李四</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>3</div>
                                        <div class="shift-item">早班: 王五</div>
                                        <div class="shift-item">中班: 赵六</div>
                                        <div class="shift-item">夜班: 张三</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>4</div>
                                        <div class="shift-item">早班: 李四</div>
                                        <div class="shift-item">中班: 王五</div>
                                        <div class="shift-item">夜班: 赵六</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>5</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>6</div>
                                        <div class="shift-item">早班: 赵六</div>
                                        <div class="shift-item">中班: 张三</div>
                                        <div class="shift-item">夜班: 李四</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>7</div>
                                        <div class="shift-item">早班: 王五</div>
                                        <div class="shift-item">中班: 赵六</div>
                                        <div class="shift-item">夜班: 张三</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="background: #f8f9fa; font-weight: bold;">第2周</td>
                                    <td class="schedule-cell">
                                        <div>8</div>
                                        <div class="shift-item">早班: 李四</div>
                                        <div class="shift-item">中班: 王五</div>
                                        <div class="shift-item">夜班: 赵六</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>9</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>10</div>
                                        <div class="shift-item">早班: 赵六</div>
                                        <div class="shift-item">中班: 张三</div>
                                        <div class="shift-item">夜班: 李四</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>11</div>
                                        <div class="shift-item">早班: 王五</div>
                                        <div class="shift-item">中班: 赵六</div>
                                        <div class="shift-item">夜班: 张三</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>12</div>
                                        <div class="shift-item">早班: 李四</div>
                                        <div class="shift-item">中班: 王五</div>
                                        <div class="shift-item">夜班: 赵六</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>13</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>14</div>
                                        <div class="shift-item">早班: 赵六</div>
                                        <div class="shift-item">中班: 张三</div>
                                        <div class="shift-item">夜班: 李四</div>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="background: #f8f9fa; font-weight: bold;">第3周</td>
                                    <td class="schedule-cell">
                                        <div>15</div>
                                        <div class="shift-item">早班: 王五</div>
                                        <div class="shift-item">中班: 赵六</div>
                                        <div class="shift-item">夜班: 张三</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>16</div>
                                        <div class="shift-item">早班: 李四</div>
                                        <div class="shift-item">中班: 王五</div>
                                        <div class="shift-item">夜班: 赵六</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>17</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>18</div>
                                        <div class="shift-item">早班: 赵六</div>
                                        <div class="shift-item">中班: 张三</div>
                                        <div class="shift-item">夜班: 李四</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>19</div>
                                        <div class="shift-item">早班: 王五</div>
                                        <div class="shift-item">中班: 赵六</div>
                                        <div class="shift-item">夜班: 张三</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>20</div>
                                        <div class="shift-item">早班: 李四</div>
                                        <div class="shift-item">中班: 王五</div>
                                        <div class="shift-item">夜班: 赵六</div>
                                    </td>
                                    <td class="schedule-cell">
                                        <div>21</div>
                                        <div class="shift-item">早班: 张三</div>
                                        <div class="shift-item">中班: 李四</div>
                                        <div class="shift-item">夜班: 王五</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- 个人排班查询 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user-clock"></i>
                            个人排班查询
                        </h3>
                    </div>
                    <div class="form-grid">
                        <div class="form-group">
                            <label class="form-label">选择人员</label>
                            <select class="form-select" id="personalScheduleQuery">
                                <option value="">请选择人员</option>
                                <option value="张三">张三</option>
                                <option value="李四">李四</option>
                                <option value="王五">王五</option>
                                <option value="赵六">赵六</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">查询月份</label>
                            <input type="month" class="form-input" id="queryMonth">
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="queryPersonalSchedule()">
                                <i class="fas fa-search"></i>
                                查询排班
                            </button>
                        </div>
                    </div>

                    <div id="personalScheduleResult" style="margin-top: 20px; display: none;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">张三 - 2024年1月排班情况</h4>
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>班次</th>
                                        <th>时间段</th>
                                        <th>状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>2024-01-01</td>
                                        <td><span class="status-badge status-morning">早班</span></td>
                                        <td>08:00-16:00</td>
                                        <td>已完成</td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-02</td>
                                        <td><span class="status-badge status-afternoon">中班</span></td>
                                        <td>16:00-24:00</td>
                                        <td>已完成</td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-03</td>
                                        <td><span class="status-badge status-night">夜班</span></td>
                                        <td>00:00-08:00</td>
                                        <td>已完成</td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-15</td>
                                        <td><span class="status-badge status-morning">早班</span></td>
                                        <td>08:00-16:00</td>
                                        <td>进行中</td>
                                    </tr>
                                    <tr>
                                        <td>2024-01-16</td>
                                        <td><span class="status-badge status-afternoon">中班</span></td>
                                        <td>16:00-24:00</td>
                                        <td>待执行</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 设置当前日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('handoverDate').value = today;

            // 添加卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.15)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.08)';
                });
            });
        });

        // 标签页切换功能
        function showTab(tabId) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // 移除所有标签的激活状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // 显示选中的标签页内容
            const targetTab = document.getElementById(tabId);
            if (targetTab) {
                targetTab.classList.add('active');
            }

            // 激活选中的标签
            event.target.classList.add('active');

            // 根据标签页加载相应内容
            if (tabId === 'duty-statistics') {
                loadDutyStatistics();
            } else if (tabId === 'schedule-management') {
                loadScheduleManagement();
            }
        }

        // 保存交接班日志
        function saveHandoverLog() {
            const formData = collectHandoverData();
            if (validateHandoverData(formData)) {
                // 模拟保存到本地存储
                const savedLogs = JSON.parse(localStorage.getItem('handoverLogs') || '[]');
                formData.id = Date.now();
                formData.status = 'draft';
                formData.createTime = new Date().toLocaleString();
                savedLogs.push(formData);
                localStorage.setItem('handoverLogs', JSON.stringify(savedLogs));
                
                showNotification('交接班日志保存成功！', 'success');
            }
        }

        // 提交交接班日志
        function submitHandoverLog() {
            const formData = collectHandoverData();
            if (validateHandoverData(formData)) {
                const savedLogs = JSON.parse(localStorage.getItem('handoverLogs') || '[]');
                formData.id = Date.now();
                formData.status = 'pending';
                formData.createTime = new Date().toLocaleString();
                formData.submitTime = new Date().toLocaleString();
                savedLogs.push(formData);
                localStorage.setItem('handoverLogs', JSON.stringify(savedLogs));
                
                showNotification('交接班日志已提交审核！', 'success');
                document.getElementById('handoverForm').reset();
                document.getElementById('handoverDate').value = new Date().toISOString().split('T')[0];
            }
        }

        // 收集表单数据
        function collectHandoverData() {
            return {
                handoverDate: document.getElementById('handoverDate').value,
                shiftType: document.getElementById('shiftType').value,
                handoverPerson: document.getElementById('handoverPerson').value,
                receivePerson: document.getElementById('receivePerson').value,
                workSituation: document.getElementById('workSituation').value,
                problems: document.getElementById('problems').value,
                notes: document.getElementById('notes').value
            };
        }

        // 验证表单数据
        function validateHandoverData(data) {
            if (!data.handoverDate || !data.shiftType || !data.handoverPerson || !data.receivePerson || !data.workSituation) {
                showNotification('请填写必填项！', 'error');
                return false;
            }
            if (data.handoverPerson === data.receivePerson) {
                showNotification('交班人员和接班人员不能相同！', 'error');
                return false;
            }
            return true;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${getNotificationColor(type)};
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                z-index: 10000;
                animation: slideInRight 0.3s ease-out;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                max-width: 400px;
            `;
            
            const icon = getNotificationIcon(type);
            notification.innerHTML = `<i class="${icon}" style="margin-right: 8px;"></i>${message}`;
            
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease-out';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // 获取通知颜色
        function getNotificationColor(type) {
            const colors = {
                'success': '#28a745',
                'error': '#dc3545',
                'warning': '#ffc107',
                'info': '#17a2b8'
            };
            return colors[type] || colors.info;
        }

        // 获取通知图标
        function getNotificationIcon(type) {
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };
            return icons[type] || icons.info;
        }

        // 加载值班统计功能
        function loadDutyStatistics() {
            // 设置默认日期范围
            const endDate = new Date();
            const startDate = new Date();
            startDate.setMonth(startDate.getMonth() - 1);

            document.getElementById('statsStartDate').value = startDate.toISOString().split('T')[0];
            document.getElementById('statsEndDate').value = endDate.toISOString().split('T')[0];

            // 初始化图表
            initializeStatisticsCharts();
        }

        // 加载排班管理功能
        function loadScheduleManagement() {
            // 设置当前月份
            const currentMonth = new Date().toISOString().slice(0, 7);
            document.getElementById('scheduleMonth').value = currentMonth;
            document.getElementById('queryMonth').value = currentMonth;
        }

        // 初始化统计图表
        function initializeStatisticsCharts() {
            // 班次分布饼图
            const shiftCtx = document.getElementById('shiftDistributionChart');
            if (shiftCtx) {
                new Chart(shiftCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['早班', '中班', '夜班'],
                        datasets: [{
                            data: [78, 82, 74],
                            backgroundColor: [
                                '#667eea',
                                '#764ba2',
                                '#f093fb'
                            ],
                            borderWidth: 2,
                            borderColor: '#fff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom'
                            }
                        }
                    }
                });
            }

            // 人员工作量柱状图
            const workloadCtx = document.getElementById('workloadChart');
            if (workloadCtx) {
                new Chart(workloadCtx, {
                    type: 'bar',
                    data: {
                        labels: ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
                        datasets: [{
                            label: '值班次数',
                            data: [18, 20, 19, 17, 16, 15],
                            backgroundColor: 'rgba(102, 126, 234, 0.8)',
                            borderColor: '#667eea',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        scales: {
                            y: {
                                beginAtZero: true,
                                title: {
                                    display: true,
                                    text: '值班次数'
                                }
                            }
                        },
                        plugins: {
                            legend: {
                                display: false
                            }
                        }
                    }
                });
            }
        }

        // 生成统计报表
        function generateStatistics() {
            const dimension = document.getElementById('statisticsDimension').value;
            const startDate = document.getElementById('statsStartDate').value;
            const endDate = document.getElementById('statsEndDate').value;
            const team = document.getElementById('teamFilter').value;

            if (!startDate || !endDate) {
                showNotification('请选择统计时间范围！', 'error');
                return;
            }

            showNotification('正在生成统计报表...', 'info');

            setTimeout(() => {
                showNotification('统计报表生成完成！', 'success');
                // 这里可以更新表格数据和图表
                updateStatisticsDisplay(dimension, startDate, endDate, team);
            }, 2000);
        }

        // 更新统计显示
        function updateStatisticsDisplay(dimension, startDate, endDate, team) {
            console.log(`更新统计显示: ${dimension}, ${startDate} - ${endDate}, 班组: ${team}`);
            // 实际应用中这里会根据参数更新图表和表格数据
        }

        // 导出Excel
        function exportToExcel() {
            showNotification('正在生成Excel文件...', 'info');

            setTimeout(() => {
                // 模拟Excel导出
                const csvContent = generateCSVContent();
                downloadFile(csvContent, 'duty-statistics.csv', 'text/csv');
                showNotification('Excel文件导出成功！', 'success');
            }, 2000);
        }

        // 导出PDF
        function exportToPDF() {
            showNotification('正在生成PDF文件...', 'info');

            setTimeout(() => {
                // 模拟PDF生成
                const pdfWindow = window.open('', '_blank', 'width=900,height=700');
                pdfWindow.document.write(`
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>值班统计报告</title>
                        <style>
                            body { font-family: Arial, sans-serif; padding: 20px; }
                            .header { text-align: center; border-bottom: 2px solid #667eea; padding-bottom: 20px; margin-bottom: 30px; }
                            .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 30px 0; }
                            .stat-item { background: #f8f9fa; padding: 20px; border-radius: 10px; text-align: center; border-left: 4px solid #667eea; }
                            .stat-number { font-size: 2rem; font-weight: bold; color: #667eea; }
                            .stat-label { color: #666; margin-top: 5px; }
                            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
                            th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
                            th { background: #667eea; color: white; }
                        </style>
                    </head>
                    <body>
                        <div class="header">
                            <h1>值班统计报告</h1>
                            <p>统计时间：${new Date().toLocaleDateString()} - ${new Date().toLocaleDateString()}</p>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number">12</div>
                                <div class="stat-label">值班人员总数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">234</div>
                                <div class="stat-label">总值班次数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">19.5</div>
                                <div class="stat-label">人均值班次数</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">91%</div>
                                <div class="stat-label">日志完成率</div>
                            </div>
                        </div>

                        <table>
                            <thead>
                                <tr>
                                    <th>人员姓名</th>
                                    <th>班组</th>
                                    <th>早班次数</th>
                                    <th>中班次数</th>
                                    <th>夜班次数</th>
                                    <th>总值班次数</th>
                                    <th>日志完成率</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>张三</td><td>A班组</td><td>8</td><td>6</td><td>4</td><td>18</td><td>95%</td></tr>
                                <tr><td>李四</td><td>A班组</td><td>7</td><td>8</td><td>5</td><td>20</td><td>90%</td></tr>
                                <tr><td>王五</td><td>B班组</td><td>6</td><td>7</td><td>6</td><td>19</td><td>88%</td></tr>
                                <tr><td>赵六</td><td>B班组</td><td>9</td><td>5</td><td>3</td><td>17</td><td>92%</td></tr>
                            </tbody>
                        </table>
                    </body>
                    </html>
                `);
                pdfWindow.document.close();
                showNotification('PDF报告生成完成！', 'success');
            }, 2500);
        }

        // 生成CSV内容
        function generateCSVContent() {
            const headers = ['人员姓名', '班组', '早班次数', '中班次数', '夜班次数', '总值班次数', '日志完成率'];
            const data = [
                ['张三', 'A班组', '8', '6', '4', '18', '95%'],
                ['李四', 'A班组', '7', '8', '5', '20', '90%'],
                ['王五', 'B班组', '6', '7', '6', '19', '88%'],
                ['赵六', 'B班组', '9', '5', '3', '17', '92%']
            ];

            let csvContent = headers.join(',') + '\n';
            data.forEach(row => {
                csvContent += row.join(',') + '\n';
            });

            return csvContent;
        }

        // 下载文件
        function downloadFile(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 自动排班
        function generateSchedule() {
            const month = document.getElementById('scheduleMonth').value;
            const team = document.getElementById('scheduleTeam').value;
            const mode = document.getElementById('scheduleMode').value;

            if (!month || !team) {
                showNotification('请选择排班月份和班组！', 'error');
                return;
            }

            showNotification(`正在为${team}班组生成${month}的${getScheduleModeName(mode)}排班...`, 'info');

            setTimeout(() => {
                showNotification('排班计划生成完成！', 'success');
                // 这里可以更新排班表格
            }, 2000);
        }

        // 发布排班
        function publishSchedule() {
            if (confirm('确定要发布当前排班计划吗？发布后相关人员将收到通知。')) {
                showNotification('正在发布排班计划...', 'info');

                setTimeout(() => {
                    showNotification('排班计划已发布，相关人员已收到通知！', 'success');
                }, 1500);
            }
        }

        // 上一月
        function prevMonth() {
            showNotification('切换到上一月排班表', 'info');
        }

        // 下一月
        function nextMonth() {
            showNotification('切换到下一月排班表', 'info');
        }

        // 查询个人排班
        function queryPersonalSchedule() {
            const person = document.getElementById('personalScheduleQuery').value;
            const month = document.getElementById('queryMonth').value;

            if (!person || !month) {
                showNotification('请选择人员和查询月份！', 'error');
                return;
            }

            showNotification(`正在查询${person}在${month}的排班情况...`, 'info');

            setTimeout(() => {
                document.getElementById('personalScheduleResult').style.display = 'block';
                showNotification('个人排班查询完成！', 'success');
            }, 1000);
        }

        // 获取排班模式名称
        function getScheduleModeName(mode) {
            const modeNames = {
                'rotation': '轮班制',
                'fixed': '固定班次',
                'flexible': '灵活排班'
            };
            return modeNames[mode] || mode;
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            
            @keyframes slideOutRight {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
